{"name": "sinfin-platform", "version": "1.0.0", "private": true, "main": "index.js", "type": "module", "scripts": {"start": "vite", "build": "vite build", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "prettier:all": "prettier . --write", "test-storybook": "test-storybook --testTimeout=60000", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.11.5", "@geoman-io/leaflet-geoman-free": "^2.17.0", "@mui/icons-material": "^6.4.9", "@mui/material": "^6.4.9", "@mui/x-charts": "^7.5.0", "@mui/x-data-grid": "^7.24.1", "@mui/x-data-grid-pro": "^7.28.1", "@mui/x-date-pickers-pro": "^7.27.3", "@mui/x-tree-view": "^7.6.2", "@photo-sphere-viewer/core": "^5.11.1", "@photo-sphere-viewer/markers-plugin": "^5.11.1", "@photo-sphere-viewer/virtual-tour-plugin": "^5.11.1", "@playwright/test": "^1.51.1", "@tanstack/react-virtual": "^3.13.8", "@tiptap/core": "^2.5.7", "@tiptap/extension-color": "^2.5.8", "@tiptap/extension-hard-break": "^2.8.0", "@tiptap/extension-image": "^2.5.1", "@tiptap/extension-link": "^2.5.1", "@tiptap/extension-text-align": "^2.5.1", "@tiptap/extension-text-style": "^2.5.8", "@tiptap/extension-underline": "^2.5.1", "@tiptap/pm": "^2.5.1", "@tiptap/react": "^2.5.1", "@tiptap/starter-kit": "^2.5.1", "@vitejs/plugin-react-swc": "^3.6.0", "dayjs": "^1.11.13", "dompurify": "^3.1.6", "embla-carousel-react": "^8.1.6", "i18next": "^23.11.4", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.5.2", "leaflet": "^1.9.4", "react": "18.3.1", "react-color": "^2.19.3", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-dom": "18.3.1", "react-dropzone": "^14.2.3", "react-error-boundary": "^4.0.13", "react-i18next": "^14.1.1", "react-leaflet": "^4.2.1", "react-photo-sphere-viewer": "^6.0.0", "react-router": "^7.5.2", "uuid": "^11.0.5", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.4", "@eslint/js": "^9.3.0", "@storybook/addon-essentials": "^8.6.9", "@storybook/addon-interactions": "^8.6.9", "@storybook/addon-onboarding": "^8.6.9", "@storybook/blocks": "^8.6.9", "@storybook/react": "^8.6.9", "@storybook/react-vite": "^8.6.9", "@storybook/test": "^8.6.9", "@storybook/test-runner": "^0.21.3", "@types/node": "^20.12.12", "@vitejs/plugin-react": "^4.2.1", "eslint": "^9.3.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-storybook": "^0.11.3", "globals": "^15.2.0", "msw": "^2.7.3", "msw-storybook-addon": "^2.0.4", "npm-check-updates": "^17.0.1", "prettier": "3.2.5", "prop-types": "^15.8.1", "storybook": "^8.6.9"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "msw": {"workerDirectory": ["public"]}}