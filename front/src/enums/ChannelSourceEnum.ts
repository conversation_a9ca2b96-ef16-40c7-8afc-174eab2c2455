import {ApiTypeEnum} from "@/enums";

export enum ChannelSourceEnum {
  PRODUCT = ApiTypeEnum.PRODUCT,
  CONTENT = ApiTypeEnum.CONTENT,
  MEDIA = ApiTypeEnum.MEDIA,
  ATTRIBUTE = ApiTypeEnum.ATTRIBUTE,
  ATTRIBUTE_GROUP = ApiTypeEnum.ATTRIBUTE_GROUP,
  ATTRIBUTE_OPTION = ApiTypeEnum.ATTRIBUTE_OPTION,
  CHANNEL = ApiTypeEnum.CATALOG_SCOPE,
  COMPLETUDE = ApiTypeEnum.COMPLETUDE,
  CURRENCY = ApiTypeEnum.CURRENCY,
  HUB_SOURCE = ApiTypeEnum.HUB_SOURCE,
  HUB_STOCK = ApiTypeEnum.HUB_STOCK,
  LOCALE = ApiTypeEnum.LOCALE,
  RULE = ApiTypeEnum.RULE,
  RULE_GROUP = ApiTypeEnum.RULE_GROUP,
  SCOPE = ApiTypeEnum.SCOPE,
  TEMPLATE = ApiTypeEnum.TEMPLATE,
  USER = ApiTypeEnum.USER,
}
