import MediaIcon from "@/components/attributeTypeIcons/Media";
import AssetIcon from "@/components/attributeTypeIcons/Asset";
import DateIcon from "@/components/attributeTypeIcons/Date";
import DecimalIcon from "@/components/attributeTypeIcons/Decimal";
import MediaCollectionIcon from "@/components/attributeTypeIcons/MediaCollection";
import ProductIcon from "@/components/attributeTypeIcons/Product";
import ProductCollectionIcon from "@/components/attributeTypeIcons/ProductCollection";
import ColorIcon from "@/components/attributeTypeIcons/Color";
import ContentIcon from "@/components/attributeTypeIcons/Content";
import ContentCollectionIcon from "@/components/attributeTypeIcons/ContentCollection";
import LinkIcon from "@/components/attributeTypeIcons/Link";
import MailIcon from "@/components/attributeTypeIcons/Mail";
import MetricIcon from "@/components/attributeTypeIcons/Metric";
import MultiSelectIcon from "@/components/attributeTypeIcons/MultiSelect";
import NumberIcon from "@/components/attributeTypeIcons/Number";
import PriceCollectionIcon from "@/components/attributeTypeIcons/Price";
import SelectIcon from "@/components/attributeTypeIcons/Select";
import SwitchIcon from "@/components/attributeTypeIcons/Switch";
import TextCollectionIcon from "@/components/attributeTypeIcons/TextCollection";
import TextIcon from "@/components/attributeTypeIcons/Text";
import TextareaIcon from "@/components/attributeTypeIcons/Textarea";
import HtmlIcon from "@/components/attributeTypeIcons/Html";
import JsonIcon from "@/components/attributeTypeIcons/Json";
import {AttributeTypeEnum} from "@/enums";
import type {ComponentType} from "react";

export const AttributeTypeIconEnum: Record<AttributeTypeEnum, ComponentType> = {
  [AttributeTypeEnum.MEDIA]: MediaIcon,
  [AttributeTypeEnum.ASSET_COLLECTION]: AssetIcon,
  [AttributeTypeEnum.DATE]: DateIcon,
  [AttributeTypeEnum.DECIMAL]: DecimalIcon,
  [AttributeTypeEnum.MEDIA_COLLECTION]: MediaCollectionIcon,
  [AttributeTypeEnum.PRODUCT]: ProductIcon,
  [AttributeTypeEnum.PRODUCT_COLLECTION]: ProductCollectionIcon,
  [AttributeTypeEnum.COLOR]: ColorIcon,
  [AttributeTypeEnum.CONTENT]: ContentIcon,
  [AttributeTypeEnum.CONTENT_COLLECTION]: ContentCollectionIcon,
  [AttributeTypeEnum.LINK]: LinkIcon,
  [AttributeTypeEnum.MAIL]: MailIcon,
  [AttributeTypeEnum.METRIC]: MetricIcon,
  [AttributeTypeEnum.MULTISELECT]: MultiSelectIcon,
  [AttributeTypeEnum.NUMBER]: NumberIcon,
  [AttributeTypeEnum.PRICE_COLLECTION]: PriceCollectionIcon,
  [AttributeTypeEnum.SELECT]: SelectIcon,
  [AttributeTypeEnum.SWITCH]: SwitchIcon,
  [AttributeTypeEnum.TEXT_COLLECTION]: TextCollectionIcon,
  [AttributeTypeEnum.TEXT]: TextIcon,
  [AttributeTypeEnum.TEXTAREA]: TextareaIcon,
  [AttributeTypeEnum.HTML]: HtmlIcon,
  [AttributeTypeEnum.JSON]: JsonIcon,
};
