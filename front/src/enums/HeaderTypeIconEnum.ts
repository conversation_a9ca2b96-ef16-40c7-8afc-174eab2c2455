import { HeaderTypeE<PERSON> } from "@/enums/HeaderTypeEnum";
import ReceiptLongIcon from "@mui/icons-material/ReceiptLong";
import ManageSearchIcon from "@mui/icons-material/ManageSearch";
import type { SvgIconComponent } from "@mui/icons-material";

export const HeaderTypeIconEnum: Record<HeaderTypeEnum, SvgIconComponent> = {
  [HeaderTypeEnum.PROPERTY]: ReceiptLongIcon,
  [HeaderTypeEnum.VIRTUAL_PROPERTY]: ReceiptLongIcon,
  [HeaderTypeEnum.ATTRIBUTE]: ManageSearchIcon,
  [HeaderTypeEnum.COMPLETUDE]: ManageSearchIcon,
  [HeaderTypeEnum.WORKFLOW]: ManageSearchIcon,
};
