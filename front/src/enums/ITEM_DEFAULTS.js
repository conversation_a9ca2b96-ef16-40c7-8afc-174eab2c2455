import ORDERS_STATES from "@/enums/ORDERS_STATES";
import { ApiTypeEnum, AttributeTypeEnum, FilterOperatorEnum, RuleLevelEnum } from "@/enums";

const ITEM_DEFAULTS = {
  [ApiTypeEnum.ATTRIBUTE]: {
    names: {},
    attributeGroup: null,
    type: AttributeTypeEnum.TEXT,
    isRequired: false,
    isSearchable: true,
    isLocalizable: false,
    isScopable: false,
    settings: {},
    code: "",
    status: true,
  },
  [ApiTypeEnum.ATTRIBUTE_GROUP]: {
    names: {},
    code: "",
    status: true,
  },
  [ApiTypeEnum.ATTRIBUTE_OPTION]: {
    attribute: null,
    code: "",
    names: {},
  },
  [ApiTypeEnum.ATTRIBUTE_SUMMARY]: {
    code: "",
    attribute: null,
    group: null,
  },
  [ApiTypeEnum.AUTH_METHOD]: {
    type: "",
    url: null,
    options: [],
  },
  [ApiTypeEnum.BREADCRUMB]: {
    parts: [],
  },
  [ApiTypeEnum.BREADCRUMB_PART]: {
    id: 0,
    names: {},
  },
  [ApiTypeEnum.CATALOG]: {
    names: {},
    filters: { filters: [], operator: FilterOperatorEnum.AND },
    code: "",
    type: "Product",
    upload: null,
    status: true,
  },
  [ApiTypeEnum.CATALOG_SYNC]: {
    codes: [],
  },
  [ApiTypeEnum.CATALOG_SCOPE]: {
    catalog: null,
    scope: null,
    locales: [],
    workflow: null,
    code: "",
    status: true,
  },
  [ApiTypeEnum.CATALOG_SCOPE_SUMMARY]: {
    code: "",
    catalogScope: null,
    catalog: null,
    scope: null,
    locales: [],
    workflow: null,
  },
  [ApiTypeEnum.CATALOG_SCOPE_WORKFLOW_STAT]: {
    stepId: 0,
    ruleGroupCode: "",
    ruleGroupNames: {},
    ruleCode: "",
    ruleNames: {},
    errors: [],
  },
  [ApiTypeEnum.CATALOG_SCOPE_WORKFLOW_STATUS]: {
    code: null,
    stats: [],
  },
  [ApiTypeEnum.CATEGORY]: {
    catalog: null,
    parent: null,
    name: null,
    filters: { filters: [], operator: FilterOperatorEnum.AND },
    position: 0,
    status: true,
    values: {},
  },
  [ApiTypeEnum.COLLECTION]: {},
  [ApiTypeEnum.COMPLETUDE]: {
    names: {},
    code: "",
  },
  [ApiTypeEnum.CONFIGURATION_SYNC]: {},
  [ApiTypeEnum.CONTENT_FOLDER]: {
    name: null,
    parent: null,
    position: 0,
  },
  [ApiTypeEnum.COUNTERS]: {},
  [ApiTypeEnum.EXPORT]: {
    source: {
      type: null,
    },
    format: {
      type: null,
      parameters: {},
    },
    adapter: {
      type: null,
      parameters: {},
    },
    schedule: {
      cron: null,
    },
    mapping: {
      root: null,
      columns: [],
    },
  },
  [ApiTypeEnum.IMPORT]: {
    source: {
      type: null,
    },
    format: {
      type: null,
      parameters: {},
    },
    adapter: {
      type: null,
      parameters: {},
    },
    schedule: {
      cron: null,
    },
    mapping: {
      root: null,
      columns: [],
    },
  },
  [ApiTypeEnum.FILE_EXTRACTOR]: {
    id: 0,
    type: "",
    skus: [],
    error: null,
  },
  [ApiTypeEnum.FILTER]: {
    code: null,
    locale: null,
    operator: "",
  },
  [ApiTypeEnum.FILTERS]: {},
  [ApiTypeEnum.FILTERS_RESOLVE]: {
    filters: null,
  },
  [ApiTypeEnum.HUB_ORDER]: {
    state: ORDERS_STATES.PENDING_FOR_VALIDATION,
    billing: {
      street: "",
      street2: "",
      city: "",
      postcode: "",
      region: "",
      country: "",
      phone: "",
      mobile: "",
      email: "",
      company: "",
    },
    shipping: {
      street: "",
      street2: "",
      city: "",
      postcode: "",
      region: "",
      country: "",
      phone: "",
      mobile: "",
      email: "",
      company: "",
    },
    shippingPart: .0,
  },
  [ApiTypeEnum.HUB_STOCK]: {
    source: null,
    product: null,
    quantity: 0,
  },
  [ApiTypeEnum.HUB_SOURCE]: {
    names: {},
    code: "",
    upload: null,
    status: true,
  },
  [ApiTypeEnum.LIGHT_CATALOG_SCOPE]: {
    code: "",
    iri: null,
  },
  [ApiTypeEnum.LIGHT_LOCALE]: {
    code: "",
    iri: null,
  },
  [ApiTypeEnum.LIGHT_RULE]: {
    code: "",
    iri: "",
    names: {},
    descriptions: {},
    level: "",
    errorTargets: [],
  },
  [ApiTypeEnum.LIGHT_SCOPE]: {
    code: "",
    iri: null,
    names: {},
  },
  [ApiTypeEnum.LOCALE]: {
    names: {},
    code: "",
  },
  [ApiTypeEnum.MEASURE_FAMILY]: {
    names: {},
    default: "",
    units: [],
    code: "",
  },
  [ApiTypeEnum.MEDIA]: {
    category: null,
    name: "",
    type: "",
    size: 0,
  },
  [ApiTypeEnum.MEDIA_FOLDER]: {
    name: null,
    parent: null,
    position: 0,
  },
  [ApiTypeEnum.PASSWORD_RESET]: {
    token: null,
    password: null,
  },
  [ApiTypeEnum.PASSWORD_RESET_START]: {
    email: null,
  },
  [ApiTypeEnum.PASSWORD_RESET_TOKEN]: {
    email: null,
  },
  [ApiTypeEnum.PASSWORD_UPDATE]: {
    oldPassword: null,
    newPassword: null,
  },
  [ApiTypeEnum.PLATFORM_STATISTIC]: {
    code: "",
    count: null,
    date: "",
  },
  [ApiTypeEnum.PRODUCT]: {
    sku: "",
    attributeGroups: [],
    status: true,
    values: {},
  },
  [ApiTypeEnum.DOCUMENT_ERROR_SUMMARY]: {
    id: 0,
    iri: null,
    sku: "",
    createdAt: "",
    step: 0,
    steps: 0,
    type: "",
    rule: {},
    catalogScope: {},
    scope: {},
    locale: {},
    statuses: null,
    color: "",
    product: null,
  },
  [ApiTypeEnum.PRODUCT_SCOPE]: {
    sku: null,
    attributeGroups: [],
    catalogScopes: [],
    errors: [],
  },
  [ApiTypeEnum.PRODUCT_SYNC]: {
    skus: [],
  },
  [ApiTypeEnum.REQUESTABLE_USER_GROUP]: {
    id: 0,
    name: "",
  },
  [ApiTypeEnum.RULE]: {
    ruleGroup: null,
    names: {},
    descriptions: {},
    isBlocking: false,
    triggerCondition: null,
    rule: "",
    errorTargets: [],
    level: RuleLevelEnum.MEDIUM,
    code: "",
    position: 0,
  },
  [ApiTypeEnum.RULE_GROUP]: {
    names: {},
    triggerCondition: null,
    code: "",
  },
  [ApiTypeEnum.RULE_TEST]: {
    id: null,
    rule: null,
    locale: null,
    skus: [],
    results: [],
  },
  [ApiTypeEnum.SETTINGS]: {
    metricMeasureFamily: null,
    metricDefaultUnit: null,
  },
  [ApiTypeEnum.SCOPE]: {
    names: {},
    code: "",
    upload: null,
  },
  [ApiTypeEnum.TOKEN]: {
    email: null,
    password: null,
  },
  [ApiTypeEnum.TOKEN_RENEW]: {},
  [ApiTypeEnum.TRANSLATION]: {
    code: "",
    messages: [],
  },
  [ApiTypeEnum.TRANSLATIONS]: {},
  [ApiTypeEnum.TEMPLATE]: {
    code: "",
    names: {},
    attributeGroups: [],
    status: true,
  },
  [ApiTypeEnum.UNIT]: {
    code: null,
    names: null,
    symbol: null,
    convertToStandard: null,
    convertFromStandard: null,
  },
  [ApiTypeEnum.UPLOAD]: {
    name: "",
    type: "",
    size: 0,
  },
  [ApiTypeEnum.USER]: {
    email: "",
    firstname: "",
    lastname: "",
    lang: null,
    locale: null,
    preferences: {},
    upload: null,
    status: true,
  },
  [ApiTypeEnum.USER_GROUP]: {
    name: "",
    color: "#000000",
    catalogs: [],
    parent: null,
    upload: null,
  },
  [ApiTypeEnum.USER_GROUP_MEMBERSHIP]: {
    user: null,
    group: null,
    role: "",
  },
  [ApiTypeEnum.USER_GROUP_MEMBERSHIP_REQUEST]: {
    group: null,
    message: null,
  },
  [ApiTypeEnum.USER_GROUP_MEMBERSHIP_REQUEST_HANDLE]: {
    id: null,
    wantedGroups: [],
  },
  [ApiTypeEnum.USER_GROUP_MEMBERSHIP_REQUEST_SUMMARY]: {
    id: 0,
    user: null,
    group: null,
    message: "",
    createdAt: "",
  },
  [ApiTypeEnum.USER_GROUP_SCOPE]: {
    group: null,
    attributeGroup: null,
    scope: "",
  },
  [ApiTypeEnum.USER_LOGIN]: {
    id: 0,
    email: "",
    loginCount: 0,
    role: "",
    status: false,
  },
  [ApiTypeEnum.VALUE]: {
    code: "",
    data: [],
  },
  [ApiTypeEnum.VALUE_DATA]: {
    locale: null,
    scope: null,
  },
  [ApiTypeEnum.VALUES]: {},
  [ApiTypeEnum.WORKFLOW]: {
    names: {},
    descriptions: {},
    statuses: {},
    color: "#000000",
  },
  [ApiTypeEnum.WORKFLOW_GLOBAL_STAT]: {
    id: 0,
    productCount: 0,
    productErrors: 0,
    catalogScopeCount: 0,
    scopeCount: 0,
  },
  [ApiTypeEnum.WORKFLOW_SPECIFIC_STAT]: {
    id: 0,
    byScope: [],
    byLocale: [],
    byRuleGroup: [],
    byRule: [],
  },
  [ApiTypeEnum.WORKFLOW_SPECIFIC_STAT_MODEL]: {
    code: "",
    errorCount: 0,
    color: "",
  },
  [ApiTypeEnum.WORKFLOW_STEP]: {
    names: {},
    workflow: null,
    ruleGroups: [],
    isBlocking: false,
    statuses: {},
    color: "#000000",
    position: 0,
  },
  [ApiTypeEnum.WORKFLOW_STEP_SUMMARY]: {
    id: 0,
    step: null,
    ruleGroups: {},
  },
  [ApiTypeEnum.DICTIONARY]: {
    code: "",
    names: {},
  },
  [ApiTypeEnum.DICTIONARY_MAPPING]: {
    from: "",
    to: "",
  },
};

export default ITEM_DEFAULTS;
