import RU<PERSON>_LEVELS from "@/enums/RULE_LEVELS";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import ORDERS_STATES from "@/enums/ORDERS_STATES";
import { AttributeTypeEnum } from "@/enums";

const ITEM_DEFAULTS = {
  [ITEM_TYPES.ATTRIBUTE]: {
    names: {},
    attributeGroup: null,
    type: AttributeTypeEnum.TEXT,
    isRequired: false,
    isSearchable: true,
    isLocalizable: false,
    isScopable: false,
    settings: {},
    code: "",
    status: true,
  },
  [ITEM_TYPES.ATTRIBUTE_GROUP]: {
    names: {},
    code: "",
    status: true,
  },
  [ITEM_TYPES.ATTRIBUTE_OPTION]: {
    attribute: null,
    code: "",
    names: {},
  },
  [ITEM_TYPES.ATTRIBUTE_SUMMARY]: {
    code: "",
    attribute: null,
    group: null,
  },
  [ITEM_TYPES.AUTH_METHOD]: {
    type: "",
    url: null,
    options: [],
  },
  [ITEM_TYPES.BREADCRUMB]: {
    parts: [],
  },
  [ITEM_TYPES.BREADCRUMB_PART]: {
    id: 0,
    names: {},
  },
  [ITEM_TYPES.CATALOG]: {
    names: {},
    filters: { filters: [], operator: "AND" },
    code: "",
    type: "Product",
    upload: null,
    status: true,
  },
  [ITEM_TYPES.CATALOG_SYNC]: {
    codes: [],
  },
  [ITEM_TYPES.CATALOG_SCOPE]: {
    catalog: null,
    scope: null,
    locales: [],
    workflow: null,
    code: "",
    status: true,
  },
  [ITEM_TYPES.CATALOG_SCOPE_SUMMARY]: {
    code: "",
    catalogScope: null,
    catalog: null,
    scope: null,
    locales: [],
    workflow: null,
  },
  [ITEM_TYPES.CATALOG_SCOPE_WORKFLOW_STAT]: {
    stepId: 0,
    ruleGroupCode: "",
    ruleGroupNames: {},
    ruleCode: "",
    ruleNames: {},
    errors: [],
  },
  [ITEM_TYPES.CATALOG_SCOPE_WORKFLOW_STATUS]: {
    code: null,
    stats: [],
  },
  [ITEM_TYPES.CATEGORY]: {
    catalog: null,
    parent: null,
    name: null,
    filters: { filters: [], operator: "AND" },
    position: 0,
    status: true,
    values: {},
  },
  [ITEM_TYPES.COLLECTION]: {},
  [ITEM_TYPES.COMPLETUDE]: {
    names: {},
    code: "",
  },
  [ITEM_TYPES.CONFIGURATION_SYNC]: {},
  [ITEM_TYPES.CONTENT_FOLDER]: {
    name: null,
    parent: null,
    position: 0,
  },
  [ITEM_TYPES.COUNTERS]: {},
  [ITEM_TYPES.EXPORT]: {
    source: {
      type: null,
    },
    format: {
      type: null,
      parameters: {},
    },
    adapter: {
      type: null,
      parameters: {},
    },
    schedule: {
      cron: null,
    },
    mapping: {
      root: null,
      columns: [],
    },
  },
  [ITEM_TYPES.IMPORT]: {
    source: {
      type: null,
    },
    format: {
      type: null,
      parameters: {},
    },
    adapter: {
      type: null,
      parameters: {},
    },
    schedule: {
      cron: null,
    },
    mapping: {
      root: null,
      columns: [],
    },
  },
  [ITEM_TYPES.FILE_EXTRACTOR]: {
    id: 0,
    type: "",
    skus: [],
    error: null,
  },
  [ITEM_TYPES.FILTER]: {
    code: null,
    locale: null,
    operator: "",
  },
  [ITEM_TYPES.FILTERS]: {},
  [ITEM_TYPES.FILTERS_RESOLVE]: {
    filters: null,
  },
  [ITEM_TYPES.HUB_ORDER]: {
    state: ORDERS_STATES.PENDING_FOR_VALIDATION,
    billing: {
      street: "",
      street2: "",
      city: "",
      postcode: "",
      region: "",
      country: "",
      phone: "",
      mobile: "",
      email: "",
      company: "",
    },
    shipping: {
      street: "",
      street2: "",
      city: "",
      postcode: "",
      region: "",
      country: "",
      phone: "",
      mobile: "",
      email: "",
      company: "",
    },
    shippingPart: .0
  },
  [ITEM_TYPES.HUB_STOCK]: {
    source: null,
    product: null,
    quantity: 0,
  },
  [ITEM_TYPES.HUB_SOURCE]: {
    names: {},
    code: "",
    upload: null,
    status: true,
  },
  [ITEM_TYPES.LIGHT_CATALOG_SCOPE]: {
    code: "",
    iri: null,
  },
  [ITEM_TYPES.LIGHT_LOCALE]: {
    code: "",
    iri: null,
  },
  [ITEM_TYPES.LIGHT_RULE]: {
    code: "",
    iri: "",
    names: {},
    descriptions: {},
    level: "",
    errorTargets: [],
  },
  [ITEM_TYPES.LIGHT_SCOPE]: {
    code: "",
    iri: null,
    names: {},
  },
  [ITEM_TYPES.LOCALE]: {
    names: {},
    code: "",
  },
  [ITEM_TYPES.MEASURE_FAMILY]: {
    names: {},
    default: "",
    units: [],
    code: "",
  },
  [ITEM_TYPES.MEDIA]: {
    category: null,
    name: "",
    type: "",
    size: 0,
  },
  [ITEM_TYPES.MEDIA_FOLDER]: {
    name: null,
    parent: null,
    position: 0,
  },
  [ITEM_TYPES.PASSWORD_RESET]: {
    token: null,
    password: null,
  },
  [ITEM_TYPES.PASSWORD_RESET_START]: {
    email: null,
  },
  [ITEM_TYPES.PASSWORD_RESET_TOKEN]: {
    email: null,
  },
  [ITEM_TYPES.PASSWORD_UPDATE]: {
    oldPassword: null,
    newPassword: null,
  },
  [ITEM_TYPES.PLATFORM_STATISTIC]: {
    code: "",
    count: null,
    date: "",
  },
  [ITEM_TYPES.PRODUCT]: {
    sku: "",
    attributeGroups: [],
    status: true,
    values: {},
  },
  [ITEM_TYPES.DOCUMENT_ERROR_SUMMARY]: {
    id: 0,
    iri: null,
    sku: "",
    createdAt: "",
    step: 0,
    steps: 0,
    type: "",
    rule: {},
    catalogScope: {},
    scope: {},
    locale: {},
    statuses: null,
    color: "",
    product: null,
  },
  [ITEM_TYPES.PRODUCT_SCOPE]: {
    sku: null,
    attributeGroups: [],
    catalogScopes: [],
    errors: [],
  },
  [ITEM_TYPES.PRODUCT_SYNC]: {
    skus: [],
  },
  [ITEM_TYPES.REQUESTABLE_USER_GROUP]: {
    id: 0,
    name: "",
  },
  [ITEM_TYPES.RULE]: {
    ruleGroup: null,
    names: {},
    descriptions: {},
    isBlocking: false,
    triggerCondition: null,
    rule: "",
    errorTargets: [],
    level: RULE_LEVELS.MEDIUM,
    code: "",
    position: 0,
  },
  [ITEM_TYPES.RULE_GROUP]: {
    names: {},
    triggerCondition: null,
    code: "",
  },
  [ITEM_TYPES.RULE_TEST]: {
    id: null,
    rule: null,
    locale: null,
    skus: [],
    results: [],
  },
  [ITEM_TYPES.SETTINGS]: {
    metricMeasureFamily: null,
    metricDefaultUnit: null,
  },
  [ITEM_TYPES.SCOPE]: {
    names: {},
    code: "",
    upload: null,
  },
  [ITEM_TYPES.TOKEN]: {
    email: null,
    password: null,
  },
  [ITEM_TYPES.TOKEN_RENEW]: {},
  [ITEM_TYPES.TRANSLATION]: {
    code: "",
    messages: [],
  },
  [ITEM_TYPES.TRANSLATIONS]: {},
  [ITEM_TYPES.TEMPLATE]: {
    code: "",
    names: {},
    attributeGroups: [],
    status: true,
  },
  [ITEM_TYPES.UNIT]: {
    code: null,
    names: null,
    symbol: null,
    convertToStandard: null,
    convertFromStandard: null,
  },
  [ITEM_TYPES.UPLOAD]: {
    name: "",
    type: "",
    size: 0,
  },
  [ITEM_TYPES.USER]: {
    email: "",
    firstname: "",
    lastname: "",
    lang: null,
    locale: null,
    preferences: {},
    upload: null,
    status: true,
  },
  [ITEM_TYPES.USER_GROUP]: {
    name: "",
    color: "#000000",
    catalogs: [],
    parent: null,
    upload: null,
  },
  [ITEM_TYPES.USER_GROUP_MEMBERSHIP]: {
    user: null,
    group: null,
    role: "",
  },
  [ITEM_TYPES.USER_GROUP_MEMBERSHIP_REQUEST]: {
    group: null,
    message: null,
  },
  [ITEM_TYPES.USER_GROUP_MEMBERSHIP_REQUEST_HANDLE]: {
    id: null,
    wantedGroups: [],
  },
  [ITEM_TYPES.USER_GROUP_MEMBERSHIP_REQUEST_SUMMARY]: {
    id: 0,
    user: null,
    group: null,
    message: "",
    createdAt: "",
  },
  [ITEM_TYPES.USER_GROUP_SCOPE]: {
    group: null,
    attributeGroup: null,
    scope: "",
  },
  [ITEM_TYPES.USER_LOGIN]: {
    id: 0,
    email: "",
    loginCount: 0,
    role: "",
    status: false,
  },
  [ITEM_TYPES.VALUE]: {
    code: "",
    data: [],
  },
  [ITEM_TYPES.VALUE_DATA]: {
    locale: null,
    scope: null,
  },
  [ITEM_TYPES.VALUES]: {},
  [ITEM_TYPES.WORKFLOW]: {
    names: {},
    descriptions: {},
    statuses: {},
    color: "#000000",
  },
  [ITEM_TYPES.WORKFLOW_GLOBAL_STAT]: {
    id: 0,
    productCount: 0,
    productErrors: 0,
    catalogScopeCount: 0,
    scopeCount: 0,
  },
  [ITEM_TYPES.WORKFLOW_SPECIFIC_STAT]: {
    id: 0,
    byScope: [],
    byLocale: [],
    byRuleGroup: [],
    byRule: [],
  },
  [ITEM_TYPES.WORKFLOW_SPECIFIC_STAT_MODEL]: {
    code: "",
    errorCount: 0,
    color: "",
  },
  [ITEM_TYPES.WORKFLOW_STEP]: {
    names: {},
    workflow: null,
    ruleGroups: [],
    isBlocking: false,
    statuses: {},
    color: "#000000",
    position: 0,
  },
  [ITEM_TYPES.WORKFLOW_STEP_SUMMARY]: {
    id: 0,
    step: null,
    ruleGroups: {},
  },
};

export default ITEM_DEFAULTS;
