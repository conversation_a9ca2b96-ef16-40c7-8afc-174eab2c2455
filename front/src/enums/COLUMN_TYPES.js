import { AttributeTypeEnum } from "@/enums";

export const COLUMN_TYPES = {
  STRING: "string",
  NUMBER: "number",
  DATE: "date",
  DATETIME: "datetime",
  BOOLEAN: "boolean",
  SINGLE_SELECT: "single_select",
};

export const COLUMN_ATTRIBUTE_TYPES = {
  [AttributeTypeEnum.NUMBER]: COLUMN_TYPES.NUMBER,
  [AttributeTypeEnum.DECIMAL]: COLUMN_TYPES.NUMBER,
  [AttributeTypeEnum.PRICE]: COLUMN_TYPES.NUMBER,
  [AttributeTypeEnum.PRICE_COLLECTION]: COLUMN_TYPES.NUMBER,
  [AttributeTypeEnum.DATE]: COLUMN_TYPES.DATETIME,
  [AttributeTypeEnum.SWITCH]: COLUMN_TYPES.BOOLEAN,
};
