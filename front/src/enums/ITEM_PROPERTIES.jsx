import { Thumbnail, ThumbnailValues } from "@/theme/components/Image";
import ITEM_TYPES from "./ITEM_TYPES";
import Time from "@/components/ui/Time";
import Translations from "@/components/ui/translation/Translations";
import { StyledLink } from "@/theme/styled";
import { generatePath } from "react-router";
import { Stack, TableCell } from "@mui/material";
import FileUtils from "@/utils/file.utils";
import { Fragment } from "react";
import { HEADER_TYPES } from "@/utils/models/Header";
import ChipType from "@/components/chips/ChipType";
import ChipAttributes from "@/components/chips/ChipAttributes";
import { RenderIsBlocking } from "@/components/ui/isBlocking/RenderIsBlocking";
import StackedLabelCode from "@/components/ui/StackedLabelCode";
import { Body1 } from "@/components/ui/Typography";
import ChipUsers from "@/components/chips/ChipUsers";
import AvatarUser from "@/components/ui/avatars/AvatarUser";
import Schedule from "@/components/ui/Schedule";
import OrdersStates from "@/components/ui/OrdersStates";
import { useTranslation } from "@/hooks/useTranslation";
import Chips from "@/components/chips/Chips";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import CancelIcon from "@mui/icons-material/Cancel";
import HubOriginOwnerLogo from "@/components/hubOrder/HubOriginOwnerLogo";
import Price from "@/components/ui/Price";
import { ChipDefault, ChipDefaultFilled, ChipPrimaryFilled } from "@/components/ui/Chip";
import ChipRuleLevel from "@/components/chips/ChipRuleLevel";
import NoResultValue from "@/components/ui/NoResultValue";
import { RouteEnum } from "@/enums/RouteEnum";

export const ITEM_PROPERTIES = {
  [ITEM_TYPES.PRODUCT]: [
    {
      code: "upload",
      headerType: HEADER_TYPES.VIRTUAL_PROPERTY,
      label: "items.Product.upload",
      type: "file",
      hideHeader: true,
      width: 60,
      renderCell: (params) => {
        return <ThumbnailValues values={params.row.values} />;
      },
    },
    {
      code: "uuid",
      headerType: HEADER_TYPES.PROPERTY,
      label: "UUID",
      type: "text",
      flex: 1,
      isSearchable: true,
      renderCell: (params) => {
        return <strong>{params.row.uuid}</strong>;
      },
    },
    {
      code: "sku",
      headerType: HEADER_TYPES.PROPERTY,
      label: "SKU",
      type: "text",
      flex: 1,
      isSearchable: true,
      renderCell: (params) => {
        return <strong>{params.row.sku}</strong>;
      },
    },
    {
      code: "createdAt",
      headerType: HEADER_TYPES.PROPERTY,
      label: "actions.created_at",
      type: "date",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Time datetime={params.row.createdAt} />;
      },
    },
    {
      code: "updatedAt",
      headerType: HEADER_TYPES.PROPERTY,
      label: "common.updated_at",
      type: "date",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Time datetime={params.row.updatedAt} />;
      },
    },
    {
      code: "status",
      headerType: HEADER_TYPES.PROPERTY,
      label: "common.status",
      type: "bool",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return params.row.status ? <CheckCircleOutlineIcon color="success" /> : <CancelIcon color="error" />;
      },
    },
  ],
  [ITEM_TYPES.MEDIA]: [
    {
      code: "upload",
      label: "items.Media.upload",
      type: "file",
      hideHeader: true,
      width: 60,
      headerType: HEADER_TYPES.VIRTUAL_PROPERTY,
      renderCell: (params) => {
        return <Thumbnail src={params.row.url} />;
      },
    },
    {
      code: "uuid",
      headerType: HEADER_TYPES.PROPERTY,
      label: "UUID",
      type: "text",
      flex: 1,
      isSearchable: true,
      renderCell: (params) => {
        return <strong>{params.row.uuid}</strong>;
      },
    },
    {
      code: "path",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Media.path",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <strong>{params.row.path}</strong>;
      },
    },
    {
      code: "name",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Media.name",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <strong>{params.row.name}</strong>;
      },
    },
    {
      code: "size",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Media.size",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return FileUtils.convertSize(params.row);
      },
    },
    {
      code: "type",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Media.type",
      type: "text",
      isSearchable: true,
      flex: 1,
    },
    {
      code: "source",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Media.source",
      type: "text",
      isSearchable: true,
      flex: 1,
    },
    {
      code: "createdAt",
      headerType: HEADER_TYPES.PROPERTY,
      label: "actions.created_at",
      type: "date",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Time datetime={params.row.createdAt} />;
      },
    },
    {
      code: "updatedAt",
      headerType: HEADER_TYPES.PROPERTY,
      label: "actions.updated_at",
      type: "date",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Time datetime={params.row.updatedAt} />;
      },
    },
    {
      code: "status",
      headerType: HEADER_TYPES.PROPERTY,
      label: "common.status",
      type: "bool",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return params.row.status ? <CheckCircleOutlineIcon color="success" /> : <CancelIcon color="error" />;
      },
    },
  ],
  [ITEM_TYPES.CONTENT]: [
    {
      code: "upload",
      headerType: HEADER_TYPES.VIRTUAL_PROPERTY,
      label: "items.Content.upload",
      type: "file",
      width: 60,
      hideHeader: true,
      renderCell: (params) => {
        return <ThumbnailValues values={params.row.values} />;
      },
    },
    {
      code: "uuid",
      headerType: HEADER_TYPES.PROPERTY,
      label: "UUID",
      type: "text",
      flex: 1,
      isSearchable: true,
      renderCell: (params) => {
        return <strong>{params.row.uuid}</strong>;
      },
    },
    {
      code: "name",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Content.name",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <strong>{params.row.name}</strong>;
      },
    },
    {
      code: "path",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Content.path",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <strong>{params.row.path}</strong>;
      },
    },
    {
      code: "createdAt",
      headerType: HEADER_TYPES.PROPERTY,
      label: "actions.created_at",
      type: "date",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Time datetime={params.row.createdAt} />;
      },
    },
    {
      code: "updatedAt",
      headerType: HEADER_TYPES.PROPERTY,
      label: "actions.updated_at",
      type: "date",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Time datetime={params.row.updatedAt} />;
      },
    },
    {
      code: "status",
      headerType: HEADER_TYPES.PROPERTY,
      label: "common.status",
      type: "bool",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return params.row.status ? <CheckCircleOutlineIcon color="success" /> : <CancelIcon color="error" />;
      },
    },
  ],
  [ITEM_TYPES.CATALOG]: [
    {
      code: "upload",
      headerType: HEADER_TYPES.VIRTUAL_PROPERTY,
      label: "items.Catalog.upload",
      type: "file",
      hideHeader: true,
      width: 60,
      renderCell: (params) => {
        return <Thumbnail src={params.row.upload} />;
      },
    },
    {
      code: "code",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Catalog.code",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <strong>{params.row.code}</strong>;
      },
    },
    {
      code: "names",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Catalog.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Translations translations={params.row.names} />;
      },
    },
    {
      code: "createdAt",
      headerType: HEADER_TYPES.PROPERTY,
      label: "actions.created_at",
      type: "date",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Time datetime={params.row.createdAt} />;
      },
    },
    {
      code: "counters.categories",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Catalog.categories",
      type: "number",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return params.row.counters.categories;
      },
    },
    {
      code: "counters.products",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Catalog.products",
      type: "number",
      hideHeader: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return (
          <StyledLink to={generatePath(RouteEnum.CATALOGS_DETAIL, { code: params.row.code })}>
            {params.row.counters.products} {t(params.row.counters.products > 1 ? "items.Product._" : "items.Product._singular")}
          </StyledLink>
        );
      },
    },
  ],
  [ITEM_TYPES.SCOPE]: [
    {
      code: "upload",
      headerType: HEADER_TYPES.VIRTUAL_PROPERTY,
      label: "Upload",
      type: "file",
      hideHeader: true,
      width: 60,
      renderCell: (params) => {
        return <Thumbnail src={params.row.upload} />;
      },
    },
    {
      code: "code",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Scope.code",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <strong>{params.row.code}</strong>;
      },
    },
    {
      code: "names",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Scope.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Translations translations={params.row.names} />;
      },
    },
    {
      code: "createdAt",
      headerType: HEADER_TYPES.PROPERTY,
      label: "actions.created_at",
      type: "date",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Time datetime={params.row.createdAt} />;
      },
    },
  ],
  [ITEM_TYPES.CATALOG_SCOPE]: [
    {
      code: "code",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.CatalogScope.code",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return (
          <Stack direction="row" alignItems="center" gap={1}>
            <Body1 fontWeight="bold">{params.row.code}</Body1>
          </Stack>
        );
      },
    },
    {
      code: "catalogScope",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.CatalogScope.names",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Translations translations={params.row.catalogScope.names} />;
      },
    },
    {
      code: "scope",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.CatalogScope.scope",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Translations translations={params.row.scope.names} />;
      },
    },
    {
      code: "locale",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.CatalogScope.locales",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return params.row.locales.map((l, i) => (
          <Fragment key={l["@id"]}>
            <Translations translations={l.names} />
            {i < params.row?.locales.length - 1 && ", "}
          </Fragment>
        ));
      },
    },
    {
      code: "workflow",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.CatalogScope.workflow",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Translations translations={params.row.workflow?.names} />;
      },
    },
    {
      code: "catalog",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.CatalogScope.catalog",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Translations translations={params.row.catalog?.names} />;
      },
    },
    {
      code: "type",
      headerType: HEADER_TYPES.PROPERTY,
      label: "common.type",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return <ChipDefault>{t(`enums.CHANNEL_TYPE.${params.row.catalogScope.type}`)}</ChipDefault>;
      },
    },
  ],
  [ITEM_TYPES.CHANNEL]: [
    {
      code: "code",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Channel.code",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return (
          <Stack direction="row" alignItems="center" gap={1}>
            <Body1 fontWeight="bold">{params.row.code}</Body1>
          </Stack>
        );
      },
    },
    {
      code: "catalogScope",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Channel.names",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Translations translations={params.row.names} />;
      },
    },
    {
      code: "type",
      headerType: HEADER_TYPES.PROPERTY,
      label: "common.type",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return <ChipDefault>{t(`enums.CHANNEL_TYPE.${params.row.type}`)}</ChipDefault>;
      },
    },
  ],
  [ITEM_TYPES.DOCUMENT_ERROR_SUMMARY]: [
    {
      code: "identifier",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.DocumentErrorSummary.identifier",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <strong>{params.row.documentFrontIdentifier}</strong>;
      },
    },
    {
      code: "rule",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.DocumentErrorSummary.rule",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return params.row.rule.code;
      },
    },
    {
      code: "level",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.DocumentErrorSummary.level",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return (
          <TableCell sx={{ paddingLeft: 0, paddingRight: 0, border: "none" }}>
            <ChipRuleLevel level={params.row.rule.level} />
          </TableCell>
        );
      },
    },
    {
      code: "type",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.DocumentErrorSummary.type",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return t(`rules.types.${params.row.type}`);
      },
    },
    {
      code: "catalogScope",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.DocumentErrorSummary.catalogScope",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return params.row.catalogScope.code;
      },
    },
    {
      code: "locale",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.DocumentErrorSummary.locale",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return params.row.locale.code;
      },
    },
    {
      code: "errorTargets",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.DocumentErrorSummary.errorTargets",
      type: "text",
      flex: 1,
      renderCell: (params) => {
        const array = params.row.rule.errorTargets.map((target, index) => {
          const attribute = params.row.rowProps.getAttribute(target);
          return <ChipDefaultFilled key={`${attribute["@id"]}-${index}`} label={<Translations translations={attribute.names} />} />;
        });
        return <Chips max={1} chips={array} />;
      },
    },
  ],
  [ITEM_TYPES.TEMPLATE]: [
    {
      code: "code",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Template.code",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <strong>{params.row.code}</strong>;
      },
    },
    {
      code: "names",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Template.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Translations translations={params.row.names} />;
      },
    },
    {
      code: "type",
      headerType: HEADER_TYPES.PROPERTY,
      label: "enums.DOCUMENT_TYPES._singular",
      type: "text",
      flex: 1,
      renderCell: (params) => {
        return <ChipType type={params.row.type} />;
      },
    },
    {
      code: "attributeGroups",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Template.attributeGroups",
      type: "number",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return (
          <StyledLink to={`${RouteEnum.CONFIGURATION_CENTRALISATION_ATTRIBUTE_GROUPS}?template=${params.row.code}`}>
            {params.row.counters?.attributeGroups} {params.row.counters?.attributeGroups > 1 ? t("items.AttributeGroup._") : t("items.AttributeGroup._singular")}
          </StyledLink>
        );
      },
    },
  ],
  [ITEM_TYPES.ATTRIBUTE_GROUP]: [
    {
      code: "code",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.AttributeGroup.code",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <strong>{params.row.code}</strong>;
      },
    },
    {
      code: "names",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.AttributeGroup.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Translations translations={params.row.names} />;
      },
    },
    {
      code: "type",
      headerType: HEADER_TYPES.PROPERTY,
      label: "enums.DOCUMENT_TYPES._singular",
      type: "text",
      flex: 1,
      renderCell: (params) => {
        return <ChipType type={params.row.type} />;
      },
    },
    {
      code: "attributes",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.AttributeGroup.counters.attributes",
      type: "number",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return (
          <StyledLink to={`${RouteEnum.CONFIGURATION_CENTRALISATION_ATTRIBUTES}?attributeGroup=${params.row.code}`}>
            {params.row.counters?.attributes} {params.row.counters?.attributes > 1 ? t("items.Attribute._") : t("items.Attribute._singular")}
          </StyledLink>
        );
      },
    },
  ],
  [ITEM_TYPES.ATTRIBUTE]: [
    {
      code: "code",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Attribute.code",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <strong>{params.row.code}</strong>,
    },
    {
      code: "names",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Attribute.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Translations translations={params.row.names} />;
      },
    },
    {
      code: "attributeGroup",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Attribute.group",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return t(params.row.rowProps.attributeGroups.find((elem) => elem["@id"] === params.row.attributeGroup)?.names);
      },
    },
    {
      code: "types",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Attribute.type",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => (
        <TableCell sx={{ paddingLeft: 0, paddingRight: 0, border: "none" }}>
          <ChipAttributes attribute={params.row} />
        </TableCell>
      ),
    },
    {
      code: "counters.uses",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Attribute.counters.uses",
      type: "number",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => params.row.counters?.uses || 0,
    },
  ],
  [ITEM_TYPES.ATTRIBUTE_OPTION]: [
    {
      code: "code",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.AttributeOption.code",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <strong>{params.row.code}</strong>,
    },
    {
      code: "names",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.AttributeOption.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Translations translations={params.row.names} />;
      },
    },
    {
      code: "attribute",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.AttributeOption.attribute",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        return <Translations translations={params.row.rowProps.getAttribute(params.row.attribute)?.names} />;
      },
    },
  ],
  [ITEM_TYPES.RULE]: [
    {
      code: "code",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Rule.code",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <strong>{params.row.code}</strong>,
    },
    {
      code: "level",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Rule.level",
      type: "text",
      isSearchable: true,
      renderCell: (params) => <ChipRuleLevel level={params.row.level} />,
    },
    {
      code: "names",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Rule.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Translations translations={params.row.names} />,
    },
    {
      code: "attributes",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Rule.attributes",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => <strong>{params.row.errorTargets.sort().join(", ")}</strong>,
    },
    {
      code: "isBlocking",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Rule.isBlocking",
      type: "text",
      // isSearchable: true,
      // flex: 1,
      renderCell: (params) => <RenderIsBlocking value={params.row.isBlocking} />,
    },
  ],
  [ITEM_TYPES.RULE_GROUP]: [
    {
      code: "code",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.RuleGroup.code",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <strong>{params.row.code}</strong>,
    },
    {
      code: "names",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.RuleGroup.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Translations translations={params.row.names} />,
    },
    {
      code: "steps",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.RuleGroup.steps",
      type: "number",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => params.row.counters?.steps || 0,
    },
    {
      code: "counters.rules",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.RuleGroup.counters.rules",
      type: "number",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return (
          <StyledLink to={`${RouteEnum.CONFIGURATION_OPTIMISATION_RULES}?ruleGroup=${params.row.code}`}>
            {params.row.counters?.rules} {t(params.row.counters?.rules > 1 ? "items.Rule._" : "items.Rule._singular")}
          </StyledLink>
        );
      },
    },
  ],
  [ITEM_TYPES.WORKFLOW]: [
    {
      code: "names",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Workflow.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Translations translations={params.row.names} />,
    },
    {
      code: "counters.rules",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Workflow.rules",
      type: "number",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => params.row.counters?.rules || 0,
    },
    {
      code: "steps",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.WorkflowStep._",
      type: "number",
      isSearchable: true,
      hideHeader: true,
      renderCell: (params) => {
        const { t } = useTranslation();
        return (
          <StyledLink to={generatePath(RouteEnum.CONFIGURATION_OPTIMISATION_WORKFLOW_DETAIL, { id: params.row.id })}>
            {params.row.counters.steps} {t(params.row.counters.steps > 1 ? "items.WorkflowStep._" : "items.WorkflowStep._singular")}
          </StyledLink>
        );
      },
    },
  ],
  [ITEM_TYPES.WORKFLOW_STEP_RULE_GROUP]: [
    {
      code: "code",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.RuleGroup.code",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <strong>{params.row.code}</strong>,
    },
    {
      code: "names",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.RuleGroup.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Translations translations={params.row.names} />,
    },
    {
      code: "steps",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.RuleGroup.steps",
      type: "number",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => params.row.counters?.steps || 0,
    },
    {
      code: "counters.rules",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.RuleGroup.counters.rules",
      type: "number",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return (
          <StyledLink to={`${RouteEnum.CONFIGURATION_OPTIMISATION_RULES}?ruleGroup=${params.row.code}`}>
            {params.row.counters?.rules} {t(params.row.counters?.rules > 1 ? "items.Rule._" : "items.Rule._singular")}
          </StyledLink>
        );
      },
    },
  ],
  [ITEM_TYPES.COMPLETUDE]: [
    {
      code: "code",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Completude.code",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <strong>{params.row.code}</strong>,
    },
    {
      code: "names",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Completude.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Translations translations={params.row.names} />,
    },
    {
      code: "type",
      headerType: HEADER_TYPES.PROPERTY,
      label: "enums.DOCUMENT_TYPES._singular",
      type: "text",
      flex: 1,
      renderCell: (params) => <ChipType type={params.row.type} />,
    },
    {
      code: "channel",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.CatalogScope._singular",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return params.row.channel ? params.row.channel.replace("/api/catalog-scopes/", "") : t("items.Product._type");
      },
    },
    {
      code: "attributes",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Completude.attributes",
      type: "number",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => params.row.attributes.length,
    },
  ],
  [ITEM_TYPES.LOCALE]: [
    {
      code: "code",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Locale.code",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <strong>{params.row.code}</strong>,
    },
    {
      code: "names",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Locale.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Translations translations={params.row.names} />,
    },
  ],
  [ITEM_TYPES.CURRENCY]: [
    {
      code: "code",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Currency.code",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <strong>{params.row.code}</strong>,
    },
    {
      code: "names",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Currency.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Translations translations={params.row.names} />,
    },
    {
      code: "symbol",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Currency.symbol",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => params.row.symbol,
    },
  ],
  [ITEM_TYPES.UNIT]: [
    {
      code: "code",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Unit.code",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <StackedLabelCode item={params.row} suffix={params.row.symbol} />,
    },
    {
      code: "measureFamily",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.MeasureFamily._singular",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <StackedLabelCode item={params.row.rowProps.measureFamilies?.find((measure) => measure["@id"] === params.row.measureFamily)} />,
    },
  ],
  [ITEM_TYPES.USER]: [
    {
      code: "avatar",
      headerType: HEADER_TYPES.VIRTUAL_PROPERTY,
      label: "items.User.avatar",
      type: "file",
      hideHeader: true,
      flex: 0,
      width: 60,
      renderCell: (params) => {
        return <AvatarUser user={params.row} />;
      },
    },
    {
      code: "lastname",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.User.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => (
        <Stack gap={0}>
          <Body1>
            <strong>
              {params.row.lastname} {params.row.firstname}
            </strong>
          </Body1>
          <Body1>{params.row.email}</Body1>
        </Stack>
      ),
    },
    {
      code: "userGroups",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.User.userGroups",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => <ChipUsers user={params.row} />,
    },
    {
      code: "sso",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.User.sso",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return params.row.isSso ? t("common.yes") : t("common.no");
      },
    },
    {
      code: "lastLogin",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.User.lastLogin",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Time datetime={params.row.lastLogin} variant="datetime" />,
    },
  ],
  [ITEM_TYPES.USER_LOGIN]: [
    {
      code: "email",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.UserLogin.email",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => <strong>{params.row.email}</strong>,
    },
    {
      code: "count",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.UserLogin.count",
      type: "number",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => params.row.loginCount,
    },
    {
      code: "role",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.UserLogin.role",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return t(`enums.ROLES.${params.row.role}`);
      },
    },
  ],
  [ITEM_TYPES.USER_GROUP_MEMBERSHIP_REQUEST]: [
    {
      code: "avatar",
      headerType: HEADER_TYPES.VIRTUAL_PROPERTY,
      label: "items.UserGroupMembershipRequest.avatar",
      type: "text",
      hideHeader: true,
      width: 60,
      renderCell: (params) => <AvatarUser user={params.row.user} />,
    },
    {
      code: "name",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.UserGroupMembershipRequest.lastname",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => (
        <strong>
          {params.row.user.lastname} {params.row.user.firstname}
        </strong>
      ),
    },
    {
      code: "email",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.UserGroupMembershipRequest.email",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => params.row.user.email,
    },
    {
      code: "createdAt",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.UserGroupMembershipRequest.createdAt",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => <Time datetime={params.row.createdAt} />,
    },
    {
      code: "userGroup",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.UserGroupMembershipRequest.userGroup",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => params.row.group.name,
    },
  ],
  [ITEM_TYPES.EXPORT]: [
    {
      code: "names",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Export.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => (
        <strong>
          <Translations translations={params.row.names} />
        </strong>
      ),
    },
    {
      code: "source",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Export.source",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <ChipType type={params.row.source.type} />,
    },
    {
      code: "format",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Export.format",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return t(`enums.CHANNEL_FORMAT_TYPES.${params.row.format.type}`) + (params.row.format.parameters?.delimiter ? `(${params.row.format.parameters?.delimiter})` : null);
      },
    },
    {
      code: "adapter",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Export.adapter",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return t(`enums.CHANNEL_ADAPTER_TYPES.${params.row.adapter.type}`);
      },
    },
    {
      code: "scheduler",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Export.scheduler",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Schedule schedule={params.row.schedule} lastRun={params.row.lastRun} nextRun={params.row.nextRun} />,
    },
    {
      code: "mapping",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Export.mapping",
      type: "number",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => params.row.mapping.columns.length,
    },
  ],
  [ITEM_TYPES.IMPORT]: [
    {
      code: "names",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Import.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => (
        <strong>
          <Translations translations={params.row.names} />
        </strong>
      ),
    },
    {
      code: "source",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Import.source",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <ChipType type={params.row.source.type} />,
    },
    {
      code: "format",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Import.format",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return t(`enums.CHANNEL_FORMAT_TYPES.${params.row.format.type}`);
      },
    },
    {
      code: "adapter",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Import.adapter",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return t(`enums.CHANNEL_ADAPTER_TYPES.${params.row.adapter.type}`);
      },
    },
    {
      code: "scheduler",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Import.scheduler",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Schedule schedule={params.row.schedule} lastRun={params.row.lastRun} nextRun={params.row.nextRun} />,
    },
    {
      code: "mapping",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Import.mapping",
      type: "number",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => params.row.mapping.columns.length,
    },
  ],
  [ITEM_TYPES.HUB_SOURCE]: [
    {
      code: "upload",
      headerType: HEADER_TYPES.VIRTUAL_PROPERTY,
      label: "items.HubSource.upload",
      type: "file",
      hideHeader: true,
      width: 60,
      renderCell: (params) => <Thumbnail src={params.row.upload} />,
    },
    {
      code: "code",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.HubSource.code",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <strong>{params.row.code}</strong>,
    },
    {
      code: "type",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.HubSource.type",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return t(`enums.HUB_SOURCE_TYPES.${params.row.type}`);
      },
    },
    {
      code: "names",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.HubSource.names",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Translations translations={params.row.names} />,
    },
  ],
  [ITEM_TYPES.HUB_STOCK]: [
    {
      code: "source",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.HubStock.source",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return t(params.row.rowProps.hubSources.find((w) => w["@id"] === params.row.source)?.names);
      },
    },
    {
      code: "product",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.HubStock.product",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => params.row.product,
    },
    {
      code: "productName",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.HubStock.productName",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => params.row.productName ?? <NoResultValue />,
    },
    {
      code: "createdAt",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.HubStock.createdAt",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Time datetime={params.row.createdAt} />,
    },
    {
      code: "quantity",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.HubStock.quantity",
      type: "number",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => params.row.quantity,
    },
  ],
  [ITEM_TYPES.HUB_ORDER]: [
    {
      code: "invoiceId",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.HubOrder.order_number",
      type: "text",
      isSearchable: true,
      renderCell: (params) => `#${params.row.invoiceId}`,
    },
    {
      code: "status",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.HubOrder.status",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <OrdersStates state={params.row.state} />,
    },
    {
      code: "createdAt",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.HubOrder.createdAt",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Time datetime={params.row.createdAt} />,
    },
    {
      code: "origin",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.HubOrder.origin",
      type: "text",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <HubOriginOwnerLogo origin={params.row.origin} owner={params.row.owner} />,
    },
    {
      code: "customer",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.HubOrder.customer",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => `${params.row.shipping.firstname} ${params.row.shipping.lastname}`,
    },
    {
      code: "total",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.HubOrder.total",
      type: "number",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Price price={params.row.price} currency={params.row.currencyCode} />,
    },
  ],
  [ITEM_TYPES.CONFIG]: [
    {
      code: "key",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.Config.key",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();

        return t(`items.Config._types.${params.row.key}._`);
      },
    },
  ],
  [ITEM_TYPES.UPLOAD_IMPORT]: [
    {
      code: "email",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.UserLogin.email",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => <strong>{params.row.userEmail}</strong>,
    },
    {
      code: "createdAt",
      headerType: HEADER_TYPES.PROPERTY,
      label: "actions.created_at",
      type: "date",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Time datetime={params.row.createdAt} />,
    },
    {
      code: "endedAt",
      headerType: HEADER_TYPES.PROPERTY,
      label: "actions.ended_at",
      type: "date",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Time datetime={params.row.endedAt} />,
    },
    {
      code: "type",
      headerType: HEADER_TYPES.PROPERTY,
      label: "enums.DOCUMENT_TYPES._singular",
      type: "text",
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return <ChipPrimaryFilled label={t(`items.${params.row.type}._`)} />;
      },
    },
  ],
  [ITEM_TYPES.UPLOAD_EXPORT]: [
    {
      code: "email",
      headerType: HEADER_TYPES.PROPERTY,
      label: "items.UserLogin.email",
      type: "text",
      // isSearchable: true,
      flex: 1,
      renderCell: (params) => <strong>{params.row.userEmail}</strong>,
    },
    {
      code: "createdAt",
      headerType: HEADER_TYPES.PROPERTY,
      label: "actions.created_at",
      type: "date",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Time datetime={params.row.createdAt} />,
    },
    {
      code: "endedAt",
      headerType: HEADER_TYPES.PROPERTY,
      label: "actions.ended_at",
      type: "date",
      isSearchable: true,
      flex: 1,
      renderCell: (params) => <Time datetime={params.row.endedAt} />,
    },
    {
      code: "type",
      headerType: HEADER_TYPES.PROPERTY,
      label: "enums.DOCUMENT_TYPES._singular",
      type: "text",
      flex: 1,
      renderCell: (params) => {
        const { t } = useTranslation();
        return <ChipPrimaryFilled label={t(`items.${params.row.type}._`)} />;
      },
    },
  ],
};
