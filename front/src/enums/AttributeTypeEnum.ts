export enum AttributeTypeEnum {
  ASSET_COLLECTION = "asset_collection",
  DATE = "date",
  // DATE_TIME= "datetime",
  DECIMAL = "decimal",
  MEDIA = "media",
  MEDIA_COLLECTION = "media_collection",
  PRODUCT = "product",
  PRODUCT_COLLECTION = "product_collection",
  COLOR = "color",
  CONTENT = "content",
  CONTENT_COLLECTION = "content_collection",
  LINK = "link",
  MAIL = "mail",
  METRIC = "metric",
  MULTISELECT = "multiselect",
  NUMBER = "number",
  PRICE = "price",
  PRICE_COLLECTION = "price_collection",
  // REFERENCE= "reference",
  SELECT = "select",
  SWITCH = "switch",
  TEXT_COLLECTION = "text_collection",
  TEXT = "text",
  TEXTAREA = "textarea",
  HTML = "html",
  // GEOJSON= "geojson",
  JSON = "json",
}
