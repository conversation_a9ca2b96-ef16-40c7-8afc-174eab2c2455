const ITEM_TYPES = {
    ATTRIBUTE_GROUP: "AttributeGroup",
    ATTRIBUTE_OPTION: "AttributeOption",
    ATTRIBUTE_SUMMARY: "AttributeSummary",
    ATTRIBUTE: "Attribute",
    AUTH_METHOD: "Auth<PERSON>eth<PERSON>",
    BREADCRUMB_PART: "BreadcrumbPart",
    BREADCRUMB: "Breadcrumb",
    CATALOG_SCOPE_SUMMARY: "CatalogScopeSummary",
    CATALOG_SCOPE_WORKFLOW_STAT: "CatalogScopeWorkflowStat",
    CATALOG_SCOPE_WORKFLOW_STATUS: "CatalogScopeWorkflowStatus",
    CATALOG_SCOPE: "CatalogScope",
    CATALOG_SYNC: "CatalogSync",
    CATALOG: "Catalog",
    CATEGORY: "Category",
    CHANNEL: "Channel",
    COLLECTION: "Collection",
    COMPLETUDE: "Completude",
    CONFIG: "Config",
    CONFIGURATION_SYNC: "ConfigurationSync",
    CONTENT_FOLDER: "ContentFolder",
    CONTENT: "Content",
    COUNTERS: "Counters",
    CURRENCY: "Currency",
    DOCUMENT_ERROR_SUMMARY: "DocumentErrorSummary",
    EXPORT: "Export",
    FILE_EXTRACTOR: "FileExtractor",
    FILTER: "Filter",
    FILTERS_RESOLVE: "FiltersResolve",
    FILTERS: "Filters",
    HUB_INVOICE: "HubInvoice",
    HUB_ORDER: "HubOrder",
    HUB_ORDER_ITEM: "HubOrderItem",
    HUB_STOCK: "HubStock",
    HUB_SOURCE: "HubSource",
    HUB_ORIGIN: "HubOrigin",
    IMPORT:"Import",
    LIGHT_CATALOG_SCOPE: "LightCatalogScope",
    LIGHT_LOCALE: "LightLocale",
    LIGHT_RULE: "LightRule",
    LIGHT_SCOPE: "LightScope",
    LOCALE: "Locale",
    MAGENTO_CONFIG: 'MagentoConfig',
    MEASURE_FAMILY: "MeasureFamily",
    MEDIA_FOLDER: "MediaFolder",
    MEDIA: "Media",
    PASSWORD_RESET_START: "PasswordResetStart",
    PASSWORD_RESET_TOKEN: "PasswordResetToken",
    PASSWORD_RESET: "PasswordReset",
    PASSWORD_UPDATE: "PasswordUpdate",
    PLATFORM_STATISTIC: "PlatformStatistic",
    PRODUCT_SCOPE: "ProductScope",
    PRODUCT_SYNC: "ProductSync",
    PRODUCT: "Product",
    PRODUCT_DATA: "ProductData",
    REQUESTABLE_USER_GROUP: "RequestableUserGroup",
    RULE_GROUP: "RuleGroup",
    RULE_TEST: "RuleTest",
    RULE: "Rule",
    SCOPE: "Scope",
    SETTINGS: "Settings",
    TEMPLATE: "Template",
    TOKEN_RENEW: "TokenRenew",
    TOKEN: "Token",
    TRANSLATION: "Translation",
    TRANSLATIONS: "Translations",
    UNIT: "Unit",
    UPLOAD: "Upload",
    UPLOAD_EXPORT: "UploadExport",
    UPLOAD_IMPORT: "UploadImport",
    USER_GROUP_MEMBERSHIP_REQUEST_HANDLE: "UserGroupMembershipRequestHandle",
    USER_GROUP_MEMBERSHIP_REQUEST_SUMMARY: "UserGroupMembershipRequestSummary",
    USER_GROUP_MEMBERSHIP_REQUEST: "UserGroupMembershipRequest",
    USER_GROUP_MEMBERSHIP: "UserGroupMembership",
    USER_GROUP_SCOPE: "UserGroupScope",
    USER_GROUP: "UserGroup",
    USER_LOGIN: "UserLogin",
    USER: "User",
    VALUE_DATA: "ValueData",
    VALUE: "Value",
    VALUES: "Values",
    WORKFLOW_GLOBAL_STAT: "WorkflowGlobalStat",
    WORKFLOW_SPECIFIC_STAT_MODEL: "WorkflowSpecificStatModel",
    WORKFLOW_SPECIFIC_STAT: "WorkflowSpecificStat",
    WORKFLOW_STEP_RULE_GROUP: "WorkflowStepRuleGroup",
    WORKFLOW_STEP_SUMMARY: "WorkflowStepSummary",
    WORKFLOW_STEP: "WorkflowStep",
    WORKFLOW: "Workflow",
};

  export default ITEM_TYPES;
