// @see router/routes.config.js
export enum RouteEnum {
  ERROR_CODE = "/error-code",
  LOADING = "/loading",
  LOGOUT = "/logout",
  // firewall
  LOGIN = "/login",
  LOGIN_AFTER = "/login/after",
  FORGOTTEN_PASSWORD = "/password/forget",
  RESET_PASSWORD = "/password/reset",
  RESET_PASSWORD_TOKEN = "/password/reset/:token",
  // user
  ACCOUNT = "/account",
  // user (valid)
  HOME = "/",
  THEME = "/theme",
  ONBOARDING = "/onboarding",
  PERMALINK = "/permalink",

  // CENTRALISATION
  CENTRALISATION = "/centralisation",
  PRODUCTS = "/centralisation/products",
  PRODUCTS_DETAIL = "/centralisation/products/:sku/:catalogScope?/:locale?",
  DAM = "/centralisation/medias",
  DAM_DETAIL = "/centralisation/medias/:uuid",
  CONTENTS = "/centralisation/contents",
  CONTENT_DETAIL = "/centralisation/contents/:uuid",
  CONTENTS_ADD = "/centralisation/contents/add",

  // DIFFUSION
  DIFFUSION = "/diffusion",
  CATALOGS = "/diffusion/catalogs",
  CATALOGS_DETAIL = "/diffusion/catalogs/:code",
  SCOPES = "/diffusion/scopes",
  CHANNELS = "/diffusion/channels",
  CHANNELS_DETAIL = "/diffusion/channels/:code",
  EXPORTS = "/exports",
  IMPORTS = "/imports",

  // OPTIMISATION
  OPTIMIZATION = "/optimisation",
  OPTIMIZATION_OVERVIEW = "/optimisation/overview",
  OPTIMIZATION_ERRORS = "/optimisation/flow",

  // HUB
  HUB = "/hub",
  HUB_DASHBOARD = "/hub/dashboard",
  HUB_DASHBOARD_SALES_REPORT = "/hub/dashboard/sales-report",
  HUB_DASHBOARD_STOCK_REPORT = "/hub/dashboard/stock-report",
  HUB_DASHBOARD_ORDER_REPORT = "/hub/dashboard/order-report",
  HUB_ORDERS = "/hub/orders",
  HUB_ORDERS_DETAIL = "/hub/orders/:id",
  HUB_STOCKS = "/hub/stocks",
  HUB_SOURCES = "/hub/sources",
  HUB_SOURCES_DETAIL = "/hub/sources/:code",

  DOCUMENTATION = "/help",

  // USERS
  USERS = "/users",
  USERS_LIST = "/users/list",
  USERS_DETAIL = "/users/:id",
  USERS_GROUPS = "/users/user-groups",
  USERS_GROUPS_DETAIL = "/users/users-groups/:id",
  USERS_REQUESTS = "/users/user-requests",
  USERS_STATS = "/users/user-stats",

  // CONFIGURATION
  CONFIG = "/configs",

  // CONFIGURATION_CENTRALISATION
  CONFIGURATION_CENTRALISATION = "/configuration/centralisation",
  CONFIGURATION_CENTRALISATION_TEMPLATES = "/configuration/centralisation/templates",
  CONFIGURATION_CENTRALISATION_ATTRIBUTE_GROUPS = "/configuration/centralisation/attribute-groups",
  CONFIGURATION_CENTRALISATION_ATTRIBUTE_GROUPS_READ = "/configuration/centralisation/attribute-groups/:id",
  CONFIGURATION_CENTRALISATION_ATTRIBUTES = "/configuration/centralisation/attributes",
  CONFIGURATION_CENTRALISATION_ATTRIBUTE_OPTIONS = "/configuration/centralisation/attribute-options",
  CONFIGURATION_CENTRALISATION_COMPLENESS = "/configuration/optimisation/completeness",

  // CONFIGURATION_OPTIMISATION
  CONFIGURATION_OPTIMISATION = "/configuration/optimisation",
  CONFIGURATION_OPTIMISATION_RULE_GROUPS = "/configuration/optimisation/rule-groups",
  CONFIGURATION_OPTIMISATION_RULES = "/configuration/optimisation/rules",
  CONFIGURATION_OPTIMISATION_WORKFLOWS = "/configuration/optimisation/workflows",
  CONFIGURATION_OPTIMISATION_WORKFLOW_DETAIL = "/configuration/optimisation/workflows/:id",

  // PREFERENCES
  PREFERENCES = "/preferences",
  PREFERENCES_AFFICHAGE = "/preferences/affichage",
  PREFERENCES_AFFICHAGE_PRODUCTS = "/preferences/affichage/products",
  PREFERENCES_AFFICHAGE_MEDIAS = "/preferences/affichage/medias",
  PREFERENCES_AFFICHAGE_CONTENTS = "/preferences/affichage/contents",
  PREFERENCES_AFFICHAGE_CATALOGS = "/preferences/affichage/catalogs",
  PREFERENCES_LOCALES = "/preferences/locales",
  PREFERENCES_CURRENCIES = "/preferences/currencies",
  PREFERENCES_UNITS = "/preferences/units",
  PREFERENCES_COMMUNICATION = "/preferences/communication",

  UPLOAD_IMPORTS = "/upload-imports",
  UPLOAD_EXPORTS = "/upload-exports",
  UPLOAD_IMPORTS_DETAIL = "/upload-imports/:uuid",
  UPLOAD_EXPORTS_DETAIL = "/upload-exports/:uuid",
  CHANNELS_V2 = "/diffusion/channels-v2",
  CHANNELS_DETAIL_V2 = "/diffusion/channels-v2/:code",
}
