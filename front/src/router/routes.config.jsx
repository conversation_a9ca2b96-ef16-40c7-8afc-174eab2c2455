import AttributeOption from "@/router/pages/settings/centralization/AttributeOption";
import AttributeGroup from "@/router/pages/settings/centralization/AttributeGroup";
import Attribute from "@/router/pages/settings/centralization/Attribute";
import Catalog from "@/router/pages/diffusion/catalogs/Catalog";
import Errors from "@/router/pages/optimization/Errors";
import Locale from "@/router/pages/settings/preferences/Locale";
import ProductDetails from "@/router/pages/centralization/products/ProductDetails";
import Rule from "@/router/pages/settings/optimization/Rule";
import Scope from "@/router/pages/diffusion/Scope";
import UserStat from "@/router/pages/settings/users/UserStat";
import AdminRoute from "@/router/firewall/AdminRoute";
import AnonymousRoute from "@/router/firewall/AnonymousRoute";
import SuperAdminRoute from "@/router/firewall/SuperAdminRoute";
import UserReadyRoute from "@/router/firewall/UserReadyRoute";
import UserRoute from "@/router/firewall/UserRoute";
import Error from "@/router/pages/anonymous/Error";
import Loading from "@/router/pages/anonymous/Loading";
import Logout from "@/router/pages/anonymous/Logout";
import NotFound from "@/router/pages/anonymous/NotFound";
import ForgottenPassword from "@/router/pages/anonymous/ForgottenPassword";
import LoginAfter from "@/router/pages/anonymous/LoginAfter";
import Login from "@/router/pages/anonymous/Login";
import ResetPasswordToken from "@/router/pages/anonymous/ResetPassword";
import Home from "@/router/pages/Home";
import Theme from "@/router/pages/Theme";
import Account from "@/router/pages/Account";
import HubStock from "@/router/pages/hub/HubStock";
import Channel from "@/router/pages/diffusion/channels/Channel";
import RuleGroup from "@/router/pages/settings/optimization/RuleGroup";
import Workflow from "@/router/pages/settings/optimization/Workflow";
import UserRequest from "@/router/pages/settings/users/UserRequest";
import Content from "@/router/pages/centralization/contents/Content";
import User from "@/router/pages/settings/users/User";
import HubSource from "@/router/pages/hub/hubSources/HubSource";
import HubOrder from "@/router/pages/hub/hubOrders/HubOrder";
import HubOrderDetails from "@/router/pages/hub/hubOrders/HubOrderDetails";
import Currency from "@/router/pages/settings/preferences/Currency";
import Template from "@/router/pages/settings/centralization/Template";
import Product from "@/router/pages/centralization/products/Product";
import CatalogDetails from "@/router/pages/diffusion/catalogs/CatalogDetails";
import Unit from "@/router/pages/settings/preferences/Unit";
import Wip from "@/router/pages/Wip";
import Media from "@/router/pages/centralization/medias/Media";
import UserGroup from "@/router/pages/settings/users/UserGroup";
import MediaDetails from "@/router/pages/centralization/medias/MediaDetails";
import ContentDetails from "@/router/pages/centralization/contents/ContentDetails";
import DocumentLayout from "@/router/pages/settings/layout/DocumentLayout";
import Export from "@/router/pages/diffusion/Export";
import DOCUMENT_TYPES_MINI from "@/enums/DOCUMENT_TYPES_MINI";
import Completude from "@/router/pages/settings/optimization/Completude";
import Import from "@/router/pages/diffusion/Import";
import HubSourceDetails from "@/router/pages/hub/hubSources/HubSourceDetails";
import Onboarding from "@/router/pages/Onboarding";
import PermaLinkPage from "@/router/pages/PermaLinkPage";
import LayoutHubDashboard from "./pages/hub/LayoutHubDashboard";
import Configuration from "./pages/settings/preferences/Configuration";
import ChannelDetail from "@/router/pages/diffusion/channels/ChannelDetails";
import HubProvider from "@/contexts/HubProvider";
import UploadHistory from "./pages/settings/others/UploadHistory";
import UploadImportDetails from "@/router/pages/settings/others/UploadImportDetails";
import Optimization from "./pages/optimization/Optimization";
import WorkflowStep from "./pages/settings/optimization/workflows/WorkflowStep";
import UploadExportDetails from "@/router/pages/settings/others/UploadExportDetails";
import Components from "@/router/pages/Components";
import ChannelV2 from "@/router/pages/diffusion/channelsV2/ChannelV2.js";
import ChannelDetailsV2 from "@/router/pages/diffusion/channelsV2/ChannelDetailsV2.js";
import { RouteEnum } from "@/enums";

/**
 * @typedef {path:string,element:function,children:undefined|RouterRoute[]} RouterRoute
 * @type RouterRoute[]
 */
const ROUTER_ROUTES = [
  {
    path: RouteEnum.ERROR_CODE,
    element: <Error />,
  },
  {
    path: RouteEnum.LOADING,
    element: <Loading />,
  },
  {
    path: RouteEnum.LOGOUT,
    element: <Logout />,
  },
  {
    // from here: a firewall starts!
    path: null,
    element: <AnonymousRoute />,
    children: [
      {
        path: RouteEnum.LOGIN,
        element: <Login />,
      },
      {
        path: RouteEnum.LOGIN_AFTER,
        element: <LoginAfter />,
      },
      {
        path: RouteEnum.FORGOTTEN_PASSWORD,
        element: <ForgottenPassword />,
      },
      {
        path: RouteEnum.RESET_PASSWORD_TOKEN,
        element: <ResetPasswordToken />,
      },
    ],
  },
  {
    // from here: a user must be logged in
    path: null,
    element: <UserRoute />,
    code: "drawer.home",
    children: [
      {
        path: RouteEnum.ACCOUNT,
        element: <Account />,
      },
      {
        // from here: only a ready user is accepted (or admin...)
        path: null,
        element: <UserReadyRoute />,
        children: [
          {
            path: RouteEnum.HOME,
            element: <Home />,
          },
          {
            path: RouteEnum.DOCUMENTATION,
            element: <Wip />,
          },
          {
            path: RouteEnum.THEME,
            element: <Theme />,
          },
          {
            path: RouteEnum.ONBOARDING,
            element: <Onboarding />,
          },
          // Temporary route to test exports
          {
            path: RouteEnum.EXPORTS,
            element: <Export />,
          },
          {
            path: RouteEnum.IMPORTS,
            element: <Import />,
          },
          {
            path: RouteEnum.PERMALINK,
            element: <PermaLinkPage />,
          },
          {
            path: RouteEnum.CATALOGS,
            element: <Catalog />,
          },
          {
            path: RouteEnum.CATALOGS_DETAIL,
            element: <CatalogDetails />,
          },
          {
            path: RouteEnum.PRODUCTS,
            element: <Product />,
          },
          {
            path: RouteEnum.PRODUCTS_DETAIL,
            element: <ProductDetails />,
          },
          {
            path: RouteEnum.SCOPES,
            element: <Scope />,
          },
          {
            path: RouteEnum.CHANNELS,
            element: <Channel />,
          },
          {
            path: RouteEnum.CHANNELS_DETAIL,
            element: <ChannelDetail />,
          },
          {
            path: RouteEnum.CHANNELS_V2,
            element: <ChannelV2 />,
          },
          {
            path: RouteEnum.CHANNELS_DETAIL_V2,
            element: <ChannelDetailsV2 />,
          },
          {
            path: RouteEnum.OPTIMIZATION_OVERVIEW,
            element: <Optimization />,
          },
          {
            path: RouteEnum.OPTIMIZATION_ERRORS,
            element: <Errors />,
          },
          {
            path: RouteEnum.HUB_DASHBOARD,
            element: (
              <HubProvider>
                <LayoutHubDashboard />
              </HubProvider>
            ),
          },
          {
            path: RouteEnum.HUB_SOURCES,
            element: <HubSource />,
          },
          {
            path: RouteEnum.HUB_SOURCES_DETAIL,
            element: <HubSourceDetails />,
          },
          {
            path: RouteEnum.HUB_STOCKS,
            element: <HubStock />,
          },
          {
            path: RouteEnum.HUB_ORDERS,
            element: <HubOrder />,
          },
          {
            path: RouteEnum.HUB_ORDERS_DETAIL,
            element: <HubOrderDetails />,
          },
          {
            path: RouteEnum.DAM,
            element: <Media />,
          },
          {
            path: RouteEnum.DAM_DETAIL,
            element: <MediaDetails />,
          },
          {
            path: RouteEnum.CONTENTS,
            element: <Content />,
          },
          {
            path: RouteEnum.CONTENT_DETAIL,
            element: <ContentDetails />,
          },
        ],
      },
      {
        // only admins
        path: null,
        element: <AdminRoute />,
        children: [
          {
            path: RouteEnum.USERS_GROUPS,
            element: <UserGroup />,
          },
          {
            path: RouteEnum.USERS_REQUESTS,
            element: <UserRequest />,
          },
        ],
      },
      {
        // only super-admins
        path: null,
        element: <SuperAdminRoute />,
        children: [
          {
            path: "/components",
            element: <Components />,
          },
          {
            path: RouteEnum.PREFERENCES_LOCALES,
            element: <Locale />,
          },
          {
            path: RouteEnum.CONFIGURATION_CENTRALISATION_ATTRIBUTE_GROUPS,
            element: <AttributeGroup />,
          },
          {
            path: RouteEnum.CONFIGURATION_CENTRALISATION_ATTRIBUTE_GROUPS_READ,
            element: <Attribute />,
          },
          {
            path: RouteEnum.CONFIGURATION_CENTRALISATION_ATTRIBUTES,
            element: <Attribute />,
          },
          {
            path: RouteEnum.CONFIGURATION_CENTRALISATION_ATTRIBUTE_OPTIONS,
            element: <AttributeOption />,
          },
          {
            path: RouteEnum.CONFIGURATION_OPTIMISATION_RULE_GROUPS,
            element: <RuleGroup />,
          },
          {
            path: RouteEnum.CONFIGURATION_OPTIMISATION_RULES,
            element: <Rule />,
          },
          {
            path: RouteEnum.CONFIGURATION_OPTIMISATION_WORKFLOWS,
            element: <Workflow />,
          },
          {
            path: RouteEnum.CONFIGURATION_OPTIMISATION_WORKFLOW_DETAIL,
            element: <WorkflowStep />,
          },
          {
            path: RouteEnum.CONFIGURATION_CENTRALISATION_COMPLENESS,
            element: <Completude />,
          },
          {
            path: RouteEnum.USERS_LIST,
            element: <User />,
          },
          {
            path: RouteEnum.USERS_REQUESTS,
            element: <UserRequest />,
          },
          {
            path: RouteEnum.USERS_GROUPS,
            element: <UserGroup />,
          },
          {
            path: RouteEnum.USERS_STATS,
            element: <UserStat />,
          },
          {
            path: RouteEnum.PREFERENCES_CURRENCIES,
            element: <Currency />,
          },
          {
            path: RouteEnum.CONFIGURATION_CENTRALISATION_TEMPLATES,
            element: <Template />,
          },
          {
            path: RouteEnum.PREFERENCES_UNITS,
            element: <Unit />,
          },
          {
            path: RouteEnum.PREFERENCES_AFFICHAGE_PRODUCTS,
            element: <DocumentLayout type={DOCUMENT_TYPES_MINI.PRODUCT} />,
          },
          {
            path: RouteEnum.PREFERENCES_AFFICHAGE_MEDIAS,
            element: <DocumentLayout type={DOCUMENT_TYPES_MINI.MEDIA} />,
          },
          {
            path: RouteEnum.PREFERENCES_AFFICHAGE_CONTENTS,
            element: <DocumentLayout type={DOCUMENT_TYPES_MINI.CONTENT} />,
          },
          {
            path: RouteEnum.PREFERENCES_AFFICHAGE_CATALOGS,
            element: <Wip />,
          },
          {
            path: RouteEnum.PREFERENCES_COMMUNICATION,
            element: <Wip />,
          },
          {
            path: RouteEnum.CONFIG,
            element: <Configuration />,
          },
          {
            path: RouteEnum.UPLOAD_IMPORTS,
            element: <UploadHistory />,
          },
          {
            path: RouteEnum.UPLOAD_EXPORTS,
            element: <UploadHistory />,
          },
          {
            path: RouteEnum.UPLOAD_IMPORTS_DETAIL,
            element: <UploadImportDetails />,
          },
          {
            path: RouteEnum.UPLOAD_EXPORTS_DETAIL,
            element: <UploadExportDetails />,
          },
        ],
      },
    ],
  },
  {
    path: "*",
    element: <NotFound />,
  },
];

export default ROUTER_ROUTES;
