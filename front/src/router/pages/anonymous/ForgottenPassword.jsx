import { Body1, H3 } from "@/components/ui/Typography";
import { Box, Stack, TextField } from "@mui/material";
import { useTranslation } from "@/hooks/useTranslation";
import { useNavigate } from "react-router";
import { useUi } from "@/contexts/UiProvider";
import { useSnack } from "@/contexts/SnackProvider";
import OurReturnLink from "@/theme/components/ReturnLink";
import sinfinLogo from "@/assets/images/logo-sinfin-digital.svg";
import InputAdornment from "@mui/material/InputAdornment";
import { useState } from "react";
import { useApi } from "@/contexts/ApiProvider";
import { Person } from "@mui/icons-material";
import { ButtonPrimary } from "@/components/ui/Button";
import { RouteEnum } from "@/enums";

export default function ForgottenPassword() {
  const snack = useSnack();
  const { startLoading, stopLoading } = useUi();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const api = useApi();

  const [account, setAccount] = useState(() => ({}));

  const onSubmit = async (e) => {
    e.preventDefault();
    try {
      startLoading();
      await api.post("/api/password-reset-starts", account);
      snack.success("inputs.success_email");
      navigate(RouteEnum.LOGIN);
    } catch (error) {
      console.log(error);
      snack.error("common.error");
    } finally {
      stopLoading();
    }
  };

  return (
    <Stack>
      <Box>
        <OurReturnLink />
      </Box>
      <Stack alignItems="center">
        <img src={sinfinLogo} width="200" alt="SINFIN" />
        <H3>{t("login.forget_password_title")}</H3>
      </Stack>
      <Body1>{t("login.forget_password_text")}</Body1>
      <form onSubmit={onSubmit}>
        <Stack>
          <TextField
            type="email"
            name="email"
            autoComplete="username"
            label={t("inputs.email")}
            size="medium"
            onChange={(e) => setAccount((p) => ({ ...p, email: e.target.value }))}
            slotProps={{
              input: {
                placeholder: "<EMAIL>",
                startAdornment: (
                  <InputAdornment position="start">
                    <Person />
                  </InputAdornment>
                ),
              },
            }}
          />
          <ButtonPrimary type="submit" size="large">
            {t("login.forget_password_button")}
          </ButtonPrimary>
        </Stack>
      </form>
    </Stack>
  );
}
