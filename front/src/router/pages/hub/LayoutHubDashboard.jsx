import { Grid2, Stack } from "@mui/material";
import { useSearchParams } from "react-router";
import HubDashboard from "@/components/hubDashboard/HubDashboard";
import HubSales from "@/components/hubDashboard/HubSales";
import HubOrders from "@/components/hubDashboard/HubOrders";
import "dayjs/locale/fr";
import "dayjs/locale/en-gb";
import DateRangePicker from "@/components/ui/inputs/DateRangePicker";
import { useTranslation } from "@/hooks/useTranslation";
import { useHub } from "@/contexts/HubProvider";
import Tabs from "@/components/ui/tabs/Tabs";

export default function LayoutHubDashboard() {
  const { t } = useTranslation();
  const { handleParams, dateFrom, setDateFrom, dateTo, setDateTo } = useHub();

  const [searchParams, setSearchParams] = useSearchParams();

  const tab = searchParams.get("tab");

  const tabs = [{
    label: t("hubDashboard._"),
    tab: "",
    component: <HubDashboard />,
  }, {
    label: t("hubDashboard.salesReports"),
    tab: "sales",
    component: <HubSales />,
  }, {
    label: t("hubDashboard.orderReports"),
    tab: "orders",
    component: <HubOrders />,
  }];

  const selectedTab = tab ? tabs.findIndex((tb) => tb.tab === tab) : 0;

  return (
    <Stack gap={0} position="relative">
      <Stack position="sticky" top={52} zIndex={10} bgcolor="background.main" p={2}>
        <Tabs
          tabs={tabs}
          defaultTab={selectedTab}
          onTabChange={(v) => {
            handleParams("tab", v.tab);
          }}
        >
          <Grid2 container width={1} spacing={2}>
            <Grid2 container spacing={2} size={12}>
              {/* <Grid2 item xs>
              <SelectHubSource
                getterKey="@id"
                noLabel
                defaultValue={skus}
                onChange={(value) => {
                  setSource(value);
                  handleParams("source", value);
                }}
                byCode
              />
            </Grid2> */}
              <Grid2 size={3}>
                <DateRangePicker
                  dateFrom={dateFrom}
                  dateTo={dateTo}
                  onChange={(newValue) => {
                    setDateFrom(newValue[0]);
                    setDateTo(newValue[1]);
                    setSearchParams((prevParams) => {
                      const newParams = new URLSearchParams(prevParams);
                      if (newValue[0]) {
                        newParams.set("from", newValue[0]);
                      } else {
                        newParams.delete("from");
                      }
                      if (newValue[1]) {
                        newParams.set("to", newValue[1]);
                      } else {
                        newParams.delete("to");
                      }

                      return newParams;
                    });
                  }}
                />
              </Grid2>
            </Grid2>
          </Grid2>
        </Tabs>
      </Stack>
    </Stack>
  );
}
