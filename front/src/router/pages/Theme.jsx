/* eslint-disable */

import TopPanel from "@/components/ui/topPanel/TopPanel";
import TextFieldsIcon from "@mui/icons-material/TextFields";

import Typography, { Body1, H3, H5, Subtitle2 } from "@/components/ui/Typography";
import { Box, Checkbox, Divider, FormControlLabel, Stack } from "@mui/material";
import { ColorPicker, Input, MediaInput, SelectInput } from "@/components/ui/inputs/index";
import CheckIconSwitch from "@/components/ui/inputs/AntSwitch";
import { useState } from "react";
import { useTranslation } from "@/hooks/useTranslation";
import Wysiwyg from "@/components/ui/inputs/wysiwyg/Wysiwyg";
import Geo from "@/components/ui/inputs/geoJson/Geo";
import palette from "@/theme/palette";
import BoxColorTheme from "@/theme/BoxColorTheme";
import rules_functions from "@docs/rules_functions";
import RuleFunction from "@/components/rules/RuleFunction";
import StackedInfos from "@/components/ui/StackedInfos";
import Translations from "@/components/ui/translation/Translations";
import AttributesParametersMapper from "@/components/ui/inputs/parameters/AttributeParameters";
import ATTRIBUTE_PARAMETERS_MAP from "@/enums/ATTRIBUTE_PARAMETERS_MAP";
import { ButtonError, ButtonPrimary, ButtonSecondary, ButtonSuccess } from "@/components/ui/Button";
import { AttributeParameterEnum } from "@/enums";

export default function Theme() {
  const { t } = useTranslation();

  const typoVariants = ["h1", "h2", "h3", "h4", "h5", "h6", "subtitle1", "subtitle2", "body1", "body2", "button", "caption", "overline"];
  const typoSizes = ["2rem", "1.75rem", "1.5rem", "1.25rem", "1rem", ".875rem", ".875rem", ".7rem", ".875rem", ".875rem", "N/A", "N/A", "N/A"];

  const btnVariants = ["contained", "outlined", "text"];
  const btnSizes = ["small", "medium", "large"];

  const [isChecked, setIsChecked] = useState(true);

  const colorOrder = ["lighter", "light", "main", "dark", "darker", "contrastText"];

  return (
    <Box style={{ overflowY: "scroll", scrollBehavior: "smooth" }}>
      <TopPanel title="Design System" />
      <section>
        <ul>
          <li>
            <a href="#typography">
              <TextFieldsIcon />
              <span>Typography:</span>
            </a>
          </li>
          <li>
            <a href="#buttons">
              <span>Buttons</span>
            </a>
          </li>
          <li>
            <a href="#inputs">
              <span>Inputs</span>
            </a>
          </li>
          <li>
            <a href="#colors">
              <span>Colors</span>
            </a>
          </li>
          <li>
            <a href="#geojson">
              <span>GeoJSON</span>
            </a>
          </li>
        </ul>
      </section>
      <section style={{ padding: 15, display: "flex", flexDirection: "column", gap: 20, alignItems: "start" }}>
        <Stack direction="row" gap={8} flexWrap="wrap">
          <table style={{ borderCollapse: "collapse", width: "max-content" }}>
            <thead>
              <tr>
                <th style={{ padding: 5 }}>Attributs</th>
                <th style={{ padding: 5 }}>Inputs</th>
              </tr>
            </thead>
            <tbody>
              {Object.keys(ATTRIBUTE_PARAMETERS_MAP).map(attribut => (
                <tr key={attribut} style={{ border: "1px solid #ddd", padding: "8px" }}>
                  <td style={{ border: "1px solid #ddd", padding: "8px" }}>
                    <strong style={{ border: "1px solid #ddd", padding: "0 5px", borderRadius: "5px" }}>{attribut}</strong>
                    <Subtitle2>{ATTRIBUTE_PARAMETERS_MAP[attribut].join(", ")}</Subtitle2>
                  </td>
                  <td style={{ border: "1px solid #ddd", padding: "8px" }}>
                    <Stack gap={0} alignItems="start">
                      {ATTRIBUTE_PARAMETERS_MAP[attribut].map((parameter) => (
                        <AttributesParametersMapper key={attribut} type={parameter} />
                      ))}
                    </Stack>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          <table style={{ borderCollapse: "collapse", width: "400px", height: "min-content" }}>
            <thead>
              <tr>
                <th style={{ padding: 5 }}>Parameter</th>
                <th style={{ padding: 5 }}>Input</th>
              </tr>
            </thead>
            <tbody>
              {Object.values(AttributeParameterEnum).map(parameter => (
                <tr key={parameter} style={{ border: "1px solid #ddd", padding: "8px" }}>
                  <td style={{ border: "1px solid #ddd", padding: "8px" }}>
                    <strong style={{ border: "1px solid #ddd", padding: "0 5px", borderRadius: "5px" }}>{parameter}</strong>
                  </td>
                  <td style={{ border: "1px solid #ddd", padding: "8px" }}>
                    <AttributesParametersMapper type={parameter} />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </Stack>
      </section>
      <section style={{ padding: 15, display: "flex", flexDirection: "column", gap: 20, alignItems: "start" }}>
        <H3 id="colors" style={{ textDecoration: "underline" }}>
          Colors:
        </H3>
        <Stack gap={1}>
          {/* [lighter, light, main, dark, darker, contrastText] */}
          {Object.keys(palette).map((color) => (
            <Stack key={color} display="grid" gridTemplateColumns="1fr 7fr" gap={2}>
              <Typography mt={1.5}>
                <strong>{color}</strong>
              </Typography>
              <Stack direction="row" gap={1}>
                {typeof palette[color] === "object" ? (
                  Object.keys(palette[color])
                    .sort((a, b) => colorOrder.indexOf(a) - colorOrder.indexOf(b))
                    .map((shade, index) => <BoxColorTheme color={palette[color][shade]} title={shade} key={index} />)
                ) : (
                  <BoxColorTheme color={palette[color]} title={color} />
                )}
              </Stack>
            </Stack>
          ))}
        </Stack>
      </section>
      <section style={{ padding: 15 }}>
        <H3 id="typography" style={{ textDecoration: "underline" }} gutterBottom>
          Typography:
        </H3>
        <Body1 gutterBottom>
          Font family <strong>Open Sans, sans-serif</strong>
        </Body1>
        <table style={{ borderCollapse: "collapse", width: "100%" }}>
          <thead>
            <tr>
              <th style={{ padding: 5 }}>Variant</th>
              <th style={{ padding: 5 }}>Size</th>
              <th style={{ padding: 5 }}>Inherit</th>
              <th style={{ padding: 5 }}>Primary</th>
              <th style={{ padding: 5 }}>Secondary</th>
              <th style={{ padding: 5 }}>Error</th>
            </tr>
          </thead>
          <tbody>
            {typoVariants.map((variant, index) => (
              <tr key={`${variant}-${index}`} style={{ border: "1px solid #ccc", padding: "8px" }}>
                <td style={{ border: "1px solid #ccc", padding: "8px" }}>{variant}</td>
                <td style={{ border: "1px solid #ccc", padding: "8px" }}>{typoSizes[index]}</td>
                <td style={{ border: "1px solid #ccc", padding: "8px" }}>
                  <Typography variant={variant}>Lorem ipsum dolor</Typography>
                </td>
                <td style={{ border: "1px solid #ccc", padding: "8px" }}>
                  <Typography color="warning" variant={variant}>
                    Lorem ipsum dolor
                  </Typography>
                </td>
                <td style={{ border: "1px solid #ccc", padding: "8px" }}>
                  <Typography color="secondary" variant={variant}>
                    Lorem ipsum dolor
                  </Typography>
                </td>
                <td style={{ border: "1px solid #ccc", padding: "8px" }}>
                  <Typography color="error" variant={variant}>
                    Lorem ipsum dolor
                  </Typography>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </section>
      <Divider style={{ margin: 30 }} />
      <section style={{ padding: 15, display: "flex", flexDirection: "column", gap: 10 }}>
        <H3 id="buttons" style={{ textDecoration: "underline" }}>
          Buttons:
        </H3>
        <Body1>
          Sizes available: <strong>small, medium, large</strong>
        </Body1>
        <div style={{ overflowX: "auto" }}>
          <table style={{ borderCollapse: "collapse", width: "100%" }}>
            <thead>
              <tr>
                <th style={{ padding: 5 }}>Variant</th>
                <th style={{ padding: 5 }}>Inherit</th>
                <th style={{ padding: 5 }}>Primary</th>
                <th style={{ padding: 5 }}>Success</th>
                <th style={{ padding: 5 }}>Error</th>
              </tr>
            </thead>
            <tbody>
              {btnVariants.map((variant, index) => (
                <tr key={`${variant}-${index}`} style={{ border: "1px solid #ccc", padding: "8px" }}>
                  <td style={{ border: "1px solid #ccc", padding: "8px" }}>{variant}</td>
                  <td style={{ border: "1px solid #ccc", padding: "8px" }}>
                    <div style={{ display: "flex", flexWrap: "wrap", gap: 10, justifyContent: "center", alignItems: "start" }}>
                      {btnSizes.map((size, i) => (
                        <div key={`${size}-${i}`} style={{ display: "flex", flexDirection: "column", gap: 5, alignItems: "center" }}>
                          <ButtonPrimary variant={variant} size={size}>
                            Default
                          </ButtonPrimary>
                        </div>
                      ))}
                    </div>
                  </td>
                  <td style={{ border: "1px solid #ccc", padding: "8px" }}>
                    <div style={{ display: "flex", flexWrap: "wrap", gap: 10, justifyContent: "center", alignItems: "start" }}>
                      {btnSizes.map((size, i) => (
                        <div key={`${size}-${i}`} style={{ display: "flex", flexDirection: "column", gap: 5, alignItems: "center" }}>
                          <ButtonPrimary variant={variant} size={size}>
                            Primary
                          </ButtonPrimary>
                        </div>
                      ))}
                    </div>
                  </td>
                  <td style={{ border: "1px solid #ccc", padding: "8px" }}>
                    <div style={{ display: "flex", flexWrap: "wrap", gap: 10, justifyContent: "center", alignItems: "start" }}>
                      {btnSizes.map((size, i) => (
                        <div key={`${size}-${i}`} style={{ display: "flex", flexDirection: "column", gap: 5, alignItems: "center" }}>
                          <ButtonSuccess variant={variant} size={size}>
                            Success
                          </ButtonSuccess>
                        </div>
                      ))}
                    </div>
                  </td>
                  <td style={{ border: "1px solid #ccc", padding: "8px" }}>
                    <div style={{ display: "flex", flexWrap: "wrap", gap: 10, justifyContent: "center", alignItems: "start" }}>
                      {btnSizes.map((size, i) => (
                        <div key={`${size}-${i}`} style={{ display: "flex", flexDirection: "column", gap: 5, alignItems: "center" }}>
                          <ButtonError variant={variant} size={size}>
                            Error
                          </ButtonError>
                        </div>
                      ))}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div>
          <H5>Others customs</H5>
          <div style={{ display: "flex", gap: 10 }}>
            <ButtonSecondary>Secondary</ButtonSecondary>
          </div>
        </div>
      </section>
      <Divider style={{ margin: 30 }} />
      <section style={{ padding: 15, display: "flex", flexDirection: "column", gap: 10, alignItems: "start" }}>
        <H3 id="inputs" style={{ textDecoration: "underline" }}>
          Inputs:
        </H3>
        <Body1>
          Inputs available: <strong>select, text, textarea, number, password, date, image, checkbox, radio, multiselect, color, switch</strong>
        </Body1>
        <Body1>
          Sizes available: <strong>small and normal</strong>, small by default.
        </Body1>
        <H5 style={{ marginTop: 10, padding: "5px 0", borderBottom: "1px solid" }}>
          <i>Text inputs:</i>
        </H5>
        <div style={{ display: "flex", flexWrap: "wrap", gap: 15 }}>
          <div style={{ display: "flex", flexDirection: "column", gap: 5 }}>
            <Input placeholder="Text" type="text" label="Text" />
            <Input placeholder="Textarea" multiline minRows={3} label="Textarea" />
            <p>Wysiwyg</p>
            <Wysiwyg />
          </div>
        </div>
        <H5 style={{ marginTop: 10, padding: "5px 0", borderBottom: "1px solid" }}>
          <i>Other fields:</i>
        </H5>
        <Input type="number" placeholder="Number" label="Number" />
        <Input type="password" hiddenText placeholder="Password" label="Password" />
        <H5 style={{ marginTop: 10, padding: "5px 0", borderBottom: "1px solid" }}>
          <i>Selects:</i>
        </H5>
        {/* <Select /> */}
        <SelectInput
          placeholder="Select"
          value="option1"
          options={[
            { label: "Option 1", value: "option1" },
            { label: "Option 2", value: "option2" },
          ]}
        />
        <H5 style={{ marginTop: 10, padding: "5px 0", borderBottom: "1px solid" }}>
          <i>Customs:</i>
        </H5>
        <FormControlLabel label="Switch" labelPlacement="start" control={<CheckIconSwitch checked={isChecked} onChange={() => setIsChecked(!isChecked)} label="Switch" />} />
        <FormControlLabel control={<Checkbox name="checkedA" />} label="Checkbox" />
        <div style={{ display: "flex", flexDirection: "column", gap: 5 }}>
          <ColorPicker color="#000000" onChange={(e) => console.info(e.hex)} label="Color picker" required />
          <Input type="date" placeholder="Date" label="Date" />
          <Input type="datetime-local" placeholder="Date" label="Datetime-local" />
          <MediaInput formLabel={t("inputs.image")} accept="image/*" />
        </div>
      </section>
      <section style={{ padding: 15 }}>
        <H3 id="geojson" style={{ textDecoration: "underline" }} gutterBottom>
          GeoJSON:
        </H3>
        <Geo />
      </section>
      <Stack direction="row" gap={3} p={3}>
        {["fr_FR", "en_GB"].map((lang) => (
          <section key={lang}>
            <h2>{lang}</h2>
            <Stack gap={1}>
              {rules_functions
                .filter((fn) => !fn.advanced)
                .map((fn, i) => (
                  <StackedInfos key={i} firstLine={<RuleFunction func={fn} />} secondLine={<Translations translations={fn.descriptions} locale={lang} />} />
                ))}
            </Stack>
          </section>
        ))}
      </Stack>
    </Box>
  );
}
