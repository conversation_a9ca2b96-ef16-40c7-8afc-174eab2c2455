import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import Paginator from "@/components/ui/pagination/Paginator";
import TableError from "@/components/tables/TableError";
import FormFilterError from "@/components/formFilters/FormFilterError";
import { useView } from "@/contexts/ViewProvider";
import PaginatorLayoutContainer from "@/components/ui/PaginatorLayoutContainer";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import { useSearchParams } from "react-router";
import { useEffect, useState } from "react";
import { useTheme } from "@mui/material";
import { useTranslation } from "@/hooks/useTranslation";
import RULE_TYPES from "@/enums/RULE_TYPES";
import DashboardCard from "@/components/ui/dashboards/DashboardCard";
import { useApi } from "@/contexts/ApiProvider";
import { ApiTypeEnum, FilterOperatorEnum } from "@/enums";

export default function Errors() {
  const { t } = useTranslation();
  const { view, saveOrder } = useView();
  const documentErrorSummaryView = view?.[ApiTypeEnum.DOCUMENT_ERROR_SUMMARY];
  const theme = useTheme();
  const api = useApi();

  const [searchParams] = useSearchParams();
  const channel = searchParams.get("catalogScope");
  const ruleGroup = searchParams.get("ruleGroup");
  const skus = searchParams.get("skus");

  const filters = {
    filters: { filters: documentErrorSummaryView?.attributeFilters?.filters || [], operator: FilterOperatorEnum.AND },
    ...documentErrorSummaryView?.filters,
  };

  if (ruleGroup) {
    filters.ruleGroup = ruleGroup;
  }
  if (channel) {
    filters.catalogScope = channel;
  }
  if (skus) {
    filters.skus = skus;
  }

  const paginator = useUrlPaginator(ApiTypeEnum.DOCUMENT_ERROR_SUMMARY, "/api/document-error-summaries", {}, {}, true, null, filters);

  useEffect(() => {
    if (JSON.stringify(paginator.order) !== JSON.stringify(documentErrorSummaryView?.order)) {
      saveOrder(paginator.type, paginator.order);
    }
  }, [paginator.order]);

  const [data, setData] = useState(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        const data = await api.get(`/api/dashboards/home${channel ? `?channel=${channel}` : ""}`);
        setData(data.stats);
      } catch (error) {
        console.log("Error while fetching data", error);
      }
    };

    loadData();
  }, [channel]);

  const selectedGroup = data?.valueStats?.find((item) => item.ruleGroup?.code === ruleGroup?.replace("/api/rule-groups/", ""));

  const array = [
    {
      value: selectedGroup?.valid,
      name: "valid",
      label: t("lvmh.dash.status.valid_attribute"),
      color: theme.palette.green.main,
    },
    {
      value: selectedGroup?.error,
      name: RULE_TYPES.ERROR,
      label: t("lvmh.dash.status.attribute_error"),
      color: theme.palette.warning.main,
    },
    {
      value: selectedGroup?.missing,
      name: RULE_TYPES.MISSING,
      label: t("lvmh.dash.status.empty_attribute"),
      color: theme.palette.error.main,
    },
  ];

  return (
    <PaginatorLayoutContainer topPanel={<PaginatorTopPanel paginator={paginator} />} formFilters={<FormFilterError paginator={paginator} />}>
      {selectedGroup ? (
        <DashboardCard
          title={selectedGroup.ruleGroup ? t("lvmh.dash.title.attributes_by_typologie") + t(selectedGroup.ruleGroup?.names) : t("lvmh.dash.title.attributes_by_status")}
          data={array}
          count={selectedGroup.valid + selectedGroup.error + selectedGroup.missing}
          params={Object.fromEntries(searchParams.entries())}
          hasOnClick
          hasHoverMain={false}
          isSmall
        />
      ) : null}
      <Paginator paginator={paginator}>
        <TableError paginator={paginator} />
      </Paginator>
    </PaginatorLayoutContainer>
  );
}
