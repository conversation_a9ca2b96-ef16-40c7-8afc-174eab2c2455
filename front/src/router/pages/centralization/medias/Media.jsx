import ITEM_TYPES from "@/enums/ITEM_TYPES";
import useTree from "@/hooks/useTree";
import { CircularProgress, Grid2, Stack, useTheme } from "@mui/material";
import { useTranslation } from "@/hooks/useTranslation";
import CardTree from "@/components/ui/cardTree/CardTree";
import BreadcrumbsTree from "@/components/ui/BreadcrumbsTree";
import { Body1, H2 } from "@/components/ui/Typography";
import { useSettings } from "@/contexts/SettingsProvider";
import ButtonImportExport from "@/components/buttons/ButtonImportExport";
import GridCategories from "@/components/grid/GridCategories";
import { usePaginator } from "@/hooks/usePaginator";
import { useDropzone } from "react-dropzone";
import { useTasks } from "@/contexts/TasksProvider";
import Tabs from "@/components/ui/tabs/Tabs";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import ButtonAddFolder from "@/components/buttons/ButtonAddFolder";
import ButtonAddMedia from "@/components/buttons/ButtonAddMedia";
import { useUser } from "@/contexts/UserProvider";
import useDidMountEffect from "@/hooks/useDidMountEffect";
import { useApi } from "@/contexts/ApiProvider";
import TASK_TYPES from "@/enums/TASK_TYPES";
import MediaListing from "@/components/MediaListing";
import { DirectionEnum } from "@/enums";

export default function Media() {
  const { t } = useTranslation();
  const { settings } = useSettings();
  const theme = useTheme();
  const { preferences } = useUser();
  const tasks = useTasks();
  const api = useApi();

  const tree = useTree(ITEM_TYPES.MEDIA_FOLDER, "/api/media-folders");

  const paginator = usePaginator(ITEM_TYPES.MEDIA, "/api/medias", tree.urlParams ? { folder: tree.urlParams } : { "exists[folder]": false }, { name: DirectionEnum.ASC }, true, null, preferences.getLayout(ITEM_TYPES.MEDIA));

  useDidMountEffect(() => {
    if (tree.urlParams) {
      void paginator.fetch({ folder: tree.urlParams }, null, 1);
    } else {
      void paginator.fetch(paginator?.filters?.name ? { name: paginator.filters.name } : { "exists[folder]": false }, null, 1);
    }
  }, [tree.urlParams]);

  const onDrop = (acceptedFiles) => {
    tasks.addTasks(TASK_TYPES.MEDIA, {
      files: acceptedFiles,
      folder: tree.current,
      onSuccess: () => {
        void paginator.goto(1);
        void tree.fetch();
      },
    });
  };
  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({ onDrop, noClick: true, noKeyboard: true });

  const onMove = async (dragIndex, dropIndex, type) => {
    if (dragIndex === dropIndex) {
      return;
    }
    try {
      const item = (type === "media" ? paginator : tree).items.find((item) => item["@id"] === dragIndex);
      const variables = { ...item };
      if (type === "media") {
        variables.folderId = Number(dropIndex?.replace("/api/media-folders/", ""));
      } else {
        variables.parent = dropIndex;
      }
      await api.put(dragIndex, variables);
      if (type === "media") {
        paginator.goto(paginator.page);
      }
      tree.fetch({}, tree.current);
    } catch (error) {
      console.log(error);
    }
  };

  const refreshCategories = () => {
    void tree.fetch({}, tree.current);
  };

  const tabs = [
    {
      label: `${t("items.Media._")} (${paginator.count})`,
      component: (
        <Stack overflow="auto" width="100%" position="relative">
          <MediaListing paginator={paginator} />
          <input {...getInputProps()} />
          {isDragActive ? (
            <Stack
              gap={0}
              sx={{
                backgroundColor: `${theme.palette.primary.light}40`,
                position: "absolute",
                height: "100%",
                // minHeight: "60dvh",
                width: "100%",
                zIndex: 10,
                top: 0,
                cursor: "grabbing",
                border: `1px dashed ${theme.palette.primary.main}`,
                borderRadius: "5px",
                backdropFilter: "blur(2px)",
                WebkitBackdropFilter: "blur(2px)",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <CloudUploadIcon color="primary" style={{ fontSize: "72px" }} />
              <Body1 color="primary">{t("actions.drop_files")}</Body1>
              <Body1 color="primary">{`(max ${settings.server.uploadMaxFilesizeLabel})`}</Body1>
            </Stack>
          ) : null}
        </Stack>
      ),
    },
  ];

  return (
    <Grid2 container height="100vh" overflow="hidden">
      <Grid2 sx={{ width: { xs: "200px", sm: "250px", md: "300px" } }} py={2} pl={2} height="100%">
        <CardTree title={t("items.Media._")} tree={tree} onSuccess={refreshCategories} />
      </Grid2>
      <Grid2 p={2} height="100%" size="grow">
        <Stack height="100%" display="grid" gridTemplateRows="max-content 1fr">
          <Stack direction="row" justifyContent="space-between" flexWrap="wrap">
            <Stack gap={0}>
              <Stack direction="row" alignItems="center" gap={1}>
                {tree.current ? <div style={{ width: 15, height: 15, borderRadius: "2px", backgroundColor: tree.current.color, marginTop: "5px" }} /> : null}
                <H2>{tree.urlParams ? tree.current?.name : settings.client.name}</H2>
              </Stack>
              <BreadcrumbsTree tree={tree} current={tree.urlParams} />
            </Stack>
            <Stack direction="row" alignItems="center" flexWrap="wrap">
              {tree.loading ? <CircularProgress size={20} /> : null}
              <ButtonImportExport paginator={paginator} type={ITEM_TYPES.MEDIA} onSave={() => {}} />
              <ButtonAddFolder type={ITEM_TYPES.MEDIA_FOLDER} parent={tree.current} onSuccess={refreshCategories} />
              <ButtonAddMedia onClick={() => open()} />
            </Stack>
          </Stack>
          <Stack {...getRootProps()} style={{ position: "relative" }} overflow="hidden">
            <Stack height="100%">
              <GridCategories tree={tree} onMove={onMove} onDoubleClick={(item) => tree.open(item)} />
              <Tabs tabs={tabs} />
            </Stack>
          </Stack>
        </Stack>
      </Grid2>
    </Grid2>
  );
}
