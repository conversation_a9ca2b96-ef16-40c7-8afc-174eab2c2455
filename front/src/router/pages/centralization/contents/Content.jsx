import { useState } from "react";
import { CircularProgress, Grid2, <PERSON>ack } from "@mui/material";
import { useTranslation } from "@/hooks/useTranslation";
import CardTree from "@/components/ui/cardTree/CardTree";
import { useSettings } from "@/contexts/SettingsProvider";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import useTree from "@/hooks/useTree";
import { usePaginator } from "@/hooks/usePaginator";
import { H2 } from "@/components/ui/Typography";
import BreadcrumbsTree from "@/components/ui/BreadcrumbsTree";
import ButtonAddFolder from "@/components/buttons/ButtonAddFolder";
import ButtonImportExport from "@/components/buttons/ButtonImportExport";
import ButtonAddContent from "@/components/buttons/ButtonAddContent";
import GridCategories from "@/components/grid/GridCategories";
import Tabs from "@/components/ui/tabs/Tabs";
import ItemUtils from "@/utils/item.utils";
import { useUser } from "@/contexts/UserProvider";
import useDidMountEffect from "@/hooks/useDidMountEffect";
import { useApi } from "@/contexts/ApiProvider";
import ContentListing from "../../contents/ContentListing";
import { DirectionEnum } from "@/enums";

export default function Content() {
  const { t } = useTranslation();
  const tree = useTree(ITEM_TYPES.CONTENT_FOLDER, "/api/content-folders");
  const { settings } = useSettings();
  const { preferences } = useUser();
  const api = useApi();

  const [content, setContent] = useState(null);

  const paginator = usePaginator(ITEM_TYPES.CONTENT, "/api/contents", tree.urlParams ? { folder: tree.urlParams } : { "exists[parent]": false }, { name: DirectionEnum.ASC }, true, null, preferences.getLayout(ITEM_TYPES.CONTENT));

  useDidMountEffect(() => {
    if (tree.urlParams) {
      void paginator.fetch({ folder: tree.urlParams }, null, 1);
    } else {
      void paginator.fetch({ "exists[folder]": false }, null, 1);
    }
  }, [tree.urlParams]);

  const onMove = async (dragIndex, dropIndex, type) => {
    if (dragIndex === dropIndex) {
      return;
    }
    try {
      const item = (type === "content" ? paginator : tree).items.find((item) => item["@id"] === dragIndex);
      const variables = { ...item };
      if (type === "content") {
        variables.folderId = Number(dropIndex?.replace("/api/content-folders/", ""));
      } else {
        variables.parent = dropIndex;
      }
      await api.put(dragIndex, variables);
      if (type === "content") {
        paginator.goto(paginator.page);
      }
      tree.fetch({}, tree.current);
    } catch (error) {
      console.log(error);
    }
  };

  const refreshCategories = () => {
    void tree.fetch({}, tree.current);
  };

  const onActionAddContent = () => setContent({ ...ItemUtils.getDefault(paginator.type), folderId: tree.current?.id || null });

  const tabs = [
    {
      label: `${t("items.Content._")} (${paginator.count})`,
      component: <ContentListing paginator={paginator} content={content} setContent={setContent} />,
    },
  ];

  return (
    <Grid2 container height="100vh" overflow="hidden">
      <Grid2 sx={{ width: { xs: "200px", sm: "250px", md: "300px" } }} py={2} pl={2} height="100%">
        <CardTree title={t("items.Content._")} tree={tree} onSuccess={refreshCategories} />
      </Grid2>
      <Grid2 p={2} height="100%" size="grow">
        <Stack height="100%" display="grid" gridTemplateRows="max-content 1fr">
          <Stack direction="row" justifyContent="space-between" flexWrap="wrap">
            <Stack gap={0}>
              <Stack direction="row" alignItems="center" gap={1}>
                {tree.current ? <div style={{ width: 15, height: 15, borderRadius: "2px", backgroundColor: tree.current.color, marginTop: "5px" }} /> : null}
                <H2>{tree.urlParams ? tree.current?.name : settings.client.name}</H2>
              </Stack>
              <BreadcrumbsTree tree={tree} current={tree.urlParams} />
            </Stack>
            <Stack direction="row" alignItems="center" flexWrap="wrap">
              {tree.loading ? <CircularProgress size={20} /> : null}
              <ButtonImportExport paginator={paginator} type={ITEM_TYPES.CONTENT} onSave={() => {}} />
              <ButtonAddFolder type={ITEM_TYPES.CONTENT_FOLDER} parent={tree.current} onSuccess={refreshCategories} />
              <ButtonAddContent onClick={onActionAddContent} />
            </Stack>
          </Stack>
          <Stack style={{ position: "relative" }} overflow="hidden">
            <Stack height="100%">
              <GridCategories tree={tree} onMove={onMove} onDoubleClick={(item) => tree.open(item)} />
              <Tabs tabs={tabs} />
            </Stack>
          </Stack>
        </Stack>
      </Grid2>
    </Grid2>
  );
}
