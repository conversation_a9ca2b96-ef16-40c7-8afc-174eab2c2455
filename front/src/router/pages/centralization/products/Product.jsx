import ButtonSyncProduct from "@/components/buttons/ButtonSyncProduct";
import DrawerProduct from "@/components/drawers/DrawerProduct";
import FormFilterProduct from "@/components/formFilters/FormFilterProduct";
import GridProduct from "@/components/grid/GridProduct";
import TableProduct from "@/components/tables/TableProduct";
import Paginator from "@/components/ui/pagination/Paginator";
import PaginatorLayoutContainer from "@/components/ui/PaginatorLayoutContainer";
import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import { useUser } from "@/contexts/UserProvider";
import { useView } from "@/contexts/ViewProvider";
import LAYOUTS from "@/enums/LAYOUTS";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import { ApiTypeEnum, FilterOperatorEnum } from "@/enums";
import { useEffect, useState } from "react";

export default function Product() {
  const { view, saveOrder } = useView();
  const { preferences } = useUser();
  const productView = view?.[ApiTypeEnum.PRODUCT];

  const attributeFilters = productView?.attributeFilters || {
    operator: FilterOperatorEnum.AND,
    filters: [],
  };

  const paginator = useUrlPaginator(ApiTypeEnum.PRODUCT, "/api/products", {}, productView?.order || { sku: "ASC" }, true, preferences.getLayout(ApiTypeEnum.PRODUCT), {
    filters: { filters: attributeFilters },
    ...productView?.filters,
  });
  const [product, setProduct] = useState(null);

  useEffect(() => {
    if (JSON.stringify(paginator.order) !== JSON.stringify(productView?.order)) {
      saveOrder(paginator.type, paginator.order);
    }
  }, [paginator.order]);

  return (
    <PaginatorLayoutContainer
      topPanel={
        <PaginatorTopPanel paginator={paginator} open={setProduct}>
          <ButtonSyncProduct />
        </PaginatorTopPanel>
      }
      formFilters={<FormFilterProduct paginator={paginator} />}
    >
      <Paginator paginator={paginator} localizable scopable selection={{}}>
        {paginator.view === LAYOUTS.LIST ? <TableProduct paginator={paginator} /> : <GridProduct paginator={paginator} />}
      </Paginator>
      <DrawerProduct
        product={product}
        onClose={() => setProduct(null)}
        onRefresh={() => {
          void paginator.goto(1);
        }}
      />
    </PaginatorLayoutContainer>
  );
}
