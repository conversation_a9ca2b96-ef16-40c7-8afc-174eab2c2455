import ScheduleForm from "@/components/channel/schedule/ScheduleForm";
import {ChannelScheduleTypeEnum} from "@/enums/ChannelScheduleTypeEnum";
import {ChannelAdapterEnum} from "@/enums/ChannelAdapterEnum";
import {H1} from "@/components/ui/Typography";
import {Stack} from "@mui/material";
import {useState} from "react";
import type {UiDefinition} from "@/types/definition/UiDefinition";
import type {Adapter} from "@/components/channel/adapter/types";
import type {Schedule} from "@/components/channel/schedule/types";
import AdapterForm from "@/components/channel/adapter/AdapterForm";
import MappingForm from "@/components/channel/mapping/MappingForm";
import type {Mapping} from "@/components/channel/mapping/types";
import type {Format as FormatType} from "@/components/channel/format/types";
import Format from "@/components/channel/format/Format";
import {ChannelFormatEnum} from "@/enums/ChannelFormatEnum";
import type {Source as SourceType} from "@/components/channel/source/types";
import {ChannelSourceEnum} from "@/enums/ChannelSourceEnum";
import Source from "@/components/channel/source/Source";
import type {Filters as FiltersType} from "@/types/definition/SourceDefinition";
import FilterForm from "@/components/filters/FilterForm";
import {useTranslation} from "@/hooks/useTranslation";
import {FilterOperatorEnum} from "@/enums";

const ui: UiDefinition = {
  isEnabled: true,
  isRequired: true,
  isReadonly: false,
  isValid: true,
  alert: null,
};
export default function Components() {
  const {t} = useTranslation();
  const [schedule, setSchedule] = useState<Schedule>({
    type: null,
    parameters: {},
    definition: {
      ui,
      sync: null,
      types: [ChannelScheduleTypeEnum.ONE_HOUR, ChannelScheduleTypeEnum.TEN_MINUTES, ChannelScheduleTypeEnum.CUSTOM],
      max: null,
    },
  });
  const [adapter, setAdapter] = useState<Adapter>({
    type: null,
    parameters: {},
    definition: {
      ui,
      types: [ChannelAdapterEnum.LOCAL, ChannelAdapterEnum.FTP, ChannelAdapterEnum.API],
    },
  });
  const [mapping, setMapping] = useState<Mapping>({
    columns: [],
    root: null,
    definition: {
      ui,
      hasRoot: true,
      root: null,
      canAddColumn: true,
      columns: [],
    },
  });

  const [format, setFormat] = useState<FormatType>({
    type: null,
    parameters: {},
    definition: {
      ui,
      types: [ChannelFormatEnum.CSV, ChannelFormatEnum.JSON, ChannelFormatEnum.XML],
    },
  });

  const [source, setSource] = useState<SourceType>({
    type: ChannelSourceEnum.PRODUCT,
    filters: {operator: FilterOperatorEnum.AND, filters: []},
    order: {},
    definition: {
      ui,
      types: Object.values(ChannelSourceEnum),
      isFilterable: true,
      isOrderable: false,
    },
  });

  const [filters, setFilters] = useState<FiltersType>({
    operator: FilterOperatorEnum.AND,
    filters: [],
  });

  return (
    <Stack p={2}>
      <H1>{t("items.ChannelFilter._singular")}</H1>
      {source?.type ? <FilterForm filters={filters} onChange={setFilters} type={source.type} /> : <p>{t("choose_one")}</p>}
      <H1>{t("items.ChannelSchedule._singular")}</H1>
      <ScheduleForm schedule={schedule} onChange={setSchedule} />
      <H1>{t("items.ChannelAdapter._singular")}</H1>
      <AdapterForm adapter={adapter} onChange={setAdapter} />
      <H1>{t("items.ChannelMapping._singular")}</H1>
      <MappingForm mapping={mapping} onChange={setMapping} />
      <H1>{t("items.ChannelFormat._singular")}</H1>
      <Format format={format} onChange={setFormat} />
      <H1>{t("items.ChannelSource._singular")}</H1>
      <Source source={source} onChange={setSource} />
    </Stack>
  );
}
