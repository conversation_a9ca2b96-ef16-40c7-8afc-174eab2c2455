import { useApi } from "@/contexts/ApiProvider";
import { Card, Chip, Stack } from "@mui/material";
import { useParams } from "react-router";
import { useEffect, useState } from "react";
import { useTranslation } from "@/hooks/useTranslation";
import { Download } from "@mui/icons-material";
import TopPanel from "@/components/ui/topPanel/TopPanel";
import Time from "@/components/ui/Time";
import LoaderPage from "@/components/loaders/LoaderPage";
import { ButtonPrimary } from "@/components/ui/Button";
import { Body1, H4 } from "@/components/ui/Typography";
import { RouteEnum } from "@/enums";

export default function UploadExportDetails() {
  const { t } = useTranslation();
  const { uuid } = useParams();
  const api = useApi();

  const [uploadExport, setUploadExport] = useState(null);

  useEffect(() => {
    const getUploadRow = async () => {
      const uploadExportData = await api.get(`/api/upload-exports/${uuid}`);
      setUploadExport(uploadExportData);
    };
    getUploadRow();
  }, []);

  const infos = [
    { label: t("items.UploadExport.userEmail"), render: (item) => item.userEmail },
    { label: "Type", render: (item) => <Chip label={t(`items.${item.type}._`)} size="small" color="primary" /> },
    { label: t("actions.created_at"), render: (item) => <Time datetime={item.createdAt} /> },
    { label: t("actions.ended_at"), render: (item) => <Time datetime={item.endedAt} /> },
    { label: t("items.Export.format"), render: (item) => <Chip label={t(`enums.EXPORT_FORMATS.${item.format}`)} size="small" color="primary" /> },
  ];

  return (
    <Stack gap={0}>
      <TopPanel title={t("items.UploadExport._")} goBackRoute={RouteEnum.UPLOAD_EXPORTS} />
      <Stack padding={2}>
        {!uploadExport ? (
          <LoaderPage />
        ) : (
          <Stack>
            <Card variant="outlined" component={Stack} gap={1} position="relative">
              <H4>{t("items.UploadExport._")}</H4>
              <Stack direction="row" gap={1} position="absolute" top={15} right={15}>
                <ButtonPrimary variant="outlined" size="small" startIcon={<Download />} LinkComponent="a" href={`${api.apiRoot}${uploadExport["@id"]}/binary?token=${api.getToken()}`} target="_blank">
                  {t("actions.download")}
                </ButtonPrimary>
              </Stack>
              <Stack gap={1}>
                {infos.map((info) => (
                  <Stack key={info.label} direction="row" gap={1}>
                    <Body1 fontWeight="700">
                      {info.label}
                      {t("common.points")}
                    </Body1>
                    <Body1>{info.render(uploadExport)}</Body1>
                  </Stack>
                ))}
              </Stack>
            </Card>
          </Stack>
        )}
      </Stack>
    </Stack>
  );
}
