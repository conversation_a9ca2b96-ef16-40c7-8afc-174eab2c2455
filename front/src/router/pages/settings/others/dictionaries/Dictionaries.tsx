import DrawerDictionary from "@/components/drawers/DrawerDictionary";
import FormFilterDictionary from "@/components/formFilters/FormFilterDictionary";
import SettingsLayout from "@/components/SettingsLayout";
import TableDictionary from "@/components/tables/TableDictionary";
import Paginator from "@/components/ui/pagination/Paginator";
import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import {ApiTypeEnum, DirectionEnum} from "@/enums";
import {useUrlPaginator} from "@/hooks/useUrlPaginator";
import type {Dictionary} from "@/resources";
import ItemUtils from "@/utils/item.utils";
import {useState} from "react";

export default function Dictionaries() {
  const paginator = useUrlPaginator(ApiTypeEnum.DICTIONARY, "/api/dictionaries", {}, {code: DirectionEnum.ASC});
  const [dictionary, setDictionary] = useState<Dictionary | null>(null);

  const handleSuccessFormDictionary = () => {
    setDictionary(null);
    paginator.goto(1);
  };

  return (
    <SettingsLayout topPanel={<PaginatorTopPanel paginator={paginator} open={setDictionary} />} formFilters={<FormFilterDictionary paginator={paginator} />}>
      <Paginator paginator={paginator} onCreate={() => setDictionary(ItemUtils.getDefault(ApiTypeEnum.DICTIONARY))}>
        <TableDictionary paginator={paginator} open={setDictionary} />
      </Paginator>
      <DrawerDictionary
        dictionary={dictionary}
        onClose={() => setDictionary(null)}
        onDelete={() => {
          setDictionary(null);
          paginator.goto(1);
        }}
        onSuccess={handleSuccessFormDictionary}
      />
    </SettingsLayout>
  );
}
