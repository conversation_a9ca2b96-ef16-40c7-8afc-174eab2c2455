import Tabs from "@/components/ui/tabs/Tabs";
import FormFilterUploadImport from "@/components/formFilters/FormFilterUploadImport";
import Paginator from "@/components/ui/pagination/Paginator";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import { Stack } from "@mui/material";
import { ExitToApp } from "@mui/icons-material";
import TableUploadImport from "@/components/tables/TableUploadImport";
import { generatePath, useNavigate } from "react-router";
import { useTranslation } from "@/hooks/useTranslation";
import { DirectionEnum, RouteEnum } from "@/enums";

export default function UploadHistory() {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const getDefaultTab = () => {
    return window.location.pathname.includes(RouteEnum.UPLOAD_EXPORTS) ? 1 : 0;
  };

  const importPaginator = useUrlPaginator(ITEM_TYPES.UPLOAD_IMPORT, "/api/upload-imports", {}, { createdAt: DirectionEnum.ASC });
  const exportPaginator = useUrlPaginator(ITEM_TYPES.UPLOAD_EXPORT, "/api/upload-exports", {}, { createdAt: DirectionEnum.ASC });

  const tabs = [
    {
      label: t("items.UploadImport._"),
      icon: (
        <Stack sx={{ alignItems: "center", transform: "scaleX(-1)" }}>
          <ExitToApp />
        </Stack>
      ),
      component: (
        <>
          <FormFilterUploadImport paginator={importPaginator} />
          <Paginator paginator={importPaginator}>
            <TableUploadImport paginator={importPaginator} open={(item) => navigate(generatePath(RouteEnum.UPLOAD_IMPORTS_DETAIL, { uuid: item.uuid }))} />
          </Paginator>
        </>
      ),
    },
    {
      label: t("items.UploadExport._"),
      icon: (
        <Stack sx={{ alignItems: "center", transform: "scaleX(-1)" }}>
          <ExitToApp />
        </Stack>
      ),
      component: (
        <>
          <FormFilterUploadImport paginator={exportPaginator} />
          <Paginator paginator={exportPaginator}>
            <TableUploadImport paginator={exportPaginator} open={(item) => navigate(generatePath(RouteEnum.UPLOAD_EXPORTS_DETAIL, { uuid: item.uuid }))} />
          </Paginator>
        </>
      ),
    },
  ];

  return (
    <Stack p={2}>
      <Tabs tabs={tabs} defaultTab={getDefaultTab()} />
    </Stack>
  );
}
