import DrawerDictionaryMapping from "@/components/drawers/DrawerDictionaryMapping";
import FormFilterDictionaryMapping from "@/components/formFilters/FormFilterDictionaryMapping";
import SettingsLayout from "@/components/SettingsLayout";
import TableDictionaryMapping from "@/components/tables/TableDictionaryMapping";
import Paginator from "@/components/ui/pagination/Paginator";
import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import {ApiTypeEnum, DirectionEnum} from "@/enums";
import {useUrlPaginator} from "@/hooks/useUrlPaginator";
import type {DictionaryMapping} from "@/resources";
import ItemUtils from "@/utils/item.utils";
import {useState} from "react";

export default function DictionaryMappings() {
  const paginator = useUrlPaginator(ApiTypeEnum.DICTIONARY_MAPPING, "/api/dictionary-mappings", {}, {from: DirectionEnum.ASC});
  const [dictionaryMapping, setDictionaryMapping] = useState<DictionaryMapping | null>(null);

  const handleSuccessFormDictionary = () => {
    setDictionaryMapping(null);
    paginator.goto(1);
  };

  return (
    <SettingsLayout topPanel={<PaginatorTopPanel paginator={paginator} open={setDictionaryMapping} />} formFilters={<FormFilterDictionaryMapping paginator={paginator} />}>
      <Paginator paginator={paginator} onCreate={() => setDictionaryMapping(ItemUtils.getDefault(ApiTypeEnum.DICTIONARY_MAPPING))}>
        <TableDictionaryMapping paginator={paginator} open={setDictionaryMapping} />
      </Paginator>
      <DrawerDictionaryMapping
        dictionaryMapping={dictionaryMapping}
        onClose={() => setDictionaryMapping(null)}
        onDelete={() => {
          setDictionaryMapping(null);
          paginator.goto(1);
        }}
        onSuccess={handleSuccessFormDictionary}
      />
    </SettingsLayout>
  );
}
