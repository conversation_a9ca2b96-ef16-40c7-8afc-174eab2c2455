import { useState } from "react";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import Paginator from "@/components/ui/pagination/Paginator";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import FormFilterWorkflow from "@/components/formFilters/FormFilterWorkflow";
import TableWorkflow from "@/components/tables/TableWorkflow";
import DrawerWorkflow from "@/components/drawers/DrawerWorkflow";
import ItemUtils from "@/utils/item.utils";
import SettingsLayout from "@/components/SettingsLayout";
import { DirectionEnum } from "@/enums";

export default function Workflow() {
  const paginator = useUrlPaginator(ITEM_TYPES.WORKFLOW, "/api/workflows", {}, { code: DirectionEnum.ASC });
  const [workflow, setWorkflow] = useState(null);

  return (
    <SettingsLayout
      topPanel={<PaginatorTopPanel paginator={paginator} open={setWorkflow} />}
      formFilters={<FormFilterWorkflow paginator={paginator} />}
    >
      <Paginator paginator={paginator} onCreate={() => setWorkflow(ItemUtils.getDefault(ITEM_TYPES.WORKFLOW))}>
        <TableWorkflow paginator={paginator} />
      </Paginator>
      <DrawerWorkflow
        workflow={workflow}
        onClose={() => setWorkflow(null)}
        onChange={(workflow) => void paginator.replaceItem(workflow)}
        onRefresh={() => {
          void paginator.goto(1);
        }}
      />
    </SettingsLayout>
  );
}
