import { useState } from "react";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import Paginator from "@/components/ui/pagination/Paginator";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import FormFilterRule from "@/components/formFilters/FormFilterRule";
import TableRule from "@/components/tables/TableRule";
import DrawerRule from "@/components/drawers/DrawerRule";
import ItemUtils from "@/utils/item.utils";
import SettingsLayout from "@/components/SettingsLayout";
import { DirectionEnum } from "@/enums";

export default function Rule() {
  const paginator = useUrlPaginator(ITEM_TYPES.RULE, "/api/rules", {}, { code: DirectionEnum.ASC });
  const [rule, setRule] = useState(null);

  return (
    <SettingsLayout
      topPanel={<PaginatorTopPanel paginator={paginator} open={setRule} />}
      formFilters={<FormFilterRule paginator={paginator} />}
    >
      <Paginator paginator={paginator} onCreate={() => setRule(ItemUtils.getDefault(ITEM_TYPES.RULE))}>
        <TableRule paginator={paginator} open={setRule} />
      </Paginator>
      <DrawerRule
        rule={rule}
        onClose={() => setRule(null)}
        onChange={(rule) => void paginator.replaceItem(rule)}
        onRefresh={() => {
          void paginator.goto(1);
        }}
      />
    </SettingsLayout>
  );
}
