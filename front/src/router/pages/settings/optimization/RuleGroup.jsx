import { useState } from "react";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import Paginator from "@/components/ui/pagination/Paginator";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import TableRuleGroup from "@/components/tables/TableRuleGroup";
import DrawerRuleGroup from "@/components/drawers/DrawerRuleGroup";
import FormFilterRuleGroup from "@/components/formFilters/FormFilterRuleGroup";
import ItemUtils from "@/utils/item.utils";
import SettingsLayout from "@/components/SettingsLayout";
import { DirectionEnum } from "@/enums";

export default function RuleGroup() {
  const paginator = useUrlPaginator(ITEM_TYPES.RULE_GROUP, "/api/rule-groups", {}, { code: DirectionEnum.ASC });
  const [ruleGroup, setRuleGroup] = useState(null);

  return (
    <SettingsLayout
      topPanel={<PaginatorTopPanel paginator={paginator} open={setRuleGroup} />}
      formFilters={<FormFilterRuleGroup paginator={paginator} />}
    >
      <Paginator paginator={paginator} onCreate={() => setRuleGroup(ItemUtils.getDefault(ITEM_TYPES.RULE_GROUP))}>
        <TableRuleGroup paginator={paginator} open={setRuleGroup} />
      </Paginator>
      <DrawerRuleGroup
        ruleGroup={ruleGroup}
        onClose={() => setRuleGroup(null)}
        onChange={(ruleGroup) => void paginator.replaceItem(ruleGroup)}
        onRefresh={() => {
          void paginator.goto(1);
        }}
      />
    </SettingsLayout>
  );
}
