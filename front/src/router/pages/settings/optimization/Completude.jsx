import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import { useState } from "react";
import Paginator from "@/components/ui/pagination/Paginator";
import ItemUtils from "@/utils/item.utils";
import FormFilterCompletude from "@/components/formFilters/FormFilterCompletude";
import TableCompletude from "@/components/tables/TableCompletude";
import DrawerCompletude from "@/components/drawers/DrawerCompletude";
import { useCompletudes } from "@/contexts/CompletudesProvider";
import SettingsLayout from "@/components/SettingsLayout";
import { DirectionEnum } from "@/enums";

export default function Completude() {
  const { fetchCompletudes } = useCompletudes();

  const paginator = useUrlPaginator(ITEM_TYPES.COMPLETUDE, "/api/completudes", {}, { code: DirectionEnum.ASC });
  const [completude, setCompletude] = useState(null);

  return (
    <SettingsLayout
      topPanel={<PaginatorTopPanel paginator={paginator} open={setCompletude} />}
      formFilters={<FormFilterCompletude paginator={paginator} />}
    >
      <Paginator paginator={paginator} onCreate={() => setCompletude(ItemUtils.getDefault(ITEM_TYPES.COMPLETUDE))}>
        <TableCompletude paginator={paginator} open={setCompletude} />
      </Paginator>
      <DrawerCompletude
        completude={completude}
        onClose={() => setCompletude(null)}
        onChange={(item) => void paginator.replaceItem(item)}
        onRefresh={() => {
          void paginator.goto(1);
          fetchCompletudes();
        }}
      />
    </SettingsLayout>
  );
}
