import DrawerCurrency from "@/components/drawers/DrawerCurrency";
import FormFilterCurrency from "@/components/formFilters/FormFilterCurrency";
import SettingsLayout from "@/components/SettingsLayout";
import TableCurrency from "@/components/tables/TableCurrency";
import Paginator from "@/components/ui/pagination/Paginator";
import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import { useCurrencies } from "@/contexts/CurrenciesProvider";
import { DirectionEnum } from "@/enums";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import ItemUtils from "@/utils/item.utils";
import { useState } from "react";

export default function Currency() {
  const { fetchCurrencies } = useCurrencies();
  const paginator = useUrlPaginator(ITEM_TYPES.CURRENCY, "/api/currencies", {}, { code: DirectionEnum.ASC });
  const [currency, setCurrency] = useState(null);

  return (
    <SettingsLayout
      topPanel={<PaginatorTopPanel paginator={paginator} open={setCurrency} />}
      formFilters={<FormFilterCurrency paginator={paginator} />}
    >
      <Paginator paginator={paginator} onCreate={() => setCurrency(ItemUtils.getDefault(ITEM_TYPES.CURRENCY))}>
        <TableCurrency paginator={paginator} open={setCurrency} />
      </Paginator>
      <DrawerCurrency
        currency={currency}
        items={paginator.items}
        onClose={() => setCurrency(null)}
        onRefresh={() => {
          fetchCurrencies();
          paginator.goto(1);
        }}
        onDelete={() => {
          setCurrency(null);
          fetchCurrencies();
          paginator.goto(1);
        }}
      />
    </SettingsLayout>
  );
}
