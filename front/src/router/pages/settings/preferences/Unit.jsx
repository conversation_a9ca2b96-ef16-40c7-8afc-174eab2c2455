import FormFilterUnit from "@/components/formFilters/FormFilterUnit";
import SettingsLayout from "@/components/SettingsLayout";
import TableUnit from "@/components/tables/TableUnit";
import Paginator from "@/components/ui/pagination/Paginator";
import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import { DirectionEnum } from "@/enums";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";

export default function Unit() {
  const paginator = useUrlPaginator(ITEM_TYPES.UNIT, "/api/units", {}, { code: DirectionEnum.ASC });

  return (
    <SettingsLayout formFilters={<FormFilterUnit paginator={paginator} />} topPanel={<PaginatorTopPanel paginator={paginator} />}>
      <Paginator paginator={paginator}>
        <TableUnit paginator={paginator} />
      </Paginator>
    </SettingsLayout>
  );
}
