import DrawerLocale from "@/components/drawers/DrawerLocale";
import FormFilterLocale from "@/components/formFilters/FormFilterLocale";
import SettingsLayout from "@/components/SettingsLayout";
import TableLocale from "@/components/tables/TableLocale";
import Paginator from "@/components/ui/pagination/Paginator";
import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import { DirectionEnum } from "@/enums";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import ItemUtils from "@/utils/item.utils";
import { useState } from "react";

export default function Locale() {
  const paginator = useUrlPaginator(ITEM_TYPES.LOCALE, "/api/locales", {}, { code: DirectionEnum.ASC });
  const [locale, setLocale] = useState(null);

  return (
    <SettingsLayout
      topPanel={<PaginatorTopPanel paginator={paginator} open={setLocale} />}
      formFilters={<FormFilterLocale paginator={paginator} />}
    >
      <Paginator paginator={paginator} onCreate={() => setLocale(ItemUtils.getDefault(ITEM_TYPES.LOCALE))}>
        <TableLocale paginator={paginator} open={setLocale} />
      </Paginator>
      <DrawerLocale
        locale={locale}
        items={paginator.items}
        onClose={() => setLocale(null)}
        onRefresh={() => {
          void paginator.goto(1);
        }}
      />
    </SettingsLayout>
  );
}
