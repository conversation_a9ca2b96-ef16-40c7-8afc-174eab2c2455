import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import { useState } from "react";
import Paginator from "@/components/ui/pagination/Paginator";
import FormFilterAttributeOption from "@/components/formFilters/FormFilterAttributeOption";
import TableAttributeOption from "@/components/tables/TableAttributeOption";
import DrawerAttributeOption from "@/components/drawers/DrawerAttributeOption";
import ItemUtils from "@/utils/item.utils";
import SettingsLayout from "@/components/SettingsLayout";
import { DirectionEnum } from "@/enums";

export default function AttributeOption() {
  const paginator = useUrlPaginator(ITEM_TYPES.ATTRIBUTE_OPTION, "/api/attribute-options", {}, { code: DirectionEnum.ASC });
  const [attributeOption, setAttributeOption] = useState(null);

  return (
    <SettingsLayout
      topPanel={<PaginatorTopPanel paginator={paginator} open={setAttributeOption} />}
      formFilters={<FormFilterAttributeOption paginator={paginator} />}
    >
      <Paginator paginator={paginator} onCreate={() => setAttributeOption(ItemUtils.getDefault(ITEM_TYPES.ATTRIBUTE_OPTION))}>
        <TableAttributeOption paginator={paginator} open={setAttributeOption} />
      </Paginator>
      <DrawerAttributeOption
        item={attributeOption}
        onClose={() => setAttributeOption(null)}
        onChange={(attributeOption) => void paginator.replaceItem(attributeOption)}
        onRefresh={() => {
          void paginator.goto(1);
        }}
      />
    </SettingsLayout>
  );
}
