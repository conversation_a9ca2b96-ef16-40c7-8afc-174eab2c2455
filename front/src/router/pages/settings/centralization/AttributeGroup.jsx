import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import { useState } from "react";
import FormFilterAttributeGroup from "@/components/formFilters/FormFilterAttributeGroup";
import TableAttributeGroup from "@/components/tables/TableAttributeGroup";
import Paginator from "@/components/ui/pagination/Paginator";
import DrawerAttributeGroup from "@/components/drawers/DrawerAttributeGroup";
import ItemUtils from "@/utils/item.utils";
import SettingsLayout from "@/components/SettingsLayout";
import { DirectionEnum } from "@/enums";

export default function AttributeGroup() {
  const paginator = useUrlPaginator(ITEM_TYPES.ATTRIBUTE_GROUP, "/api/attribute-groups", {}, { code: DirectionEnum.ASC });
  const [attributeGroup, setAttributeGroup] = useState(null);

  const handleRefresh = (attributeGroup) => {
    paginator.replaceItem(attributeGroup);
    setAttributeGroup(attributeGroup);
  };

  return (
    <SettingsLayout
      topPanel={<PaginatorTopPanel paginator={paginator} open={setAttributeGroup} />}
      formFilters={<FormFilterAttributeGroup paginator={paginator} />}
    >
      <Paginator paginator={paginator} onCreate={() => setAttributeGroup(ItemUtils.getDefault(ITEM_TYPES.ATTRIBUTE_GROUP))}>
        <TableAttributeGroup paginator={paginator} open={setAttributeGroup} />
      </Paginator>
      <DrawerAttributeGroup
        item={attributeGroup}
        onClose={() => setAttributeGroup(null)}
        onChange={handleRefresh}
        onRefresh={() => {
          void paginator.goto(1);
        }}
      />
    </SettingsLayout>
  );
}
