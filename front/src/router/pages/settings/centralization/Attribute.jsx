import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import { useState } from "react";
import Paginator from "@/components/ui/pagination/Paginator";
import TableAttribute from "@/components/tables/TableAttribute";
import FormFilterAttribute from "@/components/formFilters/FormFilterAttribute";
import DrawerAttribute from "@/components/drawers/DrawerAttribute";
import ItemUtils from "@/utils/item.utils";
import SettingsLayout from "@/components/SettingsLayout";
import { DirectionEnum } from "@/enums";

export default function Attribute() {
  const paginator = useUrlPaginator(ITEM_TYPES.ATTRIBUTE, "/api/attributes", {}, { code: DirectionEnum.ASC });
  const [attribute, setAttribute] = useState(null);

  const handleRefresh = (item) => {
    paginator.replaceItem(item);
    setAttribute(item);
  };

  return (
    <SettingsLayout
      topPanel={<PaginatorTopPanel paginator={paginator} open={setAttribute} />}
      formFilters={<FormFilterAttribute paginator={paginator} />}
    >
      <Paginator paginator={paginator} onCreate={() => setAttribute(ItemUtils.getDefault(ITEM_TYPES.ATTRIBUTE))}>
        <TableAttribute paginator={paginator} open={setAttribute} />
      </Paginator>
      <DrawerAttribute
        item={attribute}
        onClose={() => setAttribute(null)}
        onChange={handleRefresh}
        onRefresh={() => {
          void paginator.goto(1);
        }}
      />
    </SettingsLayout>
  );
}
