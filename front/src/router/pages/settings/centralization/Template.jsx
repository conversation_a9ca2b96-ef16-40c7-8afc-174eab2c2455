import DrawerTemplate from "@/components/drawers/DrawerTemplate";
import FormFilterTemplate from "@/components/formFilters/FormFilterTemplate";
import SettingsLayout from "@/components/SettingsLayout";
import TableTemplate from "@/components/tables/TableTemplate";
import Paginator from "@/components/ui/pagination/Paginator";
import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import { DirectionEnum } from "@/enums";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import ItemUtils from "@/utils/item.utils";
import { useState } from "react";

export default function Template() {
  const paginator = useUrlPaginator(ITEM_TYPES.TEMPLATE, "/api/templates", {}, { code: DirectionEnum.ASC });
  const [template, setTemplate] = useState(null);

  const handleRefresh = (template) => {
    paginator.replaceItem(template);
    setTemplate(template);
  };

  return (
    <SettingsLayout
      topPanel={<PaginatorTopPanel paginator={paginator} open={setTemplate} />}
      formFilters={<FormFilterTemplate paginator={paginator} />}
    >
      <Paginator paginator={paginator} onCreate={() => setTemplate(ItemUtils.getDefault(ITEM_TYPES.TEMPLATE))}>
        <TableTemplate paginator={paginator} open={setTemplate} />
      </Paginator>
      <DrawerTemplate item={template} onClose={() => setTemplate(null)} onChange={handleRefresh} onRefresh={() => void paginator.goto(1)} />
    </SettingsLayout>
  );
}
