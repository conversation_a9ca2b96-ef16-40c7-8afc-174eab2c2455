import { useState } from "react";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import Paginator from "@/components/ui/pagination/Paginator";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import FormFilterUser from "@/components/formFilters/FormFilterUser";
import DrawerUser from "@/components/drawers/DrawerUser";
import TableUser from "@/components/tables/TableUser";
import SettingsLayout from "@/components/SettingsLayout";
import { DirectionEnum } from "@/enums";

export default function Users() {
  const paginator = useUrlPaginator(ITEM_TYPES.USER, "/api/users", {}, { code: DirectionEnum.ASC });
  const [user, setUser] = useState(null);

  return (
    <SettingsLayout topPanel={<PaginatorTopPanel paginator={paginator} open={setUser} />} formFilters={<FormFilterUser paginator={paginator} />}>
      <Paginator paginator={paginator} open={setUser}>
        <TableUser paginator={paginator} open={setUser} />
      </Paginator>
      <DrawerUser
        user={user}
        onClose={() => setUser(null)}
        onChange={(user) => void paginator.replaceItem(user)}
        onRefresh={() => {
          void paginator.goto(1);
        }}
      />
    </SettingsLayout>
  );
}
