import ITEM_TYPES from "@/enums/ITEM_TYPES";
import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import Paginator from "@/components/ui/pagination/Paginator";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import FormFilterUserLogin from "@/components/formFilters/FormFilterUserLogin";
import TableUserLogin from "@/components/tables/TableUserLogin";
import { useSearchParams } from "react-router";
import SettingsLayout from "@/components/SettingsLayout";
import dayjs from "@/libs/dayjs";

export default function UserStats() {
  const [searchParams] = useSearchParams();
  const start = searchParams.get("start");
  const end = searchParams.get("end");

  const firstDayOfMonth = dayjs.from().startOf("month").toISOString();
  const today = dayjs.to().toISOString();

  const paginator = useUrlPaginator(ITEM_TYPES.USER_LOGIN, "/api/user-logins", {}, {}, true, null, {
    start: start ?? firstDayOfMonth,
    end: end ?? today,
  });

  const initialFilters = {
    q: paginator.filters.q ?? "",
    start: paginator.filters.start ?? firstDayOfMonth,
    end: paginator.filters.end ?? today,
  };

  return (
    <SettingsLayout topPanel={<PaginatorTopPanel paginator={paginator} />} formFilters={<FormFilterUserLogin paginator={paginator} initialFilters={initialFilters} defaultDates={[dayjs(firstDayOfMonth), dayjs(today)]} />}>
      <Paginator paginator={paginator}>
        <TableUserLogin paginator={paginator} />
      </Paginator>
    </SettingsLayout>
  );
}
