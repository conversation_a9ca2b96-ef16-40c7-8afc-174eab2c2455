import Tabs from "@/components/ui/tabs/Tabs";
import { useSettings } from "@/contexts/SettingsProvider";
import { useTranslation } from "@/hooks/useTranslation";
import HubDashboard from "@/components/hubDashboard/HubDashboard";
import HubSales from "@/components/hubDashboard/HubSales";
import HubOrders from "@/components/hubDashboard/HubOrders";
import HubProvider from "@/contexts/HubProvider";
import MODULES from "@/enums/MODULES";
import DashboardOptimization from "@/components/dashboards/DashboardOptimization";
import CentralisationIcon from "@/assets/images/drawer/Centralisation.svg?react";
import OptimisationIcon from "@/assets/images/drawer/Optimisation.svg?react";
import { Dashboard } from "@mui/icons-material";
import { useUser } from "@/contexts/UserProvider";
import DashboardCentralization from "@/components/dashboards/DashboardCentralization";
import DashboardDiffusion from "@/components/dashboards/DashboardDiffusion";
import { DashboardTypeEnum } from "@/enums";

export default function Home() {
  const { t } = useTranslation();
  const { hasModule } = useSettings();
  const { preferences, rights } = useUser();

  const tabs = [];
  tabs.push({
    label: t("drawer.centralisation"),
    type: DashboardTypeEnum.HOME,
    icon: <CentralisationIcon />,
    component: <DashboardCentralization />,
  });
  if (rights.isTech()) {
    tabs.push({
      label: t("drawer.diffusion"),
      component: <DashboardDiffusion />,
    });
  }
  if (hasModule(MODULES.OPTIMIZATION)) {
    tabs.push({
      label: t("drawer.optimisation"),
      type: DashboardTypeEnum.OPTIMISATION,
      component: <DashboardOptimization />,
      icon: <OptimisationIcon />,
    });
  }
  if (hasModule(MODULES.HUB)) {
    tabs.push({
      label: t("drawer.hub"),
      type: DashboardTypeEnum.HUB,
      icon: <Dashboard />,
      children: [
        {
          label: t("hubDashboard._"),
          component: (
            <HubProvider>
              <HubDashboard />
            </HubProvider>
          ),
        },
        {
          label: t("hubDashboard.salesReports"),
          component: (
            <HubProvider>
              <HubSales />
            </HubProvider>
          ),
        },
        {
          label: t("hubDashboard.orderReports"),
          component: (
            <HubProvider>
              <HubOrders />
            </HubProvider>
          ),
        },
      ],
    });
  }

  const defaultIndex = tabs.findIndex((tab) => tab.type === preferences.getDashboard()) === -1 ? 0 : tabs.findIndex((tab) => tab.type === preferences.getDashboard());

  return <Tabs tabs={tabs} onTabChange={(v) => preferences.setDashboard(v.type)} defaultTab={defaultIndex} sx={{ p: 2 }} />;
}
