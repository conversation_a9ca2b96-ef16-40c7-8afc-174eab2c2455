import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import DrawerChannel from "@/components/drawers/DrawerChannel";
import TableChannel from "@/components/tables/TableChannel";
import Paginator from "@/components/ui/pagination/Paginator";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import { useState } from "react";
import FormFilterConnector from "@/components/formFilters/FormFilterConnector";
import ItemUtils from "@/utils/item.utils";
import PaginatorLayoutContainer from "@/components/ui/PaginatorLayoutContainer";
import { useChannels } from "@/contexts/ChannelsProvider";
import { DirectionEnum } from "@/enums";

export default function Channel() {
  const { fetchChannels } = useChannels();

  const paginator = useUrlPaginator(ITEM_TYPES.CATALOG_SCOPE, "/api/catalog-scope-summaries", {}, { code: DirectionEnum.ASC }, true, null);

  const [itemForm, setItemForm] = useState(null);

  const handleChangeStatus = (item) => {
    paginator.replaceItem(item);
    setItemForm(item);
  };

  const handleFormSuccess = () => {
    void paginator.goto(1);
    setItemForm(null);
    fetchChannels();
  };

  return (
    <PaginatorLayoutContainer topPanel={<PaginatorTopPanel paginator={paginator} open={setItemForm} />} formFilters={<FormFilterConnector paginator={paginator} />}>
      <Paginator paginator={paginator} onCreate={() => setItemForm(ItemUtils.getDefault(ITEM_TYPES.CATALOG_SCOPE))}>
        <TableChannel paginator={paginator} />
      </Paginator>
      <DrawerChannel item={itemForm} onClose={() => setItemForm(null)} onSuccess={handleFormSuccess} onChangeStatus={handleChangeStatus} />
    </PaginatorLayoutContainer>
  );
}
