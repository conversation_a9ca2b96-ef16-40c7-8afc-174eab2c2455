import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import Paginator from "@/components/ui/pagination/Paginator";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import { useState } from "react";
import ItemUtils from "@/utils/item.utils";
import PaginatorLayoutContainer from "@/components/ui/PaginatorLayoutContainer";
import DrawerImport from "@/components/drawers/DrawerImport";
import TableImport from "@/components/tables/TableImport";
import { DirectionEnum } from "@/enums";

export default function Import() {
  const paginator = useUrlPaginator(ITEM_TYPES.IMPORT, "/api/imports", {}, { names: DirectionEnum.ASC }, true);
  const [item, setItem] = useState(null);

  const onRefresh = () => {
    void paginator.goto(1);
    setItem(null);
  };

  return (
    <PaginatorLayoutContainer topPanel={<PaginatorTopPanel paginator={paginator} open={setItem} />}>
      <Paginator paginator={paginator} onCreate={() => setItem(ItemUtils.getDefault(ITEM_TYPES.IMPORT))}>
        <TableImport paginator={paginator} open={setItem} />
      </Paginator>
      <DrawerImport item={item} onClose={() => setItem(null)} onRefresh={onRefresh} />
    </PaginatorLayoutContainer>
  );
}
