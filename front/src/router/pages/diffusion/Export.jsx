import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import Paginator from "@/components/ui/pagination/Paginator";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import { useState } from "react";
import ItemUtils from "@/utils/item.utils";
import PaginatorLayoutContainer from "@/components/ui/PaginatorLayoutContainer";
import DrawerExport from "@/components/drawers/DrawerExport";
import TableExport from "@/components/tables/TableExport";
import { DirectionEnum } from "@/enums";

export default function Export() {
  const paginator = useUrlPaginator(ITEM_TYPES.EXPORT, "/api/exports", {}, { names: DirectionEnum.ASC }, true);
  const [item, setItem] = useState(null);

  const onRefresh = () => {
    void paginator.goto(1);
    setItem(null);
  };

  return (
    <PaginatorLayoutContainer topPanel={<PaginatorTopPanel paginator={paginator} open={setItem} />}>
      <Paginator paginator={paginator} onCreate={() => setItem(ItemUtils.getDefault(ITEM_TYPES.EXPORT))}>
        <TableExport paginator={paginator} open={setItem} />
      </Paginator>
      <DrawerExport item={item} onClose={() => setItem(null)} onRefresh={onRefresh} />
    </PaginatorLayoutContainer>
  );
}
