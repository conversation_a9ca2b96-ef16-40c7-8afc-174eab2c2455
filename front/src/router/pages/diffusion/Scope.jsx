import { useState } from "react";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import Paginator from "@/components/ui/pagination/Paginator";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import FormFilterScope from "@/components/formFilters/FormFilterScope";
import TableScope from "@/components/tables/TableScope";
import LAYOUTS from "@/enums/LAYOUTS";
import GridScope from "@/components/grid/GridScope";
import DrawerScope from "@/components/drawers/DrawerScope";
import ItemUtils from "@/utils/item.utils";
import PaginatorLayoutContainer from "@/components/ui/PaginatorLayoutContainer";
import { useUser } from "@/contexts/UserProvider";
import { DirectionEnum } from "@/enums";

export default function Scope() {
  const { preferences } = useUser();

  const paginator = useUrlPaginator(ITEM_TYPES.SCOPE, "/api/scopes", {}, { code: DirectionEnum.ASC }, true, preferences.getLayout(ITEM_TYPES.SCOPE));
  const [scope, setScope] = useState(null);

  return (
    <PaginatorLayoutContainer topPanel={<PaginatorTopPanel paginator={paginator} open={setScope} />} formFilters={<FormFilterScope paginator={paginator} />}>
      <Paginator paginator={paginator} onCreate={() => setScope(ItemUtils.getDefault(ITEM_TYPES.SCOPE))}>
        {paginator.view === LAYOUTS.LIST ? <TableScope paginator={paginator} open={setScope} /> : <GridScope paginator={paginator} open={setScope} />}
      </Paginator>
      <DrawerScope
        scope={scope}
        onClose={() => setScope(null)}
        onChange={(scope) => void paginator.replaceItem(scope)}
        onRefresh={() => {
          void paginator.goto(1);
        }}
      />
    </PaginatorLayoutContainer>
  );
}
