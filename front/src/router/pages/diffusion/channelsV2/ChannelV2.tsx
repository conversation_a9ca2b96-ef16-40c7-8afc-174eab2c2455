import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import Paginator from "@/components/ui/pagination/Paginator";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import { useState } from "react";
import FormFilterConnector from "@/components/formFilters/FormFilterConnector";
import PaginatorLayoutContainer from "@/components/ui/PaginatorLayoutContainer";
import type { Channel } from "@/router/pages/diffusion/channelsV2/type";
import TableChannelV2 from "@/components/tables/TableChannelV2";
import DrawerChannelV2 from "@/components/drawers/DrawerChannelV2";
import { DirectionEnum } from "@/enums";

export default function ChannelV2() {
  const paginator = useUrlPaginator(ITEM_TYPES.CHANNEL, "/api/channels", {}, { code: DirectionEnum.ASC }, true, null);

  const [itemForm, setItemForm] = useState<null|Channel>(null);

  const handleFormSuccess = () => {
    void paginator.goto(1);
    setItemForm(null);
  };

  return (
    <PaginatorLayoutContainer topPanel={<PaginatorTopPanel paginator={paginator} open={setItemForm} />} formFilters={<FormFilterConnector paginator={paginator} />}>
      <Paginator paginator={paginator} onCreate={() => setItemForm({} as Channel)}>
        <TableChannelV2 paginator={paginator} />
      </Paginator>
      <DrawerChannelV2 item={itemForm as Channel} onClose={() => setItemForm(null)} onSuccess={handleFormSuccess} onDeleted={() => void paginator.goto(1)} />
    </PaginatorLayoutContainer>
  );
}
