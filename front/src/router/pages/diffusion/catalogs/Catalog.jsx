import DrawerCatalog from "@/components/drawers/DrawerCatalog";
import FormFilterCatalog from "@/components/formFilters/FormFilterCatalog";
import GridCatalog from "@/components/grid/GridCatalog";
import TableCatalog from "@/components/tables/TableCatalog";
import PaginatorLayoutContainer from "@/components/ui/PaginatorLayoutContainer";
import PaginatorTopPanel from "@/components/ui/paginatorTopPanel/PaginatorTopPanel";
import Paginator from "@/components/ui/pagination/Paginator";
import { useUser } from "@/contexts/UserProvider";
import { useView } from "@/contexts/ViewProvider";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import LAYOUTS from "@/enums/LAYOUTS";
import { useUrlPaginator } from "@/hooks/useUrlPaginator";
import ItemUtils from "@/utils/item.utils";
import { useEffect, useState } from "react";
import { DirectionEnum } from "@/enums";

export default function Catalog() {
  const { rights, preferences } = useUser();
  const { view, saveOrder } = useView();
  const catalogView = view?.[ITEM_TYPES.CATALOG];

  const paginator = useUrlPaginator(ITEM_TYPES.CATALOG, "/api/catalogs", {}, catalogView?.order || { code: DirectionEnum.ASC }, true, preferences.getLayout(ITEM_TYPES.CATALOG));

  const [catalog, setCatalog] = useState(null);

  useEffect(() => {
    if (JSON.stringify(paginator.order) !== JSON.stringify(catalogView?.order)) {
      saveOrder(paginator.type, paginator.order);
    }
  }, [paginator.order]);

  return (
    <PaginatorLayoutContainer topPanel={<PaginatorTopPanel paginator={paginator} open={rights.canEditCatalogs() ? setCatalog : null} />} formFilters={<FormFilterCatalog paginator={paginator} />}>
      <Paginator paginator={paginator} onCreate={() => setCatalog(ItemUtils.getDefault(ITEM_TYPES.CATALOG))}>
        {paginator.view === LAYOUTS.LIST ? <TableCatalog paginator={paginator} /> : <GridCatalog paginator={paginator} />}
      </Paginator>
      <DrawerCatalog
        catalog={catalog}
        onClose={() => setCatalog(null)}
        onChange={(catalog) => void paginator.replaceItem(catalog)}
        onRefresh={() => {
          void paginator.goto(1);
        }}
      />
    </PaginatorLayoutContainer>
  );
}
