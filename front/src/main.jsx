import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { createRoot } from "react-dom/client";
import { BrowserRouter, Route, Routes } from "react-router";
import i18n from "i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import Backend from "i18next-http-backend";
import { initReactI18next } from "react-i18next";
import { ThemeProvider } from "@mui/material/styles";

import theme from "@/theme/theme";
import UiProvider from "@/contexts/UiProvider";
import SnackProvider from "@/contexts/SnackProvider";
import ROUTER_ROUTES from "@/router/routes.config";
import LoaderOverlay from "@/components/loaders/LoaderOverlay";
import ApiProvider from "./contexts/ApiProvider";
import { LicenseInfo } from "@mui/x-license";

import "@/assets/fonts/open-sans/open-sans.css";

LicenseInfo.setLicenseKey(import.meta.env.VITE_MUI_X_LICENSE_KEY);

function render(routes) {
  return routes.map((r, i) => (
    <Route key={i} path={r.path} element={r.element}>
      {r.children ? render(r.children) : null}
    </Route>
  ));
}

const container = document.getElementById("root");
const root = createRoot(container);

export const defaultLangs = ["fr_FR", "en_GB"];

let apiRoot = "http://127.0.0.1:8000";
if ("http:" !== document.location.protocol) {
  apiRoot = document.location.origin;
}
if (import.meta.env.VITE_API_ROOT) {
  apiRoot = import.meta.env.VITE_API_ROOT;
}

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    load: "all",
    useSuspense: false,
    backend: {
      loadPath: `${apiRoot}/api/translations/{{lng}}`,
      parse: (data) => JSON.parse(data).messages,
    },
    supportedLngs: defaultLangs,
    fallbackLng: defaultLangs[0],
    debug: false,
    interpolation: {
      escapeValue: false,
    },
  });

root.render(
  <BrowserRouter>
    <DndProvider backend={HTML5Backend}>
      <ThemeProvider theme={theme}>
        <UiProvider>
          <SnackProvider>
            <ApiProvider apiRoot={apiRoot} i18n={i18n}>
              <Routes>{render(ROUTER_ROUTES)}</Routes>
            </ApiProvider>
          </SnackProvider>
          <LoaderOverlay />
        </UiProvider>
      </ThemeProvider>
    </DndProvider>
  </BrowserRouter>,
);
