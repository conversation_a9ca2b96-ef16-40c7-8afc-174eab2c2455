import { useApi } from "@/contexts/ApiProvider";
import { useSnack } from "@/contexts/SnackProvider";
import { t } from "i18next";
import { type FormEvent, useEffect, useState } from "react";

interface FormError {
  violations?: Array<{
    propertyPath: string;
    message: string;
  }>;
}

interface FormProps {
  type: string;
  url: string;
  defaultValues?: Record<string, unknown>;
  onError?: (error: FormError) => void;
  onSuccess?: () => void;
  onDirty?: (isDirty: boolean) => void;
  forceIsReadonly?: boolean;
}

export default function useForm({ type, url, defaultValues = {}, onError = () => {}, onSuccess = () => {}, onDirty = () => {}, forceIsReadonly = false }: FormProps) {
  const [form, setForm] = useState<Record<string, unknown>>(defaultValues);
  const [error, setError] = useState<FormError | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const snack = useSnack();
  const api = useApi();

  const isUpdate = !!form?.["@id"];
  const isReadOnly = forceIsReadonly;

  const submit = async (e: FormEvent): Promise<void> => {
    e.preventDefault();
    e.stopPropagation();
    try {
      setIsLoading(true);
      await api.save(url, form);
      if (onSuccess) {
        onSuccess();
      }
      snack.success(t(form?.["@id"] ? `items.${form?.["@type"]}._saved` : `items.${form?.["@type"] ? form?.["@type"] : type}._created`));
    } catch (error) {
      console.error(error);
      setError(error as FormError);
      if (onError) {
        onError(error as FormError);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const getErrorsOf = (field: string): string[] => {
    return error?.violations?.filter((error) => error.propertyPath === field)?.map((error) => error.message) || [];
  };

  const setNestedState = <T extends Record<string, unknown>>(obj: T, keys: string[], value: unknown): T => {
    const updatedObj = { ...obj };
    let current: Record<string, unknown> = updatedObj;

    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      if (i === keys.length - 1) {
        current[key] = value;
      } else {
        current[key] = current[key] ? { ...current[key] } : {};
        current = current[key] as Record<string, unknown>;
      }
    }

    return updatedObj;
  };

  // @todo WTF le setter avec la value en premier et la clé en second :/
  const setField = (value: unknown, field: string): void => {
    try {
      const keys = field.split(".");

      // To handle nested fields like "names.en" || "value.address.city"
      if (keys.length > 1) {
        setForm(setNestedState(form, keys, value));
        return;
      }

      setForm((prev) => ({ ...prev, [field]: value }));
    } catch (e) {
      console.error(e);
    }
  };

  const getField = (field: string): unknown => {
    try {
      const keys = field.split(".");

      const getNestedValue = (obj: Record<string, unknown>, keys: string[]): unknown => {
        return keys.reduce<unknown>((acc: unknown | undefined, key: string) => {
          return (acc as Record<string, unknown>)?.[key];
        }, obj);
      };

      return keys.length > 0 ? getNestedValue(form, keys) : form[field];
    } catch (e) {
      console.error(e);
      return "";
    }
  };

  useEffect(() => {
    if (onDirty && form) {
      for (const [key, currentValue] of Object.entries(form)) {
        const defaultValue = defaultValues?.[key] ?? null;
        if (defaultValue !== currentValue) {
          onDirty(true);
          return;
        }
      }
      onDirty(false);
    }
  }, [form]);

  const fieldGetTranslation = (key: string): string => {
    const translation = t(key);
    return translation === key ? "" : translation;
  };

  const register = (name: string, isTargetValue = false) => ({
    name,
    label: fieldGetTranslation(`items.${type}.${name}`),
    helper: fieldGetTranslation(`items.${type}.${name}_help`),
    error: getErrorsOf(name).join(", "),
    onChange: (value: unknown, value2?: boolean | undefined) => {
      if (typeof value2 === "boolean") {
        return setField(value2, name);
      }
      if (isTargetValue && typeof value === "object" && value !== null && "target" in value && value.target && typeof value.target === "object" && "value" in value.target) {
        return setField(value.target.value, name);
      }
      return setField(value, name);
    },
    value: getField(name),
    errors: getErrorsOf(name),
    disabled: isReadOnly,
  });

  return {
    form,
    setForm,
    isLoading,
    submit,
    getErrorsOf,
    setField,
    register,
    isReadOnly,
    isUpdate,
    id: form?.["@type"],
    type,
    getField,
    defaultValues,
  };
}
