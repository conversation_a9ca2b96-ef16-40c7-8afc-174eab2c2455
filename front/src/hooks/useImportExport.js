import { useSettings } from "@/contexts/SettingsProvider";
import { useLocales } from "@/contexts/LocalesProvider";
import { useScopes } from "@/contexts/ScopesProvider";
import { useAttributes } from "@/contexts/AttributesProvider";
import { useAttributeGroups } from "@/contexts/AttributeGroupsProvider";
import { useHubSources } from "@/contexts/HubSourcesProvider";
import { HEADER_TYPES } from "@/utils/models/Header";
import { useChannels } from "@/contexts/ChannelsProvider";
import { ApiTypeEnum, ModuleEnum } from "@/enums";

const EXPORTABLE_TYPES_BY_TYPE = {
  [ApiTypeEnum.CATALOG_SCOPE]: [ApiTypeEnum.CATALOG_SCOPE],
  [ApiTypeEnum.COMPLETUDE]: [ApiTypeEnum.COMPLETUDE],
  [ApiTypeEnum.LOCALE]: [ApiTypeEnum.LOCALE],
  [ApiTypeEnum.TEMPLATE]: [ApiTypeEnum.TEMPLATE],
  [ApiTypeEnum.ATTRIBUTE_GROUP]: [ApiTypeEnum.ATTRIBUTE_GROUP],
  [ApiTypeEnum.ATTRIBUTE]: [ApiTypeEnum.ATTRIBUTE],
  [ApiTypeEnum.ATTRIBUTE_OPTION]: [ApiTypeEnum.ATTRIBUTE_OPTION],
  [ApiTypeEnum.CURRENCY]: [ApiTypeEnum.CURRENCY],
  [ApiTypeEnum.SCOPE]: [ApiTypeEnum.SCOPE],
  [ApiTypeEnum.PRODUCT]: [ApiTypeEnum.PRODUCT],
  [ApiTypeEnum.RULE]: [ApiTypeEnum.RULE],
  [ApiTypeEnum.RULE_GROUP]: [ApiTypeEnum.RULE_GROUP],
  [ApiTypeEnum.HUB_SOURCE]: [ApiTypeEnum.HUB_SOURCE],
  [ApiTypeEnum.HUB_STOCK]: [ApiTypeEnum.HUB_STOCK],
  [ApiTypeEnum.HUB_ORDER]: [ApiTypeEnum.HUB_ORDER, ApiTypeEnum.HUB_ORDER_ITEM, ApiTypeEnum.HUB_ORIGIN, ApiTypeEnum.HUB_INVOICE],
  [ApiTypeEnum.MEDIA]: [ApiTypeEnum.MEDIA],
  [ApiTypeEnum.CONTENT]: [ApiTypeEnum.CONTENT],
  [ApiTypeEnum.USER]: [ApiTypeEnum.USER],
  [ApiTypeEnum.DICTIONARY_MAPPING]: [ApiTypeEnum.DICTIONARY_MAPPING],
};

/**
 * @param {String} type
 * @returns {{
 *   type: string,
 *   exists: boolean,
 *   subTypes: string[],
 *   isMultiple: boolean,
 *   refresh: (() => void),
 *   isDocument: boolean,
 *   headerTypes: string[],
 *   showProperties: boolean,
 * }}
 */
export default function useImportExport(type) {
  const { hasModule } = useSettings();
  const { fetchLocales } = useLocales();
  const { fetchScopes } = useScopes();
  const { fetchAttributes } = useAttributes();
  const { fetchChannels } = useChannels();
  const { fetchAttributeGroups } = useAttributeGroups();
  const { fetchHubSources } = useHubSources();

  const subTypes = EXPORTABLE_TYPES_BY_TYPE[type] ?? [];
  const exists = hasModule(ModuleEnum.EXPORT) && 0 < subTypes.length;
  const isMultiple = 1 < subTypes.length;

  const refresh = () => {
    const fetchAttributesAndAttributeGroups = () => {
      fetchAttributes();
      fetchAttributeGroups();
    };

    ({
      [ApiTypeEnum.LOCALE]: fetchLocales,
      [ApiTypeEnum.ATTRIBUTE_GROUP]: fetchAttributeGroups,
      [ApiTypeEnum.ATTRIBUTE]: fetchAttributesAndAttributeGroups,
      [ApiTypeEnum.SCOPE]: fetchScopes,
      [ApiTypeEnum.CATALOG_SCOPE]: fetchChannels,
      [ApiTypeEnum.HUB_SOURCE]: fetchHubSources,
    }[type] || (() => null))();
  };

  let headerTypes = [
    HEADER_TYPES.PROPERTY,
  ];

  const isDocument = [
    ApiTypeEnum.PRODUCT,
    ApiTypeEnum.MEDIA,
    ApiTypeEnum.CONTENT,
    ApiTypeEnum.CATEGORY,
  ].includes(type);

  if (isDocument) {
    headerTypes = [
      HEADER_TYPES.PROPERTY,
      HEADER_TYPES.ATTRIBUTE,
      HEADER_TYPES.COMPLETUDE,
      HEADER_TYPES.WORKFLOW,
    ];
  }

  // @todo wtf just for ItemHeaderTreeView
  const showProperties = headerTypes.includes(HEADER_TYPES.PROPERTY);

  return {
    type,
    exists,
    subTypes,
    isMultiple,
    refresh,
    isDocument,
    headerTypes,
    showProperties,
  };
}
