import { useEffect } from "react";
import { useSearchParams } from "react-router";
import { usePaginator } from "./usePaginator";

export const useUrlPaginator = (type, iri, permanentFilters = {}, order = {}, pagination = true, view = null, defaultFilters = {}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const params = Object.fromEntries([...searchParams]);
  let newOrder = {};
  const hasDefaultOrder = Object.keys(params).find((e) => e.includes("order"));

  if (hasDefaultOrder) {
    const key = hasDefaultOrder.split("[")[1].split("]")[0];
    newOrder[key] = params[hasDefaultOrder];
    delete params[hasDefaultOrder];
  }

  const mapData = (data) => {
    let newParams = {};
    Object.keys(data).forEach((key) => {
      if (data[key] === "undefined") {
        return;
      }
      if (key === "filters") {
        Object.keys(data[key]).forEach((k) => {
          newParams[k] = data[key][k];
        });
      } else if (key === "order") {
        Object.keys(data[key]).forEach((k) => {
          const name = `order[${k}]`;
          newParams[name] = data[key][k];
        });
      } else if (Array.isArray(data[key])) {
        if (data[key].length === 0) {
          return;
        }
        newParams[key] = data[key].join(',');
      } else if (typeof data[key] === "string") {
        const arrayValue = data[key].split(',').map(item => item.trim());
        newParams[key] = arrayValue.length > 1 ? arrayValue : data[key];
      } else {
        newParams[key] = data[key];
      }
    });
    return newParams;
  };

  const paginator = usePaginator(type, iri, permanentFilters, Object.keys(newOrder).length ? newOrder : order, pagination, mapData({ ...params, ...defaultFilters }), view);

  const setUrl = (params) => {
    setSearchParams(mapData(params));
  };

  useEffect(() => {
    setUrl({
      page: paginator.page,
      order: paginator.order,
      ...paginator.filters,
    });
  }, [paginator.page, paginator.order, paginator.filters]);

  return paginator;
};
