// Hook to switch between api and raw enum with search because problem of paginator hook for selects

// Mode defaultOptions = enums [{"@id": "1", "name": "Option 1"}, {"@id": "2", "name": "Option 2"}]
// Mode identifier = get options from api/${identifier}

import { useCallback, useEffect, useState } from "react";
import { debounce } from "@mui/material";
import { useTranslation } from "./useTranslation";
import { useApi } from "@/contexts/ApiProvider";

const PAGINATION_LIMIT = 10;

export default function useSelect({ defaultValue, identifier, getterKey, defaultOptions = [], params = {}, permOptions = [], allowCreate = false, identifierKey = "code" }) {
  const { t } = useTranslation();
  const api = useApi();

  const [values, setValues] = useState([]);
  const [defaultValues, setDefaultValues] = useState(defaultValue ? (Array.isArray(defaultValue) ? defaultValue : [defaultValue]) : []);
  const [options, setOptions] = useState(defaultOptions);
  const [optionsFiltered, setOptionsFiltered] = useState([]);
  const [isOptionsLoaded, setIsOptionsLoaded] = useState(false);
  const [loading, setLoading] = useState(false);
  const [startLoading, setStartLoading] = useState(!!defaultValue && defaultValue.length);
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState(null);
  const [pageCount, setPageCount] = useState(1);

  const getter = (item, value) => item[getterKey] === value;

  const getOptionsAsync = async (page) => {
    setLoading(true);

    try {
      const response = await api.get(`/api/${identifier}`, { page, limit: PAGINATION_LIMIT, ...params, q: search ?? "" });

      const data = response["hydra:member"];
      const count = response["hydra:totalItems"] ?? 0;
      setPageCount(Math.ceil(count / PAGINATION_LIMIT));

      const optionsFiltered = page !== 1 ? data : [...permOptions, ...data];
      setOptions(optionsFiltered);
      setIsOptionsLoaded(true);

      setValues((prev) => {
        if (prev.length === 0 && defaultValues.length > 0) {
          return defaultValues.map((value) => {
            const updatedOption = optionsFiltered.find((item) => getter(item, value));
            return updatedOption ? updatedOption : value;
          });
        }
        return prev.map((value) => {
          const updatedOption = optionsFiltered.find((item) => getter(item, value));
          return updatedOption ? updatedOption : value;
        });
      });
    } catch (error) {
      console.error(error);
      setIsOptionsLoaded(false);
    } finally {
      setLoading(false);
      setStartLoading(false);
    }
  };

  const addMissingOptions = async (options, defaultValues) => {
    let optionsWithDefaultValues = options ?? [];

    if (defaultValues.length === 0) {
      setIsOptionsLoaded(true);
      return;
    }

    const missingOptions = defaultValues.filter((value) => !options.some((item) => getter(item, value)));

    if (missingOptions.length === 0) {
      setStartLoading(false);
      setIsOptionsLoaded(true);
      return;
    }

    // IF IRI, GET CODES
    const missingCodes = missingOptions?.map((option) => (option?.code ? option.code : typeof option === "string" ? option.replace(`/api/${identifier}/`, "") : option.uuid));
    const missingCodesObject = missingCodes.reduce((acc, code, index) => {
      acc[`${identifierKey}[${index}]`] = encodeURIComponent(code);
      return acc;
    }, {});

    try {
      const missingResponse = await api.get(`/api/${identifier}`, { ...missingCodesObject, limit: missingCodes?.length, ...params });
      const missingData = missingResponse["hydra:member"];

      if (allowCreate) {
        for (const option of missingCodes) {
          if (!missingData.some((item) => item[getterKey] === option)) {
            missingData.push({ [getterKey]: option, code: option, [allowCreate]: option });
          }
        }
      }

      optionsWithDefaultValues = [...missingData, ...options, ...permOptions];

      setValues(
        defaultValues.map((value) => {
          const updatedOption = optionsWithDefaultValues.find((item) => getter(item, value));
          return updatedOption ? updatedOption : value;
        }),
      );
      setIsOptionsLoaded(true);
    } catch (error) {
      console.error(error);
      setIsOptionsLoaded(false);
    } finally {
      setStartLoading(false);
    }
  };

  useEffect(() => {
    if (defaultValues.length === 0) {
      setValues([]);
      setStartLoading(false);
      setIsOptionsLoaded(true);
    } else if (!values?.some((value) => defaultValues.includes(value["@id"] || value?.sku || value?.[getterKey]))) {
      if (identifier) {
        addMissingOptions(options, defaultValues);
      } else {
        setValues(
          defaultValues.map((value) => {
            const updatedOption = defaultOptions.find((item) => getter(item, value));
            return updatedOption ? updatedOption : value;
          }),
        );
        setIsOptionsLoaded(true);
        setStartLoading(false);
      }
    }
  }, [defaultValues, options]);

  const fetch = (page = 1) => {
    if (identifier) {
      getOptionsAsync(page);
    } else {
      if (optionsFiltered?.length > 0) {
        setOptions(optionsFiltered.slice(10 * page - 10, 10 * page));
      }
      setStartLoading(false);
      setOptions(defaultOptions.slice(10 * page - 10, 10 * page));
      setPageCount(Math.ceil(defaultOptions.length / 10));
      setIsOptionsLoaded(true);
    }
  };

  const reset = () => {
    setLoading(true);
    handleSearch("");
    setPage(1);
    setPageCount(1);
    setIsOptionsLoaded(false);
  };

  const searchOption = async (search) => {
    setLoading(true);
    setIsOptionsLoaded(false);
    try {
      const response = await api.get(`/api/${identifier}`, { page, limit: 10, q: search ?? "", ...params });
      const data = response["hydra:member"];
      if (allowCreate && data.length === 0) {
        const missingLabel = t("actions.add_not_found", { search, identifier });
        data.push({ "@id": search, code: missingLabel, [allowCreate]: search });
      }
      setOptions(data);
      setPage(1);
      setPageCount(Math.ceil(response["hydra:totalItems"] / 10));
      setIsOptionsLoaded(true);
    } catch (error) {
      console.error(error);
      setIsOptionsLoaded(false);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = useCallback(
    debounce((e) => {
      const value = e?.target?.value;

      if (value === null || value === undefined) {
        return;
      }

      if (identifier) {
        if (value) {
          searchOption(value);
        } else {
          fetch();
          setPage(1);
        }
        return;
      }

      setIsOptionsLoaded(false);
      if (value) {
        const optionsFiltered = defaultOptions.filter((item) =>
          item.name
            ? item.name.toLowerCase().includes(e?.target?.value.toLowerCase())
            : t(item.names)?.trim().toLowerCase().includes(e?.target?.value?.trim().toLowerCase()) || item.code?.trim().toLowerCase().includes(e?.target?.value?.trim().toLowerCase()),
        );
        setOptionsFiltered(optionsFiltered);

        setPageCount(Math.ceil(optionsFiltered.length / 10));
        setOptions(optionsFiltered.slice(0, 10));
      } else {
        setOptionsFiltered(defaultOptions);
        setPageCount(Math.ceil(defaultOptions.length / 10));
        setOptions(defaultOptions.slice(0, 10));
      }
      setPage(1);
      setIsOptionsLoaded(true);
    }, 300),
    [],
  );

  return {
    defaultOptions,
    options,
    setOptions,
    loading,
    fetch,
    reset,
    handleSearch,
    page,
    setPage,
    pageCount,
    search,
    setSearch,
    values,
    setValues,
    startLoading,
    setStartLoading,
    defaultValues,
    setDefaultValues,
    getterKey,
    isOptionsLoaded,
    setIsOptionsLoaded,
  };
}
