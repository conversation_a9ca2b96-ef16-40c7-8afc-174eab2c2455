import { createContext, useContext, useEffect, useState } from "react";
import { useUser } from "@/contexts/UserProvider";
import useDidMountEffect from "./useDidMountEffect";
import { useApi } from "@/contexts/ApiProvider";
import { DirectionEnum } from "@/enums";

export const usePaginator = (type, iri, permanentFilters = {}, order = {}, pagination = true, defaultFilters = {}, view = null, start = true) => {
  const { preferences } = useUser();
  const api = useApi();

  const DEFAULT_PAGINATOR = {
    count: 0,
    pageCount: 1,
    view,
    items: [],
  };

  const [isLoading, setIsLoading] = useState(false);

  const defaultUrlFilters = {
    ...defaultFilters,
  };
  if (defaultUrlFilters?.limit) {
    delete defaultUrlFilters.limit;
  }
  if (defaultUrlFilters?.page) {
    delete defaultUrlFilters.page;
  }
  const [query, setQuery] = useState(() => ({
    limit: preferences?.getLimit(),
    page: Number(defaultFilters?.page) || 1,
    filters: { ...permanentFilters, ...defaultUrlFilters },
    order,
  }));
  const [paginator, setPaginator] = useState(() => DEFAULT_PAGINATOR);

  const [isStarting, setIsStarting] = useState(start);

  // this method helps fixing order query parameters
  // eg: order[names]=asc become order[names][fr_FR]=asc
  // eg: order[counters.uses]=asc become order[counters][uses]=asc
  const fixQueryOrder = (order) => {
    const fixed = {};
    Object.keys(order).map((key) => {
      // @todo fix hardcoded translations
      if (["names", "descriptions"].includes(key) && "string" === typeof order[key]) {
        fixed[key] = { [preferences.getLang()]: order[key] };
      } else if (-1 !== key.indexOf(".")) {
        const parts = key.split(".");
        fixed[parts[0]] = fixed[parts[0]] || {};
        fixed[parts[0]][parts[1]] = order[key];
      } else {
        fixed[key] = order[key];
      }
    });
    return fixed;
  };

  const cleanFilters = (o) => {
    const n = {};
    Object.keys(o).forEach((k) => {
      const v = o[k];
      if (Array.isArray(v) && v.length) {
        n[k] = v;
      }
      if (("string" !== typeof v || "" !== v.trim()) && !Array.isArray(v)) {
        n[k] = v;
      }
      if (v === "active") {
        n[k] = true;
      }
      if (v === "inactive") {
        n[k] = false;
      }
    });

    return n;
  };

  const fetch = async (filters = {}, order = null, page = null, limit = null) => {
    setQuery((p) => ({
      ...p,
      // "permanentFilters" are always kept!
      filters: cleanFilters({ ...(Object.keys(filters || {}).length ? filters : p.filters), ...permanentFilters }),
      order: order ?? p.order,
      page: page ?? p.page,
      limit: limit ?? p.limit,
    }));
  };

  const fetchQuery = async (options, limit) => {
    setIsLoading(true);
    try {
      const response = await api.get(iri, options);
      const count = response["hydra:totalItems"] ?? response["hydra:member"]?.length ?? 0;

      const items = response["hydra:member"] ?? [];
      const sortedItems = items.sort((a, b) => {
        const posA = typeof a?.position === "number" ? a.position : Infinity;
        const posB = typeof b?.position === "number" ? b.position : Infinity;
        return posA - posB;
      });

      setPaginator((p) => ({
        ...p,
        count: count,
        pageCount: pagination ? Math.ceil(count / limit) : 1,
        items: sortedItems,
      }));
    } catch (e) {
      console.error("error", e);
      setPaginator((p) => ({
        ...p,
        ...DEFAULT_PAGINATOR,
      }));
    } finally {
      setIsLoading(false);
      if (isStarting) {
        setIsStarting(false);
      }
    }
  };

  const handleFetch = () => {
    const options = {
      ...query.filters,
      ...(pagination
        ? {
            page: query.page,
            limit: query.limit,
          }
        : {
            pagination: false,
          }),
      order: fixQueryOrder(query.order),
    };

    // Convert string values containing commas to arrays
    Object.keys(options).forEach((key) => {
      if (typeof options[key] === "string" && options[key].includes(",")) {
        options[key] = options[key].split(",").map((item) => item.trim());
      }
    });

    void fetchQuery(options, query.limit);
  };

  useEffect(() => {
    if (start) {
      handleFetch();
    }
  }, []);

  useDidMountEffect(() => {
    handleFetch();
  }, [JSON.stringify(query)]);

  return useContext(
    createContext({
      // helper to retrieve paginator configuration
      type,
      // states
      isLoading,
      isStarting,
      // query states
      filters: query.filters,
      permanentFilters,
      order: query.order,
      page: query.page,
      limit: query.limit,
      // paginator states
      items: paginator.items,
      count: paginator.count,
      pagination,
      pageCount: paginator.pageCount,
      view: paginator.view,
      // simple methods
      fetch,
      goto: async (page) => {
        if (query.page === page) {
          setQuery((p) => ({ ...p, _: new Date().getTime() }));
        } else {
          await fetch(null, null, page);
        }
      },
      // "submit" is a shortcut to handle forms
      submit: async (filters = {}) => {
        await fetch(filters, null, 1, query.limit ?? 12);
      },
      // "replaceItem" is able to reload the paginator only if needed when an item changes
      replaceItem: async (item) => {
        if (paginator.items.some((i) => i?.["@id"] === item?.["@id"])) {
          setPaginator((p) => ({ ...p, items: p.items.map((i) => (i?.["@id"] === item?.["@id"] ? item : i)) }));
        } else {
          await fetch(null, null, 1);
        }
      },
      // "setLimit" is able to change the limit
      setLimit: async (limit) => await fetch(null, null, 1, limit),
      // "setView" is able to change the view
      setView: (view) =>
        setPaginator((p) => ({
          ...p,
          view,
        })),
      // "orderBy" is able to change ordering
      orderBy: async (key, direction = DirectionEnum.ASC) => await fetch(null, { [key]: direction }),
      // "isReadonly" detects if a filter is readonly
      isReadonly: (key) => Object.prototype.hasOwnProperty.call(permanentFilters, key),
      moveOrder: async (startIndex, endIndex, saveOnMove = false) => {
        if (pagination) {
          return;
        }

        const updatedItems = Array.from(paginator.items);
        const [draggedItem] = updatedItems.splice(startIndex, 1);
        updatedItems.splice(endIndex, 0, draggedItem);
        setPaginator((p) => ({
          ...p,
          items: updatedItems,
        }));

        if (saveOnMove) {
          await api.post("/api/orders", {
            type,
            orders: updatedItems.map((item, idx) => ({
              identifier: item.id || item.code,
              position: idx,
            })),
          });
        }
      },
      saveOrders: async (onSuccess, customKey) => {
        if (pagination) {
          return;
        }

        await api.post("/api/orders", {
          type,
          orders: paginator.items.map((item, idx) => ({
            identifier: item?.[customKey]?.id || item.id || item.code,
            position: idx,
          })),
        });
        if (onSuccess) {
          onSuccess();
        }
      },
    }),
  );
};
