import { useEffect, useMemo, useState } from "react";
import { useSearchParams } from "react-router";
import TreeItemCategory from "@/components/ui/treeItems/TreeItemCategory";
import ItemUtils from "@/utils/item.utils";
import { useTranslation } from "@/hooks/useTranslation";
import { CircularProgress, Stack } from "@mui/material";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { useApi } from "@/contexts/ApiProvider";
import { DirectionEnum } from "@/enums";

export default function useTree(type, iri, permFilters = {}) {
  const { t } = useTranslation();
  const api = useApi();

  const [items, setItems] = useState(() => []);
  const [current, setCurrent] = useState(() => null);
  const [startLoading, setStartLoading] = useState(true);
  const [loading, setLoading] = useState(true);

  const [searchParams, setSearchParams] = useSearchParams();
  const getCategoryKey = () => {
    if (type === ITEM_TYPES.CATEGORY) {
      return "category";
    }
    if (type === ITEM_TYPES.USER_GROUP) {
      return "userGroup";
    }
    return "folder";
  };
  const category = searchParams.get(getCategoryKey()); // The current category in the URL

  const deduplicate = (items) => {
    const array = [];
    items.forEach((item) => {
      if (!array.some((i) => i["@id"] === item["@id"])) {
        array.push(item);
      }
    });

    return array;
  };

  const getItemIdentifier = (item) => item?.id ?? item?.uuid;

  const fetch = async (queries, item = null) => {
    setLoading(true);
    const itemIdentifier = getItemIdentifier(item);

    let queryParams = {
      ...permFilters,
      ...queries,
      pagination: false,
      "order[name]": DirectionEnum.ASC,
    };

    if (startLoading && category) {
      queryParams.breadcrumbPreloadId = category;
    } else {
      queryParams = {
        ...queryParams,
        breadcrumb: itemIdentifier ? `${item.breadcrumb.trail}${itemIdentifier}/` : "/",
        "breadcrumbDepth[lte]": itemIdentifier ? item.breadcrumb.depth + 2 : 2,
      };
    }

    const more = await api.get(iri, queryParams);

    setItems((items) => deduplicate([...more["hydra:member"], ...items]));
    if (startLoading) {
      setStartLoading(false);
    }

    const currentItem = more["hydra:member"].find((i) => i.id === Number(category) || i.uuid === category);

    if (category && !current) {
      setCurrent(currentItem);
    }

    setLoading(false);
    return more["hydra:member"];
  };

  const open = async (item) => {
    const itemIdentifier = getItemIdentifier(item);

    if (item?.["@type"] === type) {
      setSearchParams({ [getCategoryKey()]: itemIdentifier });
      setCurrent(item);

      // then, when opening, only pre-fetch the next level (to know if we have children or not)
      void fetch({}, item);
    }
    // Handle the case when clicking on a breadcrumb part and maybe not in the tree
    else if (itemIdentifier) {
      const findItem = items.find((i) => (i.id ? i.id === item?.id : i.uuid === item?.uuid));
      const currentItem = findItem ? findItem : await api.get(`${iri}/${itemIdentifier}`);
      setSearchParams({ [getCategoryKey()]: itemIdentifier });
      setCurrent(currentItem);
      if (!findItem) {
        void fetch({}, currentItem);
      }
    } else {
      setCurrent(null);
      setSearchParams();
      void fetch();
    }
  };

  const onMove = (dragIndex, hoverIndex, parent) => {
    setItems((prevCards) => {
      const newState = [...prevCards];
      const dragItem = newState.findIndex((i) => i.parent === parent && i.position === dragIndex);
      const dropItem = newState.findIndex((i) => i.parent === parent && i.position === hoverIndex);
      if (dragItem === -1 || dropItem === -1) {
        const newItems = newState.filter((i) => i.parent === parent);
        newItems.forEach((item, index) => {
          item.position = index;
        });
        const state = newState.filter((i) => i.parent !== parent);
        return [...state, ...newItems];
      }
      newState[dragItem].position = hoverIndex;
      newState[dropItem].position = dragIndex;
      return newState;
    });
  };

  const onDrop = async () => {
    try {
      await api.post("/api/orders", {
        type: "Category",
        orders: items.map((item) => ({
          identifier: item.uuid,
          position: item.position,
        })),
      });
    } catch (error) {
      console.log(error);
    }
  };

  const render = (parentIri = null, parentItem = null) => {
    if (startLoading) {
      return (
        <Stack py={2} alignItems="center">
          <CircularProgress size={20} />
        </Stack>
      );
    }
    if (!items.length && !startLoading) {
      return (
        <Stack py={1} px={4}>
          <span>{t("common.no_result")}</span>
        </Stack>
      );
    }
    return items
      .filter((item) => item.parent === parentIri || (null !== parentIri && item.parent === ItemUtils.toId(parentIri))) // @todo that's bad, only for the categories because (for no reason) parent is not an iri
      .sort((a, b) => a.position - b.position || a.name.localeCompare(b.name))
      .map((item, i) => (
        <TreeItemCategory type={type} onMove={onMove} onDrop={onDrop} key={item["@id"]} index={i} open={open} item={item} onSuccess={() => fetch()} onDelete={(item) => deleteItem(item)} items={items} parentItem={parentItem}>
          {render(item["@id"], parentItem)}
        </TreeItemCategory>
      ));
  };

  useEffect(() => {
    // at first, only fetch first + second levels (to know what's empty and what's not)
    void fetch();
  }, []);

  const deleteItem = (item) => {
    const id = item?.["@id"] || item;
    setItems((items) => items.filter((i) => i["@id"] !== id));
    if (current?.["@id"] === id) {
      setCurrent(null);
      setSearchParams();
    }
  };

  const getExpandedItems = (items, category) => {
    if (items.length === 0) {
      return [];
    }

    if (!current) {
      return [];
    }

    const expandedItems = [];
    let currentItm = items.find((i) => i.id === Number(category) || i.uuid === category);

    while (currentItm?.parent) {
      const parentItem = items.find((i) => i["@id"] === currentItm.parent || i.uuid === currentItm.parent);
      if (parentItem) {
        expandedItems.unshift(parentItem["@id"]);
        currentItm = parentItem;
      } else {
        break;
      }
    }

    return [...expandedItems, current["@id"]];
  };

  const expandedItems = useMemo(() => {
    return category ? getExpandedItems(items, category) : [];
  }, [category, items]);

  const childrens = items.filter((item) => (category ? item.parent === `${iri}/${category}` : item.parent === null));

  // https://mui.com/x/react-tree-view/rich-tree-view/customization/#common-examples
  return {
    type,
    open,
    close,
    replaceItem: (item) => {
      setItems((items) => items.map((i) => (i["@id"] === item["@id"] ? item : i)));
      // if (current?.["@id"] === item?.["@id"]) {
      //   setCurrent(item);
      // }
      void fetch();
    },
    deleteItem,
    refresh: () => {
      // @todo
    },
    fetch,
    current,
    loading,
    items,
    render,
    urlParams: category,
    expandedItems,
    childrens,
  };
}
