import { createContext, useContext } from "react";
import { usePaginator } from "@/hooks/usePaginator";
import { ApiTypeEnum } from "@/enums";

const UnitsContext = createContext({});

export const useUnits = () => useContext(UnitsContext);

export default function UnitsProvider({ children }) {
  const paginator = usePaginator(ApiTypeEnum.UNIT, "/api/units", {}, {}, false);

  const fetchUnits = async () => {
    await paginator.goto(1);
  };

  return (
    <UnitsContext.Provider value={{ units: paginator.items, fetchUnits }}>
      {children}
    </UnitsContext.Provider>
  );
}
