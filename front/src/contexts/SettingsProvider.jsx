import { createContext, useContext, useState } from "react";
import { useApi } from "./ApiProvider";

const SettingsContext = createContext({});

/**
 * @typedef {{ code: string, name: string }} Client
 * @typedef {{ name: string, maxUsers: number, maxDiskUsage: number }} Contract
 * @typedef {{ uploadMaxFilesize: number, uploadMaxFilesizeLabel: string, maxFileUploads: number }} Server
 * @typedef {{ version: string, locales: [], modules: [] }} Environment
 * @typedef {{ code: string, type: string, name: string, in: string[], out: string[] }} Connector
 * @typedef {{ id: number, client: Client, contract: Contract, server: Server, environment: Environment, connectors: Connector[], ui: UiDefinition }} Settings
 * @returns {{
 *   settings: Settings,
 *   getConnector: (code: string) => Connector,
 *   hasModule: (code: string) => Boolean
 * }}
 */
export const useSettings = () => useContext(SettingsContext);

export default function SettingsProvider({ children }) {
  const [isLoading, setIsLoading] = useState(false);
  const [settings, setSettings] = useState(null);
  const api = useApi();

  const fetchSettings = async () => {
    const s = await api.get("/api/settings/0");
    setSettings(s);
  };

  const load = async () => {
    if (!isLoading) {
      setIsLoading(true);
      try {
        await fetchSettings();
      } catch (error) {
        console.log(error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  if (null === settings) {
    void load();

    return;
  }

  const getConnector = (code) => settings.connectors.find((connector) => connector.code === code);
  const hasModule = (code) => settings.environment.modules.includes(code);

  const isLVMH = () => settings.client.code === "lvmh" || settings.client.code === "lvmh_staging";

  return (
    <SettingsContext.Provider
      value={{
        settings,
        defaultLang: settings?.environment.langs[0],
        langs: settings?.environment.langs,
        getConnector,
        hasModule,
        fetchSettings,
        // @todo fix the lowercase in back
        getUiDetailAttributeCode: (type, position) => settings.ui[type.toLowerCase()].detail[position],
        isLVMH,
      }}
    >
      {children}
    </SettingsContext.Provider>
  );
}
