import { createContext, useContext, useEffect, useState } from "react";
import { Box, Card, CircularProgress, Divider, IconButton, Snackbar, Stack } from "@mui/material";
import { Subtitle2 } from "@/components/ui/Typography";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import LoaderProgress from "@/components/loaders/LoaderProgress";
import ImageIcon from "@mui/icons-material/Image";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import { useTranslation } from "@/hooks/useTranslation";
import { generatePath, Link } from "react-router";
import { useApi } from "./ApiProvider";
import FileUtils from "@/utils/file.utils";
import TASK_TYPES from "@/enums/TASK_TYPES";
import { RouteEnum } from "@/enums";

const TasksContext = createContext({});

/**
 * @typedef {File[]} Array of files
 * @typedef {string} iri
 * @property {function(File[], string): void} addTasks - Function to add tasks
 * @returns {{ addTasks: (tasks, iri) => void, handleClose: () => void }}
 */

export const useTasks = () => useContext(TasksContext);

export default function TasksProvider({ children }) {
  const { t } = useTranslation();
  const [medias, setMedias] = useState([]);
  const [imports, setImports] = useState([]);
  const [exports, setExports] = useState([]);
  const [uploadProgress, setUploadProgress] = useState({});
  const [isCollapsed, setIsCollapsed] = useState(false);
  const api = useApi();

  const removeAll = () => {
    setMedias([]);
    setUploadProgress({});
    setImports([]);
    setExports([]);
  };

  const handleClose = (event, reason) => {
    if ("clickaway" !== reason) {
      removeAll();
    }
  };

  const getError = (e) => {
    if (e.status === 409 || e.status === 422) {
      const errorMessage = e.data["hydra:description"];
      const headers = new Headers(e.headers);
      const location = headers.get("X-Location");

      return (
        <span>
          {errorMessage}
          {t("common.points")}{" "}
          <Link style={{ color: "inherit" }} to={generatePath(RouteEnum.DAM_DETAIL, { uuid: location.replace("/api/medias/", "") })} target="_blank" rel="noreferrer">
            {t("actions.see")}
          </Link>
        </span>
      );
    }
  };

  const upload = async (file, folder) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onloadend = () => {
        const blob = new Blob([file], { name: file.name });

        const formData = new FormData();
        formData.append("file", blob, file.name);
        formData.append("path", file.path);
        if (folder) {
          formData.append("folder", folder.id);
        }

        api
          .postXMLFormData("/api/medias", formData, file.name, (fileName, progress) => {
            setUploadProgress((prev) => ({
              ...prev,
              [fileName]: progress,
            }));
          })
          .then((result) => {
            resolve(result);
            // setUploadedFiles((prev) => new Set(prev).add(file.name));
          })
          .catch((e) => {
            setUploadProgress((prev) => ({
              ...prev,
              [file.name]: getError(e),
            }));
            reject(e);
          });
      };
    });
  };

  const uploadAll = async (medias, folder, onSuccess) => {
    const results = [];
    for (const file of medias) {
      try {
        const result = await upload(file, folder);
        results.push(result);
      } catch (error) {
        console.error("Error uploading medias:", error);
      }
    }

    if (onSuccess) {
      onSuccess(results);
    }
  };

  // For later if we want to go back to mode manual or maybe in preferences idk
  const auto = true;

  const addTasks = (type, params) => {
    if (!type) {
      return;
    }

    if (type === TASK_TYPES.MEDIA) {
      return addMedias(params.files, params.folder, params.onSuccess);
    }

    if (type === TASK_TYPES.IMPORT) {
      return addImports(params);
    }

    if (type === TASK_TYPES.EXPORT) {
      return addExports(params);
    }
  };

  const addMedias = (files, folder, onSuccess) => {
    const reversedNewArray = [...medias, ...files].toReversed();
    setMedias(reversedNewArray);
    if (auto && files.length > 0) {
      // Removed check if the file is already uploaded
      // const newTasks = files.filter((file) => !uploadedFiles.has(file.name));
      // if (newTasks.length > 0) {
      uploadAll(files, folder, onSuccess);
      // }
    }
  };

  const addImports = (files) => {
    setImports((prev) => [...prev, files]);
  };

  const addExports = (files) => {
    setExports((prev) => [...prev, files]);
  };

  const incompleteUploads = Object.values(uploadProgress).filter((value) => value !== "100" && !Number.isNaN(Number(value)).length + imports.filter((i) => i.endedAt === null).length + exports.filter((e) => e.endedAt === null).length);

  useEffect(() => {
    if (!imports.length) {
      return;
    }

    const checkImportStatus = async () => {
      try {
        for (const uploadImport of imports) {
          if (uploadImport.endedAt) {
            continue;
          }

          const index = imports.indexOf(uploadImport);
          let uploadImportStatus = await api.get(`/api/upload-imports/${uploadImport.uuid}`);
          if (uploadImportStatus.endedAt) {
            let newImports = imports;
            delete newImports[index];
            newImports.push(uploadImportStatus);
            setImports(newImports.filter((i) => i !== undefined));
          } else {
            let timeout = setTimeout(() => {
              checkImportStatus();
              clearTimeout(timeout);
            }, 2000);
          }
        }
      } catch (error) {
        console.error("Error checking import status:", error);
      }
    };

    checkImportStatus();
  }, [imports]);

  useEffect(() => {
    if (!exports.length) {
      return;
    }

    const checkExportStatus = async () => {
      try {
        for (const exportUpload of exports) {
          if (exportUpload.endedAt) {
            continue;
          }

          const index = exports.indexOf(exportUpload);

          let exportUploadStatus = await api.get(`/api/upload-exports/${exportUpload.uuid}`);
          if (exportUploadStatus.endedAt) {
            let newExports = exports;
            delete newExports[index];
            newExports.push(exportUploadStatus);
            setExports(newExports.filter((i) => i !== undefined));
          } else {
            let timeout = setTimeout(() => {
              checkExportStatus();
              clearTimeout(timeout);
            }, 2000);
          }
        }
      } catch (error) {
        console.error("Error checking import status:", error);
      }
    };

    checkExportStatus();
  }, [exports]);

  // Handle browser popup to prevent missleaving the page while uploading
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (incompleteUploads > 0) {
        event.preventDefault();
        event.returnValue = "";
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [incompleteUploads]);

  return (
    <TasksContext.Provider value={{ addTasks, addImports, handleClose }}>
      {children}
      <Snackbar
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        open={!!medias?.length || !!imports?.length || !!exports?.length}
        onClose={handleClose}
      >
        {medias.length || imports.length || exports.length ? (
          <Card sx={{ py: 0, px: 0, width: "300px" }}>
            <Stack gap={0}>
              <Stack direction="row" justifyContent="space-between" alignItems="center" px={1} py={0.5}>
                {incompleteUploads === 0 ? (
                  <Box>
                    {medias.length + imports.filter((i) => i.endedAt !== null).length + exports.filter((e) => e.endedAt !== null).length} {medias.length > 1 ? t("common.files") : t("common.file")} {t("actions.imported")}
                  </Box>
                ) : (
                  <Box>
                    {incompleteUploads} {incompleteUploads > 1 ? t("common.files") : t("common.file")} {t("actions.importing")}
                  </Box>
                )}
                <Stack direction="row" gap={1}>
                  <IconButton size="small" onClick={() => setIsCollapsed(!isCollapsed)}>
                    {isCollapsed ? <KeyboardArrowUpIcon style={{ fontSize: 20 }} /> : <KeyboardArrowDownIcon style={{ fontSize: 20 }} />}
                  </IconButton>
                  <IconButton size="small" onClick={handleClose}>
                    <CloseIcon style={{ fontSize: 20 }} />
                  </IconButton>
                </Stack>
              </Stack>
              <Divider />
              {isCollapsed ? null : (
                <Stack
                  gap={1}
                  p={1}
                  height={isCollapsed ? "0px" : "100%"}
                  maxHeight="300px"
                  overflow="auto"
                  ref={(el) => {
                    if (el && !isCollapsed) {
                      el.scrollTop = el.scrollHeight;
                    }
                  }}
                >
                  {medias.map((file, index) => {
                    const isError = uploadProgress[file.name] !== undefined ? Number.isNaN(Number(uploadProgress[file.name])) : false;

                    return (
                      <Stack key={index} gap={0}>
                        <Stack direction="row" alignItems="center" gap={1} width="100%" justifyContent="space-between">
                          <Stack direction="row" alignItems="center" gap={1}>
                            {file.type.includes("image") ? <ImageIcon /> : <InsertDriveFileIcon />}
                            <Subtitle2
                              color={isError ? "error" : "inherit"}
                              sx={{
                                width: 180,
                                height: 20,
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                              }}
                            >
                              {file.name}
                            </Subtitle2>
                          </Stack>
                          <Stack justifyContent="center" alignItems="center">
                            {uploadProgress[file.name] === "100" ? <CheckIcon color="success" /> : isError ? <CloseIcon color="error" fontSize="small" /> : <LoaderProgress value={Number(uploadProgress[file.name]) || 0} />}
                          </Stack>
                        </Stack>
                        {isError ? (
                          <Subtitle2 color="error" px={0.5}>
                            {uploadProgress[file.name]}
                          </Subtitle2>
                        ) : null}
                      </Stack>
                    );
                  })}
                  {imports.map((file, index) => (
                    <Stack gap={0} key={index}>
                      <Stack direction="row" width="100%" alignItems="center" justifyContent="space-between" gap={1}>
                        {file.endedAt ? (
                          <Stack direction="row" alignItems="center" gap={1}>
                            <InsertDriveFileIcon />
                            <Link to={generatePath(RouteEnum.UPLOAD_IMPORTS_DETAIL, { uuid: file.uuid })}>{t("actions.task_detail")}</Link>
                            {file.result?.failure ? <CloseIcon color="error" /> : <CheckIcon color="success" />}
                          </Stack>
                        ) : (
                          <Stack direction="row" alignItems="center" gap={1}>
                            <InsertDriveFileIcon />
                            <Subtitle2>
                              {file.name} {`(${FileUtils.convertSize(file)})`}
                            </Subtitle2>
                            <CircularProgress size={20} />
                          </Stack>
                        )}
                      </Stack>
                    </Stack>
                  ))}
                  {exports.map((file, index) => (
                    <Stack gap={0} key={index}>
                      <Stack direction="row" width="100%" alignItems="center" justifyContent="space-between" gap={1}>
                        {file.endedAt ? (
                          <Stack direction="row" alignItems="center" gap={1}>
                            <InsertDriveFileIcon />
                            <Link to={generatePath(RouteEnum.UPLOAD_EXPORTS_DETAIL, { uuid: file.uuid })}>{t("actions.task_detail")}</Link>
                            <CheckIcon color="success" />
                          </Stack>
                        ) : (
                          <Stack direction="row" alignItems="center" gap={1}>
                            <InsertDriveFileIcon />
                            <Subtitle2>
                              {t(`items.${file.type}._`)} {" - "} {t(`enums.EXPORT_FORMATS.${file.format}`)}
                            </Subtitle2>
                            <CircularProgress size={20} />
                          </Stack>
                        )}
                      </Stack>
                    </Stack>
                  ))}
                </Stack>
              )}
            </Stack>
          </Card>
        ) : null}
      </Snackbar>
    </TasksContext.Provider>
  );
}
