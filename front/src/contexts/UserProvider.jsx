import { createContext, useContext, useEffect, useState } from "react";
import Rights from "@/utils/models/Rights";
import { useLocales } from "./LocalesProvider";
import LAYOUTS from "@/enums/LAYOUTS";
import { useSettings } from "./SettingsProvider";
import { useApi } from "./ApiProvider";
import { DashboardTypeEnum } from "@/enums";

export const UserContext = createContext({});

export const useUser = () => useContext(UserContext);

export default function UserProvider({ children }) {
  const { getDefaultLocaleCode } = useLocales();
  const { defaultLang, isLVMH } = useSettings();
  const api = useApi();

  const [isLoading, setIsLoading] = useState(false);
  const [user, setUser] = useState(null);

  const persistUser = async (u) => {
    setUser(u);
    await api.i18n.changeLanguage(u.lang);
  };

  const fetchUser = async () => {
    try {
      const u = await api.get("/api/users/0");
      await persistUser(u);
    } catch (error) {
      console.log(error);
      // Logic already implemented in ApiProvider to redirect to login page
      // document.location.href = RouteEnum.LOGOUT;
    }
  };

  const saveUser = async (properties) => {
    try {
      const u = await api.put(`/api/users/${user.id}`, { ...user, ...properties });
      await persistUser(u);
    } catch (error) {
      console.log(error);
    }
  };

  // calling user every 10 minutes (ensure token will be ok)
  useEffect(() => {
    const id = setInterval(load, 10 * 60 * 1000);

    return () => clearInterval(id);
  }, []);

  const load = async () => {
    if (!isLoading) {
      setIsLoading(true);
      try {
        await fetchUser();
      } catch (error) {
        console.log(error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  if (null === user) {
    void load();
    return;
  }

  const rights = new Rights(user);

  const preferences = {
    getLimit: () => user.preferences.limit ?? 12,
    setLimit: async (limit) =>
      await saveUser({
        ...user,
        preferences: { ...user.preferences, limit },
      }),
    getLayout: (type) => user.preferences.layouts[type] ?? LAYOUTS.CARD,
    setLayout: async (type, layout) =>
      await saveUser({
        ...user,
        preferences: { ...user.preferences, layouts: { ...user.preferences.layouts, [type]: layout } },
      }),
    getDensity: () => user.preferences.density,
    setDensity: async (density) =>
      await saveUser({
        ...user,
        preferences: { ...user.preferences, density },
      }),
    getLocale: () => {
      return user.preferences.locale || getDefaultLocaleCode();
    },
    setLocale: async (locale) =>
      await saveUser({
        ...user,
        preferences: { ...user.preferences, locale },
      }),
    getLang: () => {
      return user.preferences.lang || defaultLang;
    },
    setLang: async (lang) => {
      await api.i18n.changeLanguage(lang);
      await saveUser({
        ...user,
        preferences: { ...user.preferences, lang },
      });
    },
    getScope: () => {
      return user.preferences.scope || null;
    },
    setScope: async (scope) =>
      await saveUser({
        ...user,
        preferences: { ...user.preferences, scope },
      }),
    getHideEmptyAttributes: () => {
      return user.preferences.hideEmptyAttributes || false;
    },
    setHideEmptyAttributes: async (hideEmptyAttributes) =>
      await saveUser({
        ...user,
        preferences: { ...user.preferences, hideEmptyAttributes },
      }),
    getHideScopable: () => {
      return user.preferences.hideScopable || false;
    },
    setHideScopable: async (hideScopable) =>
      await saveUser({
        ...user,
        preferences: { ...user.preferences, hideScopable },
      }),
    getDashboard: () => {
      return user.preferences.dashboard || (isLVMH() ? DashboardTypeEnum.OPTIMISATION : null);
    },
    setDashboard: async (dashboard) =>
      await saveUser({
        ...user,
        preferences: { ...user.preferences, dashboard },
      }),
  };

  return (
    <UserContext.Provider
      value={{
        user,
        preferences,
        fetchUser,
        rights,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}
