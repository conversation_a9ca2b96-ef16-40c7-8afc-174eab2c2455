import LOCAL_STORAGE_KEYS from "@/enums/LOCAL_STORAGE_KEYS";
import StorageUtils from "@/utils/storage.utils";
import {createContext, type ReactNode, useContext, useState} from "react";
import FormatUtils from "@/utils/format.utils";
import ImgNotFound from "@/assets/images/image-fake.png";
import type {Parameters} from "@/types/model/Parameters";
import type {i18n as I18nType} from "i18next";
import {RouteEnum} from "@/enums";

const ApiContext = createContext({});

type Api = {
  getToken: () => string | null;
  setToken: (token: string) => void;
  isLogged: () => boolean;
  get: (uri: string, params?: Parameters) => Promise<unknown>;
  post: (uri: string, data: Parameters) => Promise<unknown>;
  postFormData: (uri: string, body: unknown) => Promise<unknown>;
  postXMLFormData: (uri: string, formData: unknown, fileName: string, progressCallback: (() => void) | null) => Promise<unknown>;
  put: (uri: string, data: Parameters) => Promise<unknown>;
  save: (uri: string, data: Parameters) => Promise<unknown>;
  delete: (uri: string) => Promise<unknown>;
  flatten: (arr: string[], params: Parameters) => void;
  apiRoot: string;
  i18n: unknown;
  image: {
    get: (url: string) => string;
    thumbnail: (url: string) => string;
    binary: (url: string) => string;
    download: (url: string) => string;
    resize: (url: string, size: string, isSizeIgnored: boolean, format: string, end: string) => string;
  };
};

type ApiProviderProps = {
  apiRoot: string;
  i18n: I18nType;
  children: ReactNode;
};

type FetchConfig = {
  headers?: HeadersInit;
  method?: string;
  body?: string | FormData;
  noContentType?: boolean;
};

export const useApi = (): Api => useContext(ApiContext) as Api;

export default function ApiProvider({apiRoot, i18n, children}: ApiProviderProps) {
  const [token, setToken] = useState(StorageUtils.get(LOCAL_STORAGE_KEYS.TOKEN));

  const getToken = () => {
    if (!token) {
      const newToken = StorageUtils.get(LOCAL_STORAGE_KEYS.TOKEN);
      if (newToken) {
        setToken(newToken);
        return newToken;
      }
    }

    return token;
  };

  const updateToken = (token: string | null) => {
    if (token) {
      setToken(token);
      StorageUtils.set(LOCAL_STORAGE_KEYS.TOKEN, token);
    } else {
      setToken(null);
      StorageUtils.clear();
    }
  };

  const isLogged = () => {
    return !!getToken();
  };

  const fetching = async (uri: string, config: FetchConfig = {}) => {
    try {
      const headers = new Headers(config?.headers || {});
      headers.append("Accept-Language", "en_GB");
      headers.append("Accept", (config.headers as Record<string, string>)?.Accept || "application/ld+json");

      if (!headers.has("Content-Type") && !config.noContentType) {
        headers.append("Content-Type", "application/json");
      }

      const token = getToken();
      if (token) {
        headers.append("X-Sinfin-Token", token);
      }
      config.headers = headers;

      const isJson = (rct: string | null) => ["application/json", "application/ld+json", "application/problem+json"].some((bct) => rct?.startsWith(bct));

      const response = await fetch(`${apiRoot}${uri}`, config as RequestInit);
      const contentType = response.headers.get("Content-Type");

      // renew token
      const renew = response.headers.get("X-Sinfin-Token-Renew");
      if (renew) {
        updateToken(renew);
      }

      // 204 no Content on delete
      if (204 === response.status) {
        return;
      }

      if (401 === response.status) {
        if (token) {
          updateToken(null);
          document.location.href = RouteEnum.LOGOUT;
          return;
        }
        if (isJson(contentType)) {
          throw await response.json();
        }

        throw response;
      }

      if (!response.ok) {
        if (isJson(contentType)) {
          throw await response.json();
        }
        try {
          // try to enable debug response
          console.debug(await response.text());
        } catch (e) {
          console.error(e);
        }
        throw response;
      }

      if (!isJson(contentType)) {
        updateToken(null);
        document.location.href = RouteEnum.LOGOUT;
        return;
      }

      return await response.json();
    } catch (error) {
      console.log(error);
      throw error;
    }
  };

  const get = async (uri: string, params = {}) => {
    const query: string[] = [];
    flatten(query, params);

    return await fetching(`${uri}${query.length > 0 ? `?${query.join("&")}` : ""}`);
  };

  const post = async (uri: string, data = {}) => {
    return await fetching(uri, {
      method: "POST",
      body: JSON.stringify(data),
    });
  };

  const postFormData = async (uri: string, body: FormData) => {
    return await fetching(uri, {
      method: "POST",
      body,
      noContentType: true,
    });
  };

  // Helper methods
  const parseHeaders = (headerStr: string) => {
    const headers = new Headers();
    const headerPairs = headerStr.trim().split(/[\r\n]+/);
    headerPairs.forEach((headerPair) => {
      const [key, value] = headerPair.split(": ");
      headers.append(key, value);
    });
    return headers;
  };

  const isJson = (contentType: string | null) => {
    return ["application/json", "application/ld+json", "application/problem+json"].some((type) => contentType?.startsWith(type));
  };

  const postXMLFormData = (uri: string, formData: FormData, fileName: string, progressCallback: ((fileName: string, progress: string) => void) | null = null) => {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener("progress", (event: ProgressEvent<XMLHttpRequestEventTarget>) => {
        if (event.lengthComputable && progressCallback) {
          const percentComplete = (event.loaded / event.total) * 100;
          progressCallback(fileName, percentComplete.toFixed(0));
        }
      });

      xhr.addEventListener("load", () => {
        const response = {
          status: xhr.status,
          statusText: xhr.statusText,
          headers: parseHeaders(xhr.getAllResponseHeaders()),
          data: null,
        };

        const contentType = response.headers.get("Content-Type");

        if (isJson(contentType)) {
          try {
            response.data = JSON.parse(xhr.responseText);
          } catch (e) {
            console.error(e);
            reject(new Error("Failed to parse JSON response"));
            return;
          }
        } else {
          (response.data as unknown as string) = xhr.responseText;
        }

        if (xhr.status >= 200 && xhr.status < 300) {
          resolve(response);
        } else {
          reject(response);
        }
      });

      xhr.addEventListener("error", () => {
        reject(new Error("Network error occurred"));
      });

      xhr.open("POST", `${apiRoot}${uri}`);

      xhr.setRequestHeader("Accept-Language", i18n.language);
      xhr.setRequestHeader("Accept", "application/ld+json");

      const token = getToken();
      if (token) {
        xhr.setRequestHeader("X-Sinfin-Token", token);
      }

      xhr.send(formData);
    });
  };

  const put = async (uri: string, data = {}) => {
    return await fetching(uri, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  };

  const save = async (uri: string, data: Parameters = {}) => {
    let method = "POST";
    if (data?.["@id"]) {
      uri = data["@id"] as string;
      method = "PUT";
    }

    return await fetching(uri, {
      method,
      body: JSON.stringify(data),
      headers: {
        Accept: "application/ld+json",
        "Content-Type": data?.['@id'] ? "application/ld+json" : "application/json",
      },
    });
  };

  const del = async (uri: string) => {
    return await fetching(uri, {
      method: "DELETE",
    });
  };

  const flatten = (arr: string[], params: Parameters): void => {
    Object.keys(params).forEach((key: string) => {
      const value: unknown = params[key];
      if (key === "filters" || key === "orders") {
        arr.push(`${key}=${encodeURIComponent(JSON.stringify(value))}`);
      } else if (Array.isArray(value)) {
        value.forEach((v: unknown, i: number) => {
          arr.push(`${encodeURIComponent(key)}[${i}]=${encodeURIComponent(String(v))}`);
        });
      } else if (value !== null && typeof value === "object") {
        Object.entries(value as Record<string, unknown>).forEach(([k1, innerValue]) => {
          if (innerValue !== null && typeof innerValue === "object") {
            Object.entries(innerValue as Record<string, unknown>).forEach(([k2, v2]) => {
              arr.push(`${encodeURIComponent(key)}[${k1}][${k2}]=${encodeURIComponent(String(v2))}`);
            });
          } else {
            arr.push(`${encodeURIComponent(key)}[${k1}]=${encodeURIComponent(String(innerValue))}`);
          }
        });
      } else {
        arr.push(`${decodeURIComponent(key)}=${encodeURIComponent(String(value))}`);
      }
    });
  };

  const getImage = (url: string | null, format: string | null) => {
    if (null === url) {
      return null;
    }

    const apiUrl = FormatUtils.isIri(url) ? `${apiRoot}${url}` : url;
    const cleanUrl = apiUrl.replace("/binary", "");

    if (format) {
      return `${cleanUrl}/thumbnail/${format}`;
    }

    return `${cleanUrl}/binary`;
  };

  const resize = (url: string, size = "250x250", isSizeIgnored = false, format = "png", end = "") => {
    if (FormatUtils.isIri(url)) {
      return `${apiRoot}${url}/resize/${size}-${isSizeIgnored ? "ignore" : "preserve"}.${format}${end}`;
    }

    return ImgNotFound;
  };

  const image = {
    get: (url: string) => getImage(url, "600x600"),
    thumbnail: (url: string) => getImage(url, "400x400"),
    binary: (url: string) => getImage(url, null),
    download: (url: string) => `${getImage(url, null)}?download=1`,
    resize,
  };

  return (
    <ApiContext.Provider
      value={{
        getToken,
        setToken: updateToken,
        isLogged,
        get,
        post,
        postFormData,
        postXMLFormData,
        put,
        save,
        delete: del,
        flatten,
        apiRoot,
        i18n,
        image,
      }}
    >
      {children}
    </ApiContext.Provider>
  );
}
