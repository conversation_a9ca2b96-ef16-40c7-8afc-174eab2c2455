import {createContext, type ReactNode, useContext} from "react";
import {usePaginator} from "@/hooks/usePaginator";
import {ApiTypeEnum} from "@/enums";
import type {Scope} from "@/resources";

type ScopesProviderType = {
  scopes: Scope[];
  fetchScopes: () => Promise<void>;
  getScope: (code: string) => Scope | undefined;
}

const ScopesContext = createContext({});

export const useScopes = () => useContext<Partial<ScopesProviderType>>(ScopesContext);

export default function ScopesProvider({children}: { children: ReactNode }) {
  const paginator = usePaginator(ApiTypeEnum.SCOPE, "/api/scopes", {}, {}, false);
  const items: Scope[] = paginator.items;

  const fetchScopes = async () => {
    await paginator.goto(1);
  };

  const getScope = (code: string) => {
    return items.find((scope) => scope.code === code);
  };

  const context = {
    scopes: items,
    fetchScopes,
    getScope,
  }

  return (
    <ScopesContext.Provider value={context}>
      {children}
    </ScopesContext.Provider>
  );
}
