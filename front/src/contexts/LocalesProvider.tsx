import {createContext, type ReactNode, useContext} from "react";
import {usePaginator} from "@/hooks/usePaginator";
import type {Locale} from "@/resources";
import {ApiTypeEnum} from "@/enums";

export type LocalesProviderType = {
  locales: Locale[];
  localeDefaultCode: string | null;
  getDefaultLocaleCode: () => string | null;
  fetchLocales: () => Promise<void>;
  getLocale: (code: string) => Locale | undefined;
}

const LocalesContext = createContext<Partial<LocalesProviderType>>({});

export const useLocales = () => useContext(LocalesContext);

export default function LocalesProvider({children}: { children: ReactNode }) {
  const paginator = usePaginator(ApiTypeEnum.LOCALE, "/api/locales", {}, {}, false);
  const items: Locale[] = paginator.items;

  const fetchLocales = async () => {
    await paginator.goto(1);
  };

  let localeDefaultCode = null;

  if (items[0]) {
    localeDefaultCode = items[0].code;
  }

  const getDefaultLocaleCode = () => localeDefaultCode;

  const getLocale = (code: string) => {
    return items.find((locale) => locale.code === code);
  };

  const context = {
    locales: paginator.items,
    localeDefaultCode,
    getDefaultLocaleCode,
    fetchLocales,
    getLocale,
  };

  return (
    <LocalesContext.Provider value={context}>
      {children}
    </LocalesContext.Provider>
  );
}
