import LoaderProgress from "@/components/loaders/LoaderProgress";
import { Subtitle2 } from "@/components/ui/Typography";
import { Check, Close, Image, InsertDriveFile } from "@mui/icons-material";
import { Stack } from "@mui/material";

type MediaRowProps = {
  file: File;
  index: number;
  uploadProgress: Record<string, string>;
  isError: boolean;
};

export default function MediaRow({ file, index, uploadProgress, isError }: MediaRowProps) {
  return (
    <Stack key={index} gap={0}>
      <Stack direction="row" alignItems="center" gap={1} width="100%" justifyContent="space-between">
        <Stack direction="row" alignItems="center" gap={1}>
          {file.type.includes("image") ? <Image /> : <InsertDriveFile />}
          <Subtitle2
            color={isError ? "error" : "inherit"}
            sx={{
              width: 180,
              height: 20,
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}
          >
            {file.name}
          </Subtitle2>
        </Stack>
        <Stack justifyContent="center" alignItems="center">
          {uploadProgress[file.name] === "100" ? <Check color="success" /> : isError ? <Close color="error" fontSize="small" /> : <LoaderProgress color="primary" value={Number(uploadProgress[file.name]) || 0} />}
        </Stack>
      </Stack>
      {isError ? (
        <Subtitle2 color="error" px={0.5}>
          {uploadProgress[file.name]}
        </Subtitle2>
      ) : null}
    </Stack>
  );
}
