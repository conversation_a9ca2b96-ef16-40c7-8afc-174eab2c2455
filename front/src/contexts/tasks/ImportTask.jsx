import { useState, useEffect } from "react";
import { useApi } from "../ApiProvider";

export function useImportTask() {
  const [imports, setImports] = useState([]);
  const api = useApi();

  const addImports = (files) => {
    setImports((prev) => [...prev, files]);
  };

  useEffect(() => {
    if (!imports.length) return;

    const checkImportStatus = async () => {
      try {
        for (const uploadImport of imports) {
          if (uploadImport.endedAt) {
            continue;
          }

          const index = imports.indexOf(uploadImport);
          let uploadImportStatus = await api.get(`/api/upload-imports/${uploadImport.uuid}`);
          if (uploadImportStatus.endedAt) {
            let newImports = imports;
            delete newImports[index];
            newImports.push(uploadImportStatus);
            setImports(newImports.filter((i) => i !== undefined));
          } else {
            let timeout = setTimeout(() => {
              checkImportStatus();
              clearTimeout(timeout);
            }, 2000);
          }
        }
      } catch (error) {
        console.error("Error checking import status:", error);
      }
    };

    checkImportStatus();
  }, [imports]);

  return {
    imports,
    addImports,
    setImports,
  };
}
