import { useState } from "react";
import { useApi } from "../ApiProvider";
import { generatePath, Link } from "react-router";
import { useTranslation } from "@/hooks/useTranslation";
import { RouteEnum } from "@/enums";
import type { MediaFolder } from "@/resources";

export function useMediaTask() {
  const api = useApi();
  const { t } = useTranslation();

  const [medias, setMedias] = useState([]);
  const [uploadProgress, setUploadProgress] = useState({});

  const getError = (e) => {
    if (e.status === 409 || e.status === 422) {
      const errorMessage = e.data["hydra:description"];
      const headers = new Headers(e.headers);
      const location = headers.get("X-Location");

      return (
        <span>
          {errorMessage}
          {t("common.points")}{" "}
          <Link style={{ color: "inherit" }} to={generatePath(RouteEnum.DAM_DETAIL, { uuid: location.replace("/api/medias/", "") })} target="_blank" rel="noreferrer">
            {t("actions.see")}
          </Link>
        </span>
      );
    }
  };

  const upload = async (file: File, folder: MediaFolder) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onloadend = () => {
        const blob = new Blob([file], { name: file.name });

        const formData = new FormData();
        formData.append("file", blob, file.name);
        formData.append("path", file.path);
        if (folder) {
          formData.append("folder", folder.id);
        }

        api
          .postXMLFormData("/api/medias", formData, file.name, (fileName: string, progress: string) => {
            setUploadProgress((prev) => ({
              ...prev,
              [fileName]: progress,
            }));
          })
          .then((result) => {
            resolve(result);
            // setUploadedFiles((prev) => new Set(prev).add(file.name));
          })
          .catch((e) => {
            setUploadProgress((prev) => ({
              ...prev,
              [file.name]: getError(e),
            }));
            reject(e);
          });
      };
    });
  };

  const uploadAll = async (medias, folder, onSuccess) => {
    const results = [];
    for (const file of medias) {
      try {
        const result = await upload(file, folder);
        results.push(result);
      } catch (error) {
        console.error("Error uploading medias:", error);
      }
    }

    if (onSuccess) {
      onSuccess(results);
    }
  };

  const addMedias = (files, folder, onSuccess) => {
    const reversedNewArray = [...medias, ...files].toReversed();
    setMedias(reversedNewArray);
    if (files.length > 0) {
      uploadAll(files, folder, onSuccess);
    }
  };

  return {
    medias,
    setMedias,
    uploadProgress,
    setUploadProgress,
    addMedias,
  };
}
