import IconButton from "@/components/ui/IconButton";
import { Subtitle2 } from "@/components/ui/Typography";
import { useApi } from "@/contexts/ApiProvider";
import { RouteEnum } from "@/enums";
import { useTranslation } from "@/hooks/useTranslation";
import { Check, Download, InsertDriveFile } from "@mui/icons-material";
import { CircularProgress, Stack } from "@mui/material";
import { generatePath, Link } from "react-router";

export default function ExportRow({ file }: { file: any }) {
  const { t } = useTranslation();
  const api = useApi();

  return (
    <Stack direction="row" width="100%" alignItems="center" justifyContent="space-between" gap={1}>
      {file.endedAt ? (
        <>
          <Stack direction="row" alignItems="center" gap={1}>
            <InsertDriveFile />
            <Link to={generatePath(RouteEnum.UPLOAD_EXPORTS_DETAIL, { uuid: file.uuid })}>{t("actions.task_detail")}</Link>
          </Stack>

          <Stack direction="row" alignItems="center" gap={1}>
            <IconButton size="small" LinkComponent="a" href={`${api.apiRoot}${file["@id"]}/binary?token=${api.getToken()}`} title={t("actions.download")} target="_blank">
              <Download fontSize="small" />
            </IconButton>
            <Check color="success" />
          </Stack>
        </>
      ) : (
        <>
          <Stack direction="row" alignItems="center" gap={1}>
            <InsertDriveFile />
            <Subtitle2>
              {t(`items.${file.type}._`)} {" - "} {t(`enums.EXPORT_FORMATS.${file.format}`)}
            </Subtitle2>
          </Stack>
          <CircularProgress size={20} />
        </>
      )}
    </Stack>
  );
}
