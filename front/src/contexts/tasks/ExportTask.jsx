import { useState, useEffect } from "react";
import { useApi } from "../ApiProvider";

export function useExportTask() {
  const [exports, setExports] = useState([]);
  const api = useApi();

  const addExports = (files) => {
    setExports((prev) => [...prev, files]);
  };

  useEffect(() => {
    if (!exports.length) return;

    const checkExportStatus = async () => {
      try {
        for (const exportUpload of exports) {
          if (exportUpload.endedAt) {
            continue;
          }

          const index = exports.indexOf(exportUpload);

          let exportUploadStatus = await api.get(`/api/upload-exports/${exportUpload.uuid}`);
          if (exportUploadStatus.endedAt) {
            let newExports = exports;
            delete newExports[index];
            newExports.push(exportUploadStatus);
            setExports(newExports.filter((i) => i !== undefined));
          } else {
            let timeout = setTimeout(() => {
              checkExportStatus();
              clearTimeout(timeout);
            }, 2000);
          }
        }
      } catch (error) {
        console.error("Error checking import status:", error);
      }
    };

    checkExportStatus();
  }, [exports]);

  return {
    exports,
    addExports,
    setExports,
  };
}
