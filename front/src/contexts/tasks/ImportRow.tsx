import { Subtitle2 } from "@/components/ui/Typography";
import { RouteEnum } from "@/enums";
import { useTranslation } from "@/hooks/useTranslation";
import FileUtils from "@/utils/file.utils";
import { Check, Close, InsertDriveFile } from "@mui/icons-material";
import { CircularProgress, Stack } from "@mui/material";
import { generatePath, Link } from "react-router";

export default function ImportRow({ file }: { file: any }) {
  const { t } = useTranslation();

  return (
    <Stack direction="row" width="100%" alignItems="center" justifyContent="space-between" gap={1}>
      {file.endedAt ? (
        <>
          <Stack direction="row" alignItems="center" gap={1}>
            <InsertDriveFile />
            <Link to={generatePath(RouteEnum.UPLOAD_IMPORTS_DETAIL, { uuid: file.uuid })}>{t("actions.task_detail")}</Link>
          </Stack>

          {file.result?.failure ? <Close color="error" /> : <Check color="success" />}
        </>
      ) : (
        <>
          <Stack direction="row" alignItems="center" gap={1}>
            <InsertDriveFile />
            <Subtitle2>
              {file.name} {`(${FileUtils.convertSize(file)})`}
            </Subtitle2>
          </Stack>
          <CircularProgress size={20} />
        </>
      )}
    </Stack>
  );
}
