import { createContext, useContext, useEffect, useState } from "react";
import { useApi } from "./ApiProvider";
import { useSearchParams } from "react-router";
import HUB_ORIGIN_FILTERS from "@/enums/HUB_ORIGIN_FILTERS";
import HUB_PRODUCT_FILTERS from "@/enums/HUB_PRODUCT_FILTERS";
import useDidMountEffect from "@/hooks/useDidMountEffect";
import dayjs from "@/libs/dayjs";

const HubContext = createContext({});

export const useHub = () => useContext(HubContext);

export default function HubProvider({ children }) {
  const api = useApi();

  const [searchParams, setSearchParams] = useSearchParams();

  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState(null);
  const [orders, setOrders] = useState(null);
  const [topProducts, setTopProducts] = useState(null);
  const [sellers, setSellers] = useState(null);
  const [dateFrom, setDateFrom] = useState(searchParams.get("from") ?? dayjs.from().subtract(7, "day").toISOString());
  const [dateTo, setDateTo] = useState(searchParams.get("to") ?? dayjs.to().toISOString());

  const defaultParams = {
    from: dateFrom,
    to: dateTo,
  };

  const fetchData = async (url, params, setter) => {
    setIsLoading(true);
    try {
      const response = await api.get(url, params);
      setter(response["hydra:member"]);
    } catch (error) {
      console.error(`Error fetching data from ${url}:`, error);
    } finally {
      setIsLoading(false);
    }
  };

  const getData = async () => {
    setIsLoading(true);

    try {
      const apiCalls = [
        { url: "/api/hub-dash-stats", params: defaultParams, setter: setStats },
        { url: "/api/hub-dash-orders", params: { ...defaultParams, groupBy: searchParams.get("groupBy") ?? "day" }, setter: setOrders },
        { url: "/api/hub-dash-top-products", params: { ...defaultParams, by: searchParams.get("filter") ?? HUB_PRODUCT_FILTERS.PRICE }, setter: setTopProducts },
        { url: "/api/hub-dash-sellers", params: { ...defaultParams, by: searchParams.get("origin") ?? HUB_ORIGIN_FILTERS.OWNER }, setter: setSellers },
      ];

      await Promise.all(apiCalls.map(({ url, params, setter }) => fetchData(url, params, setter)));
    } catch (error) {
      console.error("Error in getData:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleParams = (key, value) => {
    setSearchParams((prevParams) => {
      const newParams = new URLSearchParams(prevParams);
      if (!value || value?.length === 0) {
        newParams.delete(key);
      } else {
        newParams.set(key, value);
      }
      return newParams;
    });
  };

  useEffect(() => {
    getData();
  }, [dateFrom, dateTo]);

  useDidMountEffect(() => {
    fetchData("/api/hub-dash-orders", { ...defaultParams, groupBy: searchParams.get("groupBy") ?? "day" }, setOrders);
  }, [searchParams.get("groupBy")]);

  useDidMountEffect(() => {
    fetchData("/api/hub-dash-top-products", { ...defaultParams, by: searchParams.get("filter") ?? HUB_PRODUCT_FILTERS.PRICE }, setTopProducts);
  }, [searchParams.get("filter")]);

  useDidMountEffect(() => {
    fetchData("/api/hub-dash-sellers", { ...defaultParams, by: searchParams.get("origin") ?? HUB_ORIGIN_FILTERS.OWNER }, setSellers);
  }, [searchParams.get("origin")]);

  return <HubContext.Provider value={{ stats, orders, topProducts, sellers, isLoading, handleParams, dateFrom, setDateFrom, dateTo, setDateTo }}>{children}</HubContext.Provider>;
}
