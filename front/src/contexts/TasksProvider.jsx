import { createContext, useContext, useEffect, useState } from "react";
import { Box, Card, CircularProgress, Divider, IconButton, Snackbar, Stack } from "@mui/material";
import { Subtitle2 } from "@/components/ui/Typography";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import { useTranslation } from "@/hooks/useTranslation";
import { generatePath, Link } from "react-router";
import FileUtils from "@/utils/file.utils";
import TASK_TYPES from "@/enums/TASK_TYPES";
import { RouteEnum } from "@/enums";
import { useMediaTask } from "@/contexts/tasks/MediaTask";
import { useImportTask } from "@/contexts/tasks/ImportTask";
import { useExportTask } from "@/contexts/tasks/ExportTask";
import MediaRow from "@/contexts/tasks/MediaRow";
import ExportRow from "@/contexts/tasks/ExportRow";

const TasksContext = createContext({});

/**
 * @typedef {File[]} Array of files
 * @typedef {string} iri
 * @property {function(File[], string): void} addTasks - Function to add tasks
 * @returns {{ addTasks: (tasks, iri) => void, handleClose: () => void }}
 */

export const useTasks = () => useContext(TasksContext);

export default function TasksProvider({ children }) {
  const { t } = useTranslation();

  const { medias, setMedias, uploadProgress, setUploadProgress, addMedias } = useMediaTask();
  const { imports, setImports, addImports } = useImportTask();
  const { exports, setExports, addExports } = useExportTask();

  const [isCollapsed, setIsCollapsed] = useState(false);

  const removeAll = () => {
    setMedias([]);
    setUploadProgress({});
    setImports([]);
    setExports([]);
  };

  const handleClose = (event, reason) => {
    if ("clickaway" !== reason) {
      removeAll();
    }
  };

  const addTasks = (type, params) => {
    if (!type) {
      return;
    }

    if (type === TASK_TYPES.MEDIA) {
      return addMedias(params.files, params.folder, params.onSuccess);
    }

    if (type === TASK_TYPES.IMPORT) {
      return addImports(params);
    }

    if (type === TASK_TYPES.EXPORT) {
      return addExports(params);
    }
  };

  const incompleteUploads = Object.values(uploadProgress).filter((value) => value !== "100" && !Number.isNaN(Number(value)).length + imports.filter((i) => i.endedAt === null).length + exports.filter((e) => e.endedAt === null).length);

  // Handle browser popup to prevent missleaving the page while uploading
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (incompleteUploads > 0) {
        event.preventDefault();
        event.returnValue = "";
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [incompleteUploads]);

  return (
    <TasksContext.Provider value={{ addTasks, addImports, handleClose }}>
      {children}
      <Snackbar
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        open={!!medias?.length || !!imports?.length || !!exports?.length}
        onClose={handleClose}
      >
        {medias.length || imports.length || exports.length ? (
          <Card sx={{ py: 0, px: 0, width: "300px" }}>
            <Stack gap={0}>
              <Stack direction="row" justifyContent="space-between" alignItems="center" px={1} py={0.5}>
                {incompleteUploads === 0 ? (
                  <Box>
                    {medias.length + imports.filter((i) => i.endedAt !== null).length + exports.filter((e) => e.endedAt !== null).length} {medias.length > 1 ? t("common.files") : t("common.file")} {t("actions.imported")}
                  </Box>
                ) : (
                  <Box>
                    {incompleteUploads} {incompleteUploads > 1 ? t("common.files") : t("common.file")} {t("actions.importing")}
                  </Box>
                )}
                <Stack direction="row" gap={1}>
                  <IconButton size="small" onClick={() => setIsCollapsed(!isCollapsed)}>
                    {isCollapsed ? <KeyboardArrowUpIcon style={{ fontSize: 20 }} /> : <KeyboardArrowDownIcon style={{ fontSize: 20 }} />}
                  </IconButton>
                  <IconButton size="small" onClick={handleClose}>
                    <CloseIcon style={{ fontSize: 20 }} />
                  </IconButton>
                </Stack>
              </Stack>
              <Divider />
              {isCollapsed ? null : (
                <Stack
                  gap={1}
                  p={1}
                  height={isCollapsed ? "0px" : "100%"}
                  maxHeight="300px"
                  overflow="auto"
                  ref={(el) => {
                    if (el && !isCollapsed) {
                      el.scrollTop = el.scrollHeight;
                    }
                  }}
                >
                  {medias.map((file, index) => {
                    const isError = uploadProgress[file.name] !== undefined ? Number.isNaN(Number(uploadProgress[file.name])) : false;

                    return <MediaRow key={index} file={file} index={index} uploadProgress={uploadProgress} isError={isError} />;
                  })}

                  {imports.map((file, index) => (
                    <Stack gap={0} key={index}>
                      <Stack direction="row" width="100%" alignItems="center" justifyContent="space-between" gap={1}>
                        {file.endedAt ? (
                          <Stack direction="row" alignItems="center" gap={1}>
                            <InsertDriveFileIcon />
                            <Link to={generatePath(RouteEnum.UPLOAD_IMPORTS_DETAIL, { uuid: file.uuid })}>{t("actions.task_detail")}</Link>
                            {file.result?.failure ? <CloseIcon color="error" /> : <CheckIcon color="success" />}
                          </Stack>
                        ) : (
                          <Stack direction="row" alignItems="center" gap={1}>
                            <InsertDriveFileIcon />
                            <Subtitle2>
                              {file.name} {`(${FileUtils.convertSize(file)})`}
                            </Subtitle2>
                            <CircularProgress size={20} />
                          </Stack>
                        )}
                      </Stack>
                    </Stack>
                  ))}

                  {exports.map((file, index) => (
                    <ExportRow key={index} file={file} index={index} />
                  ))}
                </Stack>
              )}
            </Stack>
          </Card>
        ) : null}
      </Snackbar>
    </TasksContext.Provider>
  );
}
