import "dayjs/locale/fr";
import "dayjs/locale/en-gb";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import dayjs from "dayjs";
import localeData from "dayjs/plugin/localeData";
import isoWeek from "dayjs/plugin/isoWeek";
import customParseFormat from "dayjs/plugin/customParseFormat";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isoWeek);
dayjs.extend(localeData);
dayjs.extend(customParseFormat);

dayjs.extend((option, dayjsClass, dayjsFactory) => {
  dayjsClass.prototype.asFrom = function () {
    return this
      .set("hour", 0)
      .set("minute", 0)
      .set("second", 0)
      .set("millisecond", 0);
  };

  dayjsClass.prototype.asTo = function () {
    return this
      .set("hour", 23)
      .set("minute", 59)
      .set("second", 59)
      .set("millisecond", 999);
  };

  dayjsFactory.from = () => dayjs().asFrom();
  dayjsFactory.to = () => dayjs().asTo();
});

dayjs.tz.setDefault("Europe/Paris");

export default dayjs;
