import "dayjs/locale/fr";
import "dayjs/locale/en-gb";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import dayjs from "dayjs";
import localeData from "dayjs/plugin/localeData";
import isoWeek from "dayjs/plugin/isoWeek";
import customParseFormat from "dayjs/plugin/customParseFormat";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isoWeek);
dayjs.extend(localeData);
dayjs.extend(customParseFormat);

dayjs.extend((option, dayjsClass, dayjsFactory) => {
  dayjsClass.prototype.hmsms = function (h, m, s, ms) {
    return this
      .tz("Europe/Paris")
      .set("hour", h)
      .set("minute", m)
      .set("second", s)
      .set("millisecond", ms);
  };

  dayjsClass.prototype.asFrom = function () {
    return this.tz("Europe/Paris").hmsms(0, 0, 0, 0);
  };

  dayjsClass.prototype.asTo = function () {
    return this.tz("Europe/Paris").hmsms(23, 59, 59, 999);
  };

  dayjsFactory.from = () => dayjs().asFrom();
  dayjsFactory.to = () => dayjs().asTo();
});

dayjs.tz.setDefault("Europe/Paris");

export default dayjs;
