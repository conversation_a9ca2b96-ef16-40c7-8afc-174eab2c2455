import { expect, userEvent, waitFor, within } from "@storybook/test";
import { sleep } from "../test.utils";
import { LicenseInfo } from "@mui/x-license";
import { usePaginator } from "@/hooks/usePaginator";
import DataTable from "@/components/DataTable";

export const Datatable = {
  decorators: [
    () => {
      LicenseInfo.setLicenseKey(import.meta.env.VITE_MUI_X_LICENSE_KEY);

      const paginator = usePaginator("Product", "/api/products", {}, { limit: 10, page: 1 }, { limit: 10, page: 1 });

      return (
        <div style={{ height: 500, width: "100%" }}>
          <DataTable paginator={paginator} />
        </div>
      );
    },
  ],

  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Wait for the table to load
    await expect(canvas.findByRole("grid")).resolves.toBeInTheDocument();

    // Check if all columns are present
    await expect(canvas.getByText("SKU")).toBeInTheDocument();
    // await expect(canvas.getByText('Name')).toBeInTheDocument();
    // await expect(canvas.getByText('Price')).toBeInTheDocument();

    await sleep(500);

    // Check if data is loaded correctly
    await expect(canvas.getByText("ADIDASBE_BLACK_U")).toBeInTheDocument();
    // await expect(canvas.getByText('Adidas Black Shoes')).toBeInTheDocument();
    // await expect(canvas.getByText('$99.99')).toBeInTheDocument();

    // Test sorting functionality
    const idColumnHeader = canvas.getByText("SKU");
    await userEvent.click(idColumnHeader);
    // await expect(canvas.getByText('ADIDASBE_BLACK_U')).toBeInTheDocument();
    // await userEvent.click(idColumnHeader);
    // await expect(canvas.getByText('NIKERE_WHITE_S')).toBeInTheDocument();

    // Test column visibility toggle
    const columnButton = await canvas.findByTestId("select-columns");
    await expect(columnButton).toBeInTheDocument();
    await userEvent.click(columnButton);

    const body = within(document.body);

    const columnSkuButton = await body.findByTestId("SKU");
    await expect(columnSkuButton).toBeInTheDocument();

    await userEvent.click(columnSkuButton);
    await userEvent.keyboard("{Enter}");
    await userEvent.keyboard("{Escape}");

    await waitFor(() => {
      expect(idColumnHeader).not.toBeInTheDocument();
    });

    await userEvent.click(columnButton);
    await expect(await body.findByTestId("SKU")).toBeInTheDocument();

    await userEvent.click(await body.findByTestId("SKU"));
    await userEvent.keyboard("{Enter}");
    await userEvent.keyboard("{Escape}");

    await expect(canvas.getByText("SKU")).toBeInTheDocument();

    // Test pagination
    // const nextPageButton = canvas.getByLabelText('Go to next page');
    // await userEvent.click(nextPageButton);
    // await expect(canvas.getByText('Page 2 of 5')).toBeInTheDocument();

    // // Test search functionality
    // const searchInput = canvas.getByPlaceholderText('Search...');
    // await userEvent.type(searchInput, 'Adidas');
    // await sleep(500);
    // await expect(canvas.getByText('ADIDASBE_BLACK_U')).toBeInTheDocument();
    // await expect(canvas.queryByText('NIKERE_WHITE_S')).not.toBeInTheDocument();

    // // Test row selection
    // const firstRowCheckbox = canvas.getAllByRole('checkbox')[1];
    // await userEvent.click(firstRowCheckbox);
    // await expect(firstRowCheckbox).toBeChecked();

    // // Test bulk actions
    // const bulkActionButton = canvas.getByText('Bulk Actions');
    // await userEvent.click(bulkActionButton);
    // await expect(canvas.getByText('Delete Selected')).toBeInTheDocument();
  },
};
