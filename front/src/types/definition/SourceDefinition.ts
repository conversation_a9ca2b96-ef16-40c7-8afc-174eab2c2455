import type {UiDefinition} from "./UiDefinition";
import type {ChannelSourceEnum} from "@/enums/ChannelSourceEnum";
import {OperatorEnum} from "@/enums";

export type Filter = {
  header?: string;
  operator?: OperatorEnum | null;
  value?: unknown;
  filters?: Filter[];
};

export type Filters = {
  operator: string;
  filters: (Filter | Filters)[];
};

type Order = Record<string, unknown>;

type Source = {
  type: ChannelSourceEnum | null;
  filters: Filters;
  order: Order;
  limit: number | null;
};

export type SourceDefinition = {
  ui: UiDefinition;
  default: Source | null;
  types: ChannelSourceEnum[];
  isFilterable: boolean;
  isOrderable: boolean;
};
