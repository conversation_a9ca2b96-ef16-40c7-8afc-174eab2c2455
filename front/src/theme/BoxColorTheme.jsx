import { Box, Stack, useTheme } from "@mui/material";
import { Body1 } from "@/components/ui/Typography";

export default function BoxColorTheme({ color, title }) {
  const theme = useTheme();

  return (
    <Stack gap={0}>
      <Box
        sx={{
          width: "60px",
          height: "40px",
          backgroundColor: color,
          borderRadius: "5px",
          border: `1px solid ${theme.palette.border.main}`,
        }}
      />
      <Body1 lineHeight={1.25}>{title}</Body1>
      <Body1 lineHeight={1.25}>{color}</Body1>
    </Stack>
  );
}
