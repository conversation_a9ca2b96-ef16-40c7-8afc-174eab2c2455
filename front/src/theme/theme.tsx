import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { Popper } from "@mui/material";
import { createTheme } from "@mui/material/styles";
import palette from "./palette";
import "./theme.css";
import { ChipDefaultFilled } from "@/components/ui/Chip";
import type { CSSProperties } from "react";

declare module "@mui/material/styles" {
  interface Theme {
    status: {
      danger: string;
    };
  }

  interface ThemeOptions {
    status?: {
      danger?: string;
    };
  }

  interface TypographyVariants {
    link: CSSProperties;
  }

  interface TypographyVariantsOptions {
    link?: CSSProperties;
  }

  interface ButtonPropsVariantOverrides {
    neutral: true;
    link: true;
  }

  interface ChipPropsColorOverrides {
    black: true;
  }

  interface Components {
    MuiTreeItem?: {
      styleOverrides?: {
        content?: {
          "&.Mui-selected"?: {
            "& .MuiTreeItem-label"?: {
              fontWeight?: string;
            };
          };
          "&.Mui-focused"?: {
            backgroundColor?: string;
            "& .MuiTreeItem-label"?: {
              fontWeight?: string;
            };
          };
        };
        iconContainer?: {
          width?: string;
          display?: string;
          alignItems?: string;
        };
      };
    };
  }
}

declare module "@mui/material/styles/components" {
  interface Components {
    MuiTreeItem?: {
      defaultProps?: {};
      styleOverrides?: {
        content?: {};
        iconContainer?: {};
      };
      variants?: [];
    };
    MuiDataGrid?: {
      defaultProps?: {};
      styleOverrides?: {};
      variants?: {};
    };
    MuiTimelineItem?: {
      styleOverrides?: {};
    };
    MuiTabPanel?: {
      styleOverrides?: {};
    };
  }
}

declare module "@mui/material/Typography" {
  interface TypographyPropsVariantOverrides {
    link: true;
  }
}

declare module "@mui/material/Button" {
  interface ButtonPropsVariantOverrides {
    neutral: true;
    link: true;
  }
}

declare module "@mui/material/Chip" {
  interface ChipPropsColorOverrides {
    black: true;
  }
}

const spacing = 7.5;

const TypographySizes = {
  h1: "2rem",
  h2: "1.75rem",
  h3: "1.5rem",
  h4: "1.25rem",
  h5: "1rem",
  h6: ".875rem",
};

const theme = createTheme({
  cssVariables: true,
  spacing: spacing,
  shape: {
    borderRadius: ".25rem" as unknown as number,
  },
  typography: {
    fontFamily: "Open Sans, sans-serif",
    fontWeightLight: 300,
    fontWeightRegular: 400,
    fontWeightMedium: 500,
    // padding: 0,
    // margin: 0,
    h1: {
      fontSize: TypographySizes.h1,
      fontWeight: "bold",
      lineHeight: "1.25",
    },
    h2: {
      fontSize: TypographySizes.h2,
      fontWeight: "bold",
    },
    h3: {
      fontSize: TypographySizes.h3,
      fontWeight: "bold",
    },
    h4: {
      fontSize: TypographySizes.h4,
      fontWeight: "bold",
    },
    h5: {
      fontSize: TypographySizes.h5,
      fontWeight: "bold",
    },
    h6: {
      fontSize: TypographySizes.h6,
      fontWeight: "bold",
    },
    body1: {
      fontSize: "0.875rem",
      fontWeight: "normal",
    },
    body2: {
      fontSize: "0.875rem",
      fontWeight: "normal",
    },
    subtitle1: {
      fontSize: "0.875rem",
      color: palette.secondary.dark,
    },
    subtitle2: {
      fontSize: "0.70rem",
      color: palette.secondary.dark,
    },
    link: {
      fontSize: "0.875rem",
      textDecoration: "underline",
      color: palette.primary.dark,
      textTransform: "none",
      cursor: "pointer",
      fontWeight: "bold",
    },
  },
  palette,
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 1000,
      lg: 1400,
      xl: 1920,
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          backgroundColor: palette.background.main,
        },
      },
    },
    MuiTypography: {
      defaultProps: {
        variantMapping: {
          link: "p",
        },
      },
      styleOverrides: {
        root: {
          textOverflow: "ellipsis",
          lineHeight: "normal",
        },
      },
    },
    MuiInputBase: {
      styleOverrides: {
        input: ({ ownerState }) => ({
          WebkitBoxShadow: "none !important",
          WebkitTextFillColor: "inherit !important",
          ...(ownerState.disabled && {
            cursor: "not-allowed",
          }),
        }),
      },
    },
    MuiStack: {
      defaultProps: {
        gap: 2,
      },
    },
    MuiButton: {
      defaultProps: {
        variant: "contained",
        disableElevation: true,
      },
      variants: [
        {
          props: { variant: "text" },
          style: {
            paddingRight: 0,
            paddingLeft: 0,
            backgroundColor: "transparent",
            "&:hover": {
              boxShadow: "none",
              backgroundColor: "transparent",
            },
            "&.MuiButton-outlined.MuiButton-colorPrimary": {
              backgroundColor: "transparent",
              borderColor: palette.border.main,
              color: palette.primary.main,
              border: "solid 1px",
              "&:hover": {
                backgroundColor: "transparent",
              },
            },
          },
        },
        {
          props: {
            variant: "neutral",
          },
          style: {
            backgroundColor: palette.white,
            border: `1px solid ${palette.border.main}`,
            color: palette.primary.main,
          },
        },
        {
          props: {
            variant: "link",
          },
          style: {
            backgroundColor: "transparent",
            border: "none",
            color: palette.secondary.main,
            textDecoration: "underline",
            whiteSpace: "nowrap",
            fontSize: "inherit",
            fontWeight: "inherit",
            outline: "none",
            padding: "0px !important",
            "&:hover": {
              textDecoration: "underline",
            },
          },
        },
        {
          props: { variant: "outlined" },
          style: {
            "&.MuiButton-outlined.MuiButton-colorPrimary": {
              backgroundColor: palette.primary.lighter,
            },
            "&.MuiButton-outlined.MuiButton-colorSuccess": {
              backgroundColor: palette.primary.light,
            },
            "&.MuiButton-outlined.MuiButton-colorError": {
              backgroundColor: palette.error.lighter,
            },
          },
        },
      ],
      styleOverrides: {
        root: {
          textTransform: "none",
          "&:hover": {
            boxShadow: "none",
            backgroundColor: "",
          },
          "&.MuiButton-outlined.MuiButton-colorPrimary": {
            borderColor: palette.border.main,
            border: "solid 1px",
          },
        },
      },
    },
    MuiChip: {
      defaultProps: {
        size: "small",
      },
      styleOverrides: {
        root: {
          width: "fit-content",
          height: "auto",
        },
        label: {
          padding: "2px 5px",
          fontSize: "12px",
        },
      },
      variants: [
        {
          props: { variant: "filled", color: "primary" },
          style: {
            backgroundColor: palette.primary.lighter,
            color: palette.primary.main,
            border: `1px solid ${palette.primary.main}`,
          },
        },
        {
          props: { variant: "filled", color: "error" },
          style: {
            backgroundColor: palette.error.lighter,
            color: palette.error.main,
            border: `1px solid ${palette.error.main}`,
          },
        },
        {
          props: { color: "black" },
          style: {
            color: palette.black,
            border: `1px solid ${palette.black}`,
          },
        },
      ],
    },
    MuiDialog: {
      defaultProps: {
        PaperProps: {
          sx: {
            border: "none",
            maxWidth: 800,
            overflow: "hidden",
          },
        },
      },
      styleOverrides: {
        root: {
          "& .MuiDialogTitle-root": {
            fontSize: TypographySizes.h3,
            padding: 0,
          },
          "& .MuiDialogContent-root": {
            padding: 0,
          },
          "& .MuiDialogActions-root": {
            padding: 0,
          },
        },
      },
    },
    MuiPaper: {
      defaultProps: {
        elevation: 0,
        variant: "outlined",
      },
      styleOverrides: {
        root: {
          textDecoration: "none",
          outline: "none !important",
          borderColor: palette.border.main,
        },
      },
    },
    MuiCheckbox: {
      defaultProps: {
        disableRipple: true,
        color: "primary",
      },
    },
    MuiMenu: {
      defaultProps: {
        elevation: 0,
        anchorOrigin: {
          vertical: "bottom",
          horizontal: "left",
        },
        transformOrigin: {
          vertical: "top",
          horizontal: "left",
        },
        PaperProps: {
          square: true,
        },
        slotProps: {
          list: {
            disablePadding: true,
          },
        },
      },
    },
    MuiList: {
      defaultProps: {
        disablePadding: true,
      },
      styleOverrides: {
        root: {
          ".MuiInputLabel-root": {
            display: "none !important",
            outline: "0 !important",
          },
        },
      },
    },
    MuiFormControl: {
      defaultProps: {
        size: "small",
      },
    },
    MuiDivider: {
      styleOverrides: {
        root: {
          margin: "0px !important",
        },
      },
    },
    MuiAutocomplete: {
      defaultProps: {
        popupIcon: <ExpandMoreIcon sx={{ color: "black" }} />,

        renderTags: (value, getTagProps) =>
          value.map((option, index) => {
            const { key, ...tagProps } = getTagProps({ index });
            return (
              <ChipDefaultFilled label={option?.label ?? option} key={key} {...tagProps}>
                {null}
              </ChipDefaultFilled>
            );
          }),

        slots: {
          popper: (props) => <Popper {...props} placement="bottom-start" />,
        },
      },
      styleOverrides: {
        tag: {
          margin: 0,
        },
        root: {
          fontWeight: "bold",
        },
        popper: {
          width: "auto !important",
        },
        option: {
          fontWeight: "bold",
          "&:hover": {
            svg: {
              fill: palette.primary.main,
            },
            color: palette.primary.main,
            backgroundColor: palette.primary.lighter,
          },
          "&[aria-selected='true']": {
            svg: {
              fill: palette.primary.main,
            },
            color: palette.primary.main,
            backgroundColor: "inherit",
          },
        },
      },
    },
    MuiSelect: {
      defaultProps: {
        IconComponent: ExpandMoreIcon,
        size: "small",
      },
      styleOverrides: {
        icon: {
          fill: palette.primary.dark,
          "&.Mui-disabled": {
            fill: palette.disabled.main,
          },
        },
        standard: {
          padding: 0,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: "none",
          "&:hover": {
            border: `1px solid ${palette.border.dark}`,
          },
          border: `1px solid ${palette.border.main}`,
          padding: "15px",
        },
      },
    },
    MuiFormLabel: {
      styleOverrides: {
        root: {
          padding: "0.25rem 0",
          color: palette.primary.dark,
        },
      },
    },
    MuiPaginationItem: {
      styleOverrides: {
        page: {
          "&.Mui-selected": {
            backgroundColor: palette.white,
            fontWeight: "bold",
            border: `solid 1px ${palette.border.main}`,
          },
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          "& .MuiTableCell-root": {
            padding: "0 15px",
            backgroundColor: ({ ownerState }: { ownerState: { bgcolor: string } }) => ownerState?.bgcolor ?? palette.background.main,
          },
          "& p": {
            fontSize: "0.75rem",
            fontWeight: "bold",
          },
        },
      },
    },
    MuiTableSortLabel: {
      styleOverrides: {
        root: {
          width: "100%",
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          padding: "15px",
          backgroundColor: "inherit",
          maxWidth: "200px",
          textOverflow: "ellipsis",
          overflow: "hidden",
          borderColor: palette.border.main,
        },
        paddingCheckbox: {
          width: "5px",
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: ({ ownerState }) => ({
          textDecoration: "none",
          "&:hover": {
            backgroundColor: palette.primary.lighter,
          },
          ...(ownerState.onClick
            ? {
                cursor: "pointer",
              }
            : {}),
          ...(ownerState.disabled
            ? {
                backgroundColor: palette.disabled.main,
                "& .MuiTableCell-root": {
                  color: palette.disabled.main,
                },
                "&:hover": {
                  backgroundColor: palette.secondary.light,
                  "& .MuiTableCell-root": {
                    color: palette.secondary.dark,
                  },
                },
              }
            : {}),
        }),
      },
    },
    MuiTableBody: {
      styleOverrides: {
        root: {
          backgroundColor: palette.white,
          "& .MuiTableCell-root": {
            "&:first-of-type": {
              borderLeft: `solid 1px ${palette.border.main}`,
            },
            "&:last-child": {
              borderRight: `solid 1px ${palette.border.main}`,
            },
          },
        },
      },
    },
    MuiTable: {
      defaultProps: {
        stickyHeader: true,
      },
    },
    MuiToggleButtonGroup: {
      styleOverrides: {
        root: {
          backgroundColor: "white",
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          gap: 5,
          // for end adornments
          paddingRight: 8,
          backgroundColor: "white",
          borderColor: palette.border.main,
          borderWidth: "0.5px",
          "& .MuiOutlinedInput-notchedOutline": {
            borderColor: `${palette.border.main} !important`,
            borderWidth: "0.5px",
          },
          "&:hover .MuiOutlinedInput-notchedOutline": {
            borderColor: `${palette.border.main} !important`,
            borderWidth: "0.5px",
          },
          "& input::placeholder, & textarea::placeholder": {
            fontWeight: "lighter",
          },
          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderColor: `${palette.border.main} !important`,
            borderWidth: "0.5px",
          },
        },
      },
    },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          fontWeight: "bold",
          color: "black",
          "& .MuiSvgIcon-root": {
            marginRight: "0.2rem",
          },
          "&:hover, &.Mui-selected:hover": {
            svg: {
              fill: palette.primary.main,
            },
            color: palette.primary.main,
            backgroundColor: palette.primary.lighter,
          },
          "&.Mui-selected": {
            svg: {
              fill: palette.primary.main,
            },
            color: palette.primary.main,
            backgroundColor: "inherit",
            fontWeight: "bold",
          },
        },
      },
    },
    MuiListItemText: {
      styleOverrides: {
        root: {
          marginTop: 0,
          marginBottom: 0,
        },
      },
    },
    MuiTooltip: {
      styleOverrides: {
        arrow: () => ({
          color: palette.black,
        }),
        tooltip: () => ({
          backgroundColor: palette.black,
        }),
      },
    },
    MuiTreeItem: {
      styleOverrides: {
        content: {
          "&.Mui-selected": {
            "& .MuiTreeItem-label": {
              fontWeight: "bold",
            },
          },
          "&.Mui-focused": {
            backgroundColor: "initial",
            "& .MuiTreeItem-label": {
              fontWeight: "bold",
            },
          },
        },
        iconContainer: {
          width: "auto",
          display: "flex",
          alignItems: "center",
        },
      },
    },
    MuiTimelineItem: {
      styleOverrides: {
        root: {
          minHeight: "45px",
        },
      },
    },
    MuiTabPanel: {
      styleOverrides: {
        root: {
          padding: 0,
        },
      },
    },
    MuiFormHelperText: {
      styleOverrides: {
        root: {
          "&.Mui-error": {
            whiteSpace: "pre-wrap",
          },
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          textTransform: "none",
          "&.Mui-selected": {
            backgroundColor: palette.background.main,
            fontWeight: "bold",
          },
        },
      },
    },
    MuiListItemIcon: {
      styleOverrides: {
        root: {
          minWidth: "auto",
        },
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: {
          gap: spacing,
        },
      },
    },
    MuiStepper: {
      styleOverrides: {
        root: {
          minWidth: "50%",
          alignItems: "flex-start",
        },
      },
    },
    MuiStepLabel: {
      styleOverrides: {
        root: {
          alignItems: "flex-start",
        },
        labelContainer: {
          width: "auto",
          marginTop: "3px",
          "& svg": {
            fontSize: "20px",
          },
          "& .MuiStepLabel-alternativeLabel": {
            marginTop: "10px !important",
          },
        },
        iconContainer: {
          "& svg": {
            fontSize: "29px",
          },
        },
      },
    },
    MuiStepConnector: {
      styleOverrides: {
        root: {
          marginTop: "13px",
        },
      },
    },
    MuiDataGrid: {
      styleOverrides: {
        root: {
          "& div[data-id='gridPanelAnchor']": {
            bottom: 0,
            top: "unset",
          },
        },
        toolbarContainer: {
          padding: 0,
        },
        cell: {
          lineHeight: "unset",
          display: "flex",
          alignItems: "center",
          outline: "transparent",
          "&:focus-within": {
            outline: "none",
          },
        },
        columnHeader: {
          outline: "transparent",
          "&:focus-within": {
            outline: "none",
          },
        },
        row: {
          "&.status-true": {
            borderLeft: `5px solid ${palette.green.main}`,
          },
          "&.status-false": {
            borderLeft: `5px solid ${palette.error.main}`,
          },
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        root: {
          minHeight: "fit-content",
        },
      },
    },
  },
});

export default theme;
