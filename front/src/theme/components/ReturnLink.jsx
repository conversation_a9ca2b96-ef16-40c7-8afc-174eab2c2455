import styled from "@emotion/styled";
import { Box } from "@mui/material";
import { useTranslation } from "@/hooks/useTranslation";
import { useNavigate } from "react-router";
import { Body1 } from "@/components/ui/Typography";
import ReturnIcon from "@/assets/icons/ReturnIcon";

const ReturnLink = styled(Box)(({ theme }) => ({
  display: "inline-flex",
  cursor: "pointer",
  alignItems: "center",
  gap: theme.spacing(1),
  "&:hover": {
    textDecoration: "underline",
  },
}));

export default function OurReturnLink({ url = -1 }) {
  const navigate = useNavigate();
  const { t } = useTranslation();

  return (
    <ReturnLink onClick={() => navigate(url)}>
      <ReturnIcon />
      <Body1 sx={{ fontWeight: "bold" }}>{t("actions.return")}</Body1>
    </ReturnLink>
  );
}
