import { AppBar as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tab as <PERSON><PERSON><PERSON>ab, Tabs as Mu<PERSON>Tabs, useTheme } from "@mui/material";

export default function FormTabs({ children, tabs, activeTab, onChange, disabled = [], variant = "fullWidth" }) {
  const theme = useTheme();

  return (
    <MuiAppBar position="static" color="transparent">
      <MuiTabs
        value={activeTab}
        onChange={onChange}
        variant={variant}
        slotProps={{
          indicator: {
            style: { display: "none" },
          }
        }}
      >
        {tabs.map((tab, index) => (
          <MuiTab
            key={index}
            label={tab}
            style={{
              color: disabled.includes(index) ? "grey" : "black",
              backgroundColor: activeTab === index ? "transparent" : "white",
              borderBottom: activeTab !== index ? `1px solid ${theme.palette.border.main}` : "1px solid transparent",
              borderLeft: activeTab + 1 === index ? `1px solid ${theme.palette.border.main}` : "none",
              borderRight: activeTab !== index && index + 1 !== tabs.length ? `1px solid ${theme.palette.border.main}` : "none",
            }}
            disabled={disabled.includes(index)}
          />
        ))}
        {children}
      </MuiTabs>
    </MuiAppBar>
  );
}
