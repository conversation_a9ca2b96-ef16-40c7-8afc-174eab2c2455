import type { LocaleCode } from "@/types/model/LocaleCode";
import type { ScopeCode } from "@/types/model/ScopeCode";
import type { Code } from "@/types/model/Code";
import { HeaderTypeEnum } from "@/enums";

export const HEADER_TYPES = {
  PROPERTY: HeaderTypeEnum.PROPERTY,
  VIRTUAL_PROPERTY: HeaderTypeEnum.VIRTUAL_PROPERTY,
  ATTRIBUTE: HeaderTypeEnum.ATTRIBUTE,
  COMPLETUDE: HeaderTypeEnum.COMPLETUDE,
  WORKFLOW: HeaderTypeEnum.WORKFLOW,
};

// console.log(Header.create("sku"));
// console.log(Header.create("#sku"));
// console.log(Header.create("#sku.fr_FR"));
// console.log(Header.create("#sku/sephora"));
// console.log(Header.create("#sku.en_US/sephora"));
// console.log(Header.create("%completude_test"));
// console.log(Header.create("&workflow_test.fr_FR/sephora"));

export default class Header {
  private readonly type: HeaderTypeEnum;

  private readonly code: Code;

  private readonly locale: LocaleCode | null;

  private readonly scope: ScopeCode | null;

  constructor(type: HeaderTypeEnum, code: Code, locale: LocaleCode | null = null, scope: ScopeCode | null = null) {
    this.type = type;
    this.code = code;
    this.locale = locale;
    this.scope = scope;
  }

  get isProperty(): boolean {
    return HEADER_TYPES.PROPERTY === this.type;
  }

  get isAttribute(): boolean {
    return HEADER_TYPES.ATTRIBUTE === this.type;
  }

  get isCompletude(): boolean {
    return HEADER_TYPES.COMPLETUDE === this.type;
  }

  get isVirtualProperty(): boolean {
    return HEADER_TYPES.VIRTUAL_PROPERTY === this.type;
  }

  get isWorkflow(): boolean {
    return HEADER_TYPES.WORKFLOW === this.type;
  }

  get isLocalized(): boolean {
    return null !== this.locale;
  }

  get isScoped(): boolean {
    return null !== this.scope;
  }

  static create(header: string): Header {
    const m = /\s*(#|&|%|@)?([^./]+)\s*(?:\.([\w_]+))?\s*(?:\/(.+))?\s*/.exec(header);
    if (null === m) {
      throw "invalid header";
    }

    let type = HeaderTypeEnum.PROPERTY;
    if ("#" === m[1]) {
      type = HeaderTypeEnum.ATTRIBUTE;
    } else if ("%" === m[1]) {
      type = HeaderTypeEnum.COMPLETUDE;
    } else if ("&" === m[1]) {
      type = HeaderTypeEnum.WORKFLOW;
    } else if ("@" === m[1]) {
      type = HeaderTypeEnum.VIRTUAL_PROPERTY;
    }

    const splited = header.split(/([/.])/);
    let locale = null;
    let scope = null;

    if (splited[1] === "/") { // Scope is before the locale in the header
      scope = splited[2]
      locale = splited[4] || null;
    } else if (splited[1] === ".") { // Locale is before the scope in the header
      locale = splited[2];
      scope = splited[4] || null;
    }

    return new Header(type, m[2], locale, scope);
  }

  toString(): string {
    return [this.isAttribute ? "#" : null, this.isCompletude ? "%" : null, this.isWorkflow ? "&" : null, this.isVirtualProperty ? "@" : null, this.code, ...(this.isLocalized ? [".", this.locale] : []), ...(this.isScoped ? ["/", this.scope] : [])]
      .filter((i) => null !== i)
      .join("");
  }
}
