import { <PERSON>Default, ChipError, ChipSecondary, ChipWarning } from "../ui/Chip";
import { useTranslation } from "@/hooks/useTranslation";
import { RuleLevelEnum } from "@/enums";

export default function ChipRuleLevel({ level }) {
  const { t } = useTranslation();

  if (!level) {
    return null;
  }

  switch (level) {
    case RuleLevelEnum.LOW:
      return <ChipSecondary>{t("rules.status.low")}</ChipSecondary>;
    case RuleLevelEnum.MEDIUM:
      return <ChipWarning>{t("rules.status.medium")}</ChipWarning>;
    case RuleLevelEnum.HIGH:
      return <ChipError>{t("rules.status.high")}</ChipError>;
    case RuleLevelEnum.BLOCKER:
      return <ChipDefault>{t("rules.status.blocker")}</ChipDefault>;
    default:
      return <ChipError>{t("rules.status.medium")}</ChipError>;
  }
}
