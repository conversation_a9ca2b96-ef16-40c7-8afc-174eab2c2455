import { Stack } from "@mui/material";
import { Fragment } from "react";
import Tooltip from "@/components/ui/Tooltip";

export default function Chips({ chips, max }) {

  const array = chips.filter((e) => !!e);
  const displayed = array.slice(0, max);
  const hidden = array.slice(max);

  return (
    <Stack direction="row" gap={1} alignItems="center">
      <Stack direction="row" gap={1} useFlexGap flexWrap="wrap">
        {displayed.map((chip, i) => (
          <Fragment key={i}>{chip}</Fragment>
        ))}
      </Stack>
      {hidden?.length > 0 && (
        <Tooltip
          light
          title={
            <Stack gap={1}>
              {hidden.map((chip, i) => (
                <div key={i}>{chip}</div>
              ))}
            </Stack>
          }
        >
          {" "}
          <span>+ {hidden?.length}</span>
        </Tooltip>
      )}
    </Stack>
  );
}
