import { useGridApiRef } from "@mui/x-data-grid";
import theme from "@/theme/theme";
import TableColumnsPanel from "./ui/TableColumnsPanel";
import { useTranslation } from "@/hooks/useTranslation";
import { TableCell } from "@mui/material";
import { useUser } from "@/contexts/UserProvider";
import { useView } from "@/contexts/ViewProvider";
import { ITEM_PROPERTIES } from "@/enums/ITEM_PROPERTIES";
import { COLUMN_ATTRIBUTE_TYPES } from "@/enums/COLUMN_TYPES";
import Header, { HEADER_TYPES } from "@/utils/models/Header";
import { useAttributes } from "@/contexts/AttributesProvider";
import Light from "./ui/attribute/Light";
import DOCUMENT_TYPES from "@/enums/DOCUMENT_TYPES";
import { useCompletudes } from "@/contexts/CompletudesProvider";
import ChipNamed from "./chips/ChipNamed";
import DataTableFooter from "./DataTableFooter";
import DataTableRow from "./DataTableRow";
import { useChannels } from "@/contexts/ChannelsProvider";
import { DataGridPro } from "@mui/x-data-grid-pro";
import { DirectionEnum } from "@/enums";

export default function DataTable({ paginator, detailLink, onRowClick = null, rowProps = null, rowReordering = false }) {
  const { t } = useTranslation();
  const apiRef = useGridApiRef();
  const { attributes, getValueData } = useAttributes();
  const { preferences } = useUser();
  const { saveColumns, view } = useView();
  const { completudes } = useCompletudes();
  const { channels } = useChannels();
  const itemView = view?.[paginator.type];
  const columns = itemView?.columns || [];

  const propertyColumns = (ITEM_PROPERTIES[paginator.type] || []).map((e) => ({
    field: new Header(e.headerType, e.code).toString(),
    headerName: e?.hideHeader ? "" : t(e.label),
    type: COLUMN_ATTRIBUTE_TYPES[e?.type],
    sortable: e.isSearchable || false,
    renderCell: e.renderCell,
    flex: e.flex,
    width: e.width,
  }));

  const attributeColumns = Object.values(DOCUMENT_TYPES).includes(paginator.type)
    ? attributes?.map((e) => {
        const header = new Header(HEADER_TYPES.ATTRIBUTE, e.code);
        return {
          field: header.toString(),
          headerName: t(e.names),
          type: COLUMN_ATTRIBUTE_TYPES[e?.type],
          sortable: e.isSearchable || false,
          minWidth: 200,
          renderCell: (params) => {
            return <Light type={e.type} attribute={header.code} value={e?.names ? getValueData(params.row.values, header.code, preferences.getLocale(), preferences.getScope()) : params.row[header.code]} locale={preferences.getLocale()} />;
          },
        };
      })
    : [];

  const completnessColumns = Object.values(DOCUMENT_TYPES).includes(paginator.type)
    ? completudes?.map((completude) => ({
        field: new Header(HEADER_TYPES.COMPLETUDE, completude.code).toString(),
        headerName: t(completude.names),
        type: null,
        sortable: false,
        minWidth: 200,
        renderCell: (params) => {
          const currentCompletude = params.row.completudes.completudes.find((e) => e.locale === preferences.getLocale() && e.code === completude.code);
          if (!currentCompletude) {
            return <Light />;
          }

          return (
            <TableCell sx={{ paddingLeft: 0, paddingRight: 0, border: "none" }}>
              <ChipNamed name={t(currentCompletude?.names)} color={currentCompletude.score === 100 ? theme.palette.green.main : theme.palette.error.main}>
                {currentCompletude.score}%
              </ChipNamed>
            </TableCell>
          );
        },
      }))
    : [];

  const channelsColumns = Object.values(DOCUMENT_TYPES).includes(paginator.type)
    ? channels?.map((channel) => ({
        field: new Header(HEADER_TYPES.WORKFLOW, channel.code).toString(),
        headerName: t(channel.names),
        type: null,
        sortable: false,
        minWidth: 200,
        renderCell: (params) => {
          const currentWorkflow = params.row.workflows.workflows.find((e) => e.locale === preferences.getLocale() && e.channel === channel.code);
          if (!currentWorkflow) {
            return <Light />;
          }
          return (
            <TableCell sx={{ paddingLeft: 0, paddingRight: 0, border: "none" }}>
              <ChipNamed name={t(currentWorkflow?.channel)} color={currentWorkflow.score === 100 ? theme.palette.green.main : theme.palette.error.main}>
                {currentWorkflow.score}%
              </ChipNamed>
            </TableCell>
          );
        },
      }))
    : [];

  const visibleColumns = columns.length > 0 ? columns : propertyColumns.map((e) => e.field);
  const allColumns = propertyColumns
    .concat(completnessColumns)
    .concat(attributeColumns)
    .concat(channelsColumns)
    .filter((e) => e);

  return (
    <DataGridPro
      apiRef={apiRef}
      columns={allColumns}
      rows={paginator.items.map((item) => ({ ...item, rowProps }))}
      loading={paginator.isLoading}
      getRowId={(row) => row.uuid || row.code || row.id || row["@id"]}
      rowCount={paginator.count}
      getRowClassName={(params) => `status-${params.row.status}`}
      rowReordering={!!rowReordering}
      disableColumnSorting={!!rowReordering}
      onRowOrderChange={(e) => {
        paginator.moveOrder(e.oldIndex, e.targetIndex, true);
      }}
      disableColumnFilter
      disableRowSelectionOnClick
      disableMultipleRowSelection
      sortingMode="server"
      paginationMode="server"
      slots={{ footer: DataTableFooter, columnsPanel: TableColumnsPanel, row: DataTableRow }}
      slotProps={{
        panel: { placement: "top-start" },
        columnsPanel: { type: paginator.type },
        footer: { paginator },
        row: { detailLink, onRowClick, style: { minHeight: "50px" } },
      }}
      initialState={{
        density: preferences.getDensity(),
        pagination: {
          paginationModel: {
            pageSize: paginator.limit,
            page: paginator.page - 1,
          },
        },
        sorting: {
          sortModel: Object.entries(paginator.order).map(([key, value]) => ({ field: key, sort: DirectionEnum[value] || value })),
        },
        columns: {
          columnVisibilityModel: allColumns.reduce((acc, item) => {
            acc[item.field] = !!visibleColumns.includes(item.field);
            return acc;
          }, {}),
        },
      }}
      pageSizeOptions={[12, 24, 36, 48, 60, 72]}
      onPaginationModelChange={(paginationModel) => {
        preferences.setLimit(paginationModel.pageSize);
        paginator.setLimit(paginationModel.pageSize);
        paginator.goto(paginationModel.page + 1);
      }}
      onColumnVisibilityModelChange={(model) => {
        const array = Object.entries(model)
          .map(([key, value]) => ({ field: key, value }))
          .filter((e) => e.value)
          .map((e) => e.field);
        const newColumns = array.map((e) => e);
        if (JSON.stringify(newColumns) !== JSON.stringify(columns) && saveColumns) {
          saveColumns(paginator.type, newColumns);
        }
      }}
      onSortModelChange={(model) => {
        const newOrder = model?.[0];
        if (newOrder) {
          paginator.orderBy(newOrder.field, newOrder.sort);
        }
      }}
      onDensityChange={(newDensity) => preferences.setDensity(newDensity)}
      localeText={{
        toolbarColumns: t("components.DataTable.columns._"),
        toolbarDensity: t("components.DataTable.density._"),
        toolbarDensityComfortable: t("components.DataTable.density.comfortable"),
        toolbarDensityCompact: t("components.DataTable.density.compact"),
        toolbarDensityStandard: t("components.DataTable.density.standard"),
        columnMenuSortAsc: t("components.DataTable.sort.asc"),
        columnMenuSortDesc: t("components.DataTable.sort.desc"),
        columnMenuUnsort: t("components.DataTable.sort.unsort"),
        columnMenuManageColumns: t("components.DataTable.columns.manage_columns_title"),
        columnMenuHideColumn: t("components.DataTable.columns.hide_column"),
        columnHeaderSortIconLabel: t("components.DataTable.sort._"),
        noResultsOverlayLabel: t("common.no_result"),
      }}
    />
  );
}
