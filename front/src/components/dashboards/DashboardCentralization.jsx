import { H4, H5 } from "@/components/ui/Typography";
import { Card, CircularProgress, Stack } from "@mui/material";
import { useTranslation } from "@/hooks/useTranslation";
import { useEffect, useState } from "react";
import { useApi } from "@/contexts/ApiProvider";
import CardChart from "../hubDashboard/CardChart";
import FormatUtils from "@/utils/format.utils";

export default function DashboardCentralization() {
  const [productData, setProductData] = useState(null);
  const [contentData, setContentData] = useState(null);
  const [mediaData, setMediaData] = useState(null);
  const [configData, setConfigData] = useState(null);
  const isLoading = !productData || !mediaData || !contentData || !configData;

  const { t } = useTranslation();
  const api = useApi();

  useEffect(() => {
    const loadData = async () => {
      try {
        await api.get("/api/dashboard-stats/Product").then((productData) => setProductData(productData));
        await api.get("/api/dashboard-stats/Content").then((contentData) => setContentData(contentData));
        await api.get("/api/dashboard-stats/Media").then((mediaData) => setMediaData(mediaData));
        await api.get("/api/dashboard-stats").then((configData) => setConfigData(configData["hydra:member"]));
      } catch (error) {
        console.log("Error while fetching data", error);
      }
    };

    void loadData();
  }, []);

  // if (isLoading) {
  //   return <LoaderPage />;
  // }

  return (
    <Stack height="100%">
      <H4>{t("homepage.title")}</H4>
      <Stack direction="row" justifyContent="space-between" alignItems="center" width="100%">
        <CardChart title={t("items.Product._")} percent={productData?.percentage} amount={FormatUtils.spaceOnNumbers(productData?.amount)} chartData={productData?.statistics} isLoading={isLoading} />
        <Card variant="outlined" component={Stack} gap={1} height="100%" width="25%">
          {configData ? (
            configData?.map((data, index) => (
              <Stack key={index} direction="row" justifyContent="space-between" alignItems="center">
                <H5>{t(`items.Dashboard.${data.code}`)}</H5>
                <H5>{FormatUtils.spaceOnNumbers(data.count)}</H5>
              </Stack>
            ))
          ) : (
            <Stack justifyContent="center" alignItems="center" height="100%">
              <CircularProgress />
            </Stack>
          )}
        </Card>
      </Stack>
      <CardChart title={t("items.Media._")} percent={mediaData?.percentage} amount={FormatUtils.spaceOnNumbers(mediaData?.amount)} chartData={mediaData?.statistics} isLoading={isLoading} />
      <CardChart title={t("items.Content._")} percent={contentData?.percentage} amount={FormatUtils.spaceOnNumbers(contentData?.amount)} chartData={contentData?.statistics} isLoading={isLoading} />
    </Stack>
  );
}
