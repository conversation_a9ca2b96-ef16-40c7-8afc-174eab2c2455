import { useUser } from "@/contexts/UserProvider";
import { Body1, H4, H5 } from "@/components/ui/Typography";
import { Grid2, Paper, Stack, useTheme } from "@mui/material";
import { useTranslation } from "@/hooks/useTranslation";
import { Link, useSearchParams } from "react-router";
import RULE_TYPES from "@/enums/RULE_TYPES";
import DashboardCard from "../ui/dashboards/DashboardCard";
import FormatUtils from "@/utils/format.utils";
import SelectProductFile from "../selects/SelectProductFile";
import AttributeStats from "../ui/dashboards/AttributeStats";
import SelectChannel from "../selects/SelectChannel";
import ButtonSync from "../buttons/ButtonSync";
import { useEffect, useState } from "react";
import { useApi } from "@/contexts/ApiProvider";
import LoaderPage from "../loaders/LoaderPage";
import { useSettings } from "@/contexts/SettingsProvider";
import { ApiTypeEnum, RouteEnum } from "@/enums";

export const generateLink = (searchParams) => {
  const baseUrl = RouteEnum.OPTIMIZATION_ERRORS;
  const params = new URLSearchParams();

  for (const key of Object.keys(searchParams)) {
    if (searchParams[key]) {
      params.append(key, searchParams[key]);
    }
  }

  const queryString = params.toString();
  return queryString ? `${baseUrl}?${queryString}` : baseUrl;
};

export default function DashboardOptimization() {
  const [data, setData] = useState(null);
  const { t } = useTranslation();
  const theme = useTheme();
  const { rights } = useUser();
  const [searchParams, setSearchParams] = useSearchParams();
  const api = useApi();
  const channel = searchParams.get("channel");
  const skus = searchParams.get("skus");
  const { isLVMH } = useSettings();

  useEffect(() => {
    const loadData = async () => {
      try {
        const skuParams = prepareSkus();
        const data = await api.get(`/api/dashboards/optimisation`, {
          ...skuParams,
          ...(channel ? { channel } : {}),
        });
        setData(data.stats ?? {});
      } catch (error) {
        console.log("Error while fetching data", error);
      }
    };

    void loadData();
  }, [channel, skus]);

  const prepareSkus = () => {
    if (skus) {
      const arrayOfSkus = skus.split(",");
      const skuParam = {};
      arrayOfSkus.forEach((value, i) => {
        skuParam[`sku[${i}]`] = value;
      });
      return skuParam;
    }
    return {};
  };

  if (!data) {
    return <LoaderPage />;
  }

  const productData = [
    {
      value: data?.productStats?.valid,
      name: "valid",
      label: t("lvmh.dash.status.valid_product"),
      color: theme.palette.green.main,
    },
    {
      value: data?.productStats?.error,
      name: RULE_TYPES.ERROR,
      label: t("lvmh.dash.status.product_without_blocker"),
      color: theme.palette.warning.main,
    },
    {
      value: data?.productStats?.blocker,
      name: "blocker",
      label: t("lvmh.dash.status.blocker_product"),
      color: theme.palette.error.main,
    },
  ];

  const handleChangeSkus = (values) => {
    if (values?.length) {
      searchParams.set("skus", values);
    } else {
      searchParams.delete("skus");
    }
    setSearchParams(searchParams);
  };

  const handleChangeChannel = (value) => {
    if (value?.length) {
      searchParams.set("channel", value);
    } else {
      searchParams.delete("channel");
    }
    setSearchParams(searchParams);
  };

  return (
    <>
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <H4>{t("homepage.title")}</H4>
        <Stack direction="row">
          {rights.canSyncSettings() && <ButtonSync type={ApiTypeEnum.SETTINGS}>{t("homepage.pop_sync_msg")}</ButtonSync>}
          <Grid2 container alignItems="center" width={600} spacing={2}>
            <Grid2 size={{ xs: 6 }}>
              <SelectProductFile getterKey="sku" noLabel multiple defaultValue={skus ? skus.split(",") : []} onChange={(value) => handleChangeSkus(value)} />
            </Grid2>
            <Grid2 size={{ xs: 6 }}>
              <SelectChannel getterKey="code" noLabel defaultValue={channel} onChange={(val) => handleChangeChannel(val)} />
            </Grid2>
          </Grid2>
        </Stack>
      </Stack>
      <Stack display="grid" gridTemplateColumns={{ md: "1fr", lg: "repeat(2, 1fr)" }}>
        <Stack component={Paper} p={2} gap={1}>
          <Body1>{t("items.Product._")}</Body1>
          <H5>{FormatUtils.spaceOnNumbers(data.products)}</H5>
        </Stack>
        <Stack component={Paper} p={2} gap={1}>
          <Body1>{t("inputs.errors")}</Body1>
          <H5 color="error">{FormatUtils.spaceOnNumbers(data.errors)}</H5>
        </Stack>
        {data.productStats ? <DashboardCard title={t("lvmh.dash.title.products_by_completion")} data={productData} count={data.products} /> : null}
        {data.valueStats?.map((item, i) => {
          const array = [
            {
              value: item.valid,
              name: "valid",
              label: t("lvmh.dash.status.valid_attribute"),
              color: theme.palette.green.main,
            },
            {
              value: item.error,
              name: RULE_TYPES.ERROR,
              label: t("lvmh.dash.status.attribute_error"),
              color: theme.palette.warning.main,
            },
            {
              value: item.missing,
              name: RULE_TYPES.MISSING,
              label: t("lvmh.dash.status.empty_attribute"),
              color: theme.palette.error.main,
            },
          ];

          const params = { ruleGroup: item.ruleGroup?.code, catalogScope: channel, skus };

          return (
            <Link key={i} to={generateLink(params)} style={{ textDecoration: "none" }}>
              <DashboardCard
                title={item.ruleGroup ? t("lvmh.dash.title.attributes_by_typologie") + t(item.ruleGroup.names) : t("lvmh.dash.title.attributes_by_status")}
                data={array}
                count={item.valid + item.error + item.missing}
                params={params}
                hasOnClick
              />
            </Link>
          );
        })}
      </Stack>
      {isLVMH() ? (
        <Stack display="grid" gridTemplateColumns={{ md: "1fr", lg: "repeat(2, 1fr)" }}>
          <AttributeStats attributeCode="gaia_integration_product_status" />
          <AttributeStats attributeCode="gaia_integration_logistic_status" />
          <AttributeStats attributeCode="gaia_delivery_gaia_product_status" />
          <AttributeStats attributeCode="gaia_delivery_gaia_logistic_status" />
          <AttributeStats attributeCode="gaia_delivery_trading_partner_product_status" channel={channel} />
          <AttributeStats attributeCode="gaia_delivery_trading_partner_logistic_status" channel={channel} />
        </Stack>
      ) : null}
    </>
  );
}
