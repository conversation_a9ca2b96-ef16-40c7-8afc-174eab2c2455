import { GridRow } from "@mui/x-data-grid";
import { Link } from "react-router";

export default function DataTableRow({ detailLink, onRowClick, ...props }) {
  return detailLink ? (
    <Link style={{ textDecoration: "none", color: "inherit" }} to={detailLink(props.row)}>
      <GridRow {...props} />
    </Link>
  ) : onRowClick ? (
    <GridRow {...props} onClick={() => onRowClick(props.row)} style={{ cursor: "pointer" }} />
  ) : (
    <GridRow {...props} />
  );
}
