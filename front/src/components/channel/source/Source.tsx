import {type Dispatch, type ReactElement, type SetStateAction, useEffect, useState} from "react";
import SimpleTabs from "@/components/ui/tabs/SimpleTabs";
import {useTranslation} from "@/hooks/useTranslation";
import type {SimpleTab} from "@/components/ui/tabs/types";
import type {Source as SourceType} from "@/components/channel/source/types";
import {ChannelSourceEnum} from "@/enums/ChannelSourceEnum";
import DOCUMENT_TYPE_ICONS from "@/enums/DOCUMENT_TYPE_ICONS";
import DividerLeft from "@/components/ui/dividers/DividerLeft";
import {H3} from "@/components/ui/Typography";
import type {Filters as FiltersType} from "@/types/definition/SourceDefinition";
import FilterForm from "@/components/filters/FilterForm";
import {FilterOperatorEnum} from "@/enums";

export type SourceProps = {
  source: SourceType;
  onChange: Dispatch<SetStateAction<SourceType>>;
};

export default function Source({source, onChange}: SourceProps): ReactElement {
  const {t} = useTranslation("components.channel.source");

  const tabs: SimpleTab<ChannelSourceEnum>[] = [
    {
      key: ChannelSourceEnum.PRODUCT,
      children: <SourceForm source={source} onChange={onChange} />,
      label: `items.${ChannelSourceEnum.PRODUCT}._`,
      icon: DOCUMENT_TYPE_ICONS[ChannelSourceEnum.PRODUCT],
    },
    {
      key: ChannelSourceEnum.CONTENT,
      children: <SourceForm source={source} onChange={onChange} />,
      label: `items.${ChannelSourceEnum.CONTENT}._`,
      icon: DOCUMENT_TYPE_ICONS[ChannelSourceEnum.CONTENT],
    },
    {
      key: ChannelSourceEnum.MEDIA,
      children: <SourceForm source={source} onChange={onChange} />,
      label: `items.${ChannelSourceEnum.MEDIA}._`,
      icon: DOCUMENT_TYPE_ICONS[ChannelSourceEnum.MEDIA],
    },
  ];

  const setSelectedSourceType = (key: ChannelSourceEnum) => {
    onChange((prev) => ({...prev, type: key, parameters: {}}));
  };

  return (
    <SimpleTabs tabs={tabs.filter((e) => source.definition?.types.includes(e.key))} onChange={(tab: SimpleTab<ChannelSourceEnum>) => setSelectedSourceType(tab.key)} isActive={(tab) => source.type === tab.key}>
      <p>{t("choose_one")}</p>
    </SimpleTabs>
  );
}

function SourceForm({source, onChange}: SourceProps): ReactElement {
  const {t} = useTranslation("components.channel.source");
  const [filters, setFilters] = useState<FiltersType>(source.filters ?? {operator: FilterOperatorEnum.AND, filters: []});

  let filter: ReactElement = <p>{t("unfilterable")}</p>;
  if (source.definition?.isFilterable) {
    filter = <FilterForm filters={filters} onChange={setFilters} type={source.type} />;
  }

  useEffect(() => {
    onChange((prevState) => ({
      ...prevState,
      filters: filters,
    }));
  }, [filters]);

  return (
    <>
      <H3>{t(`items.${source.type}._`)}</H3>
      <p>{t("configure")}</p>
      <DividerLeft label={t("common.filters")} />
      {filter}
    </>
  );
}
