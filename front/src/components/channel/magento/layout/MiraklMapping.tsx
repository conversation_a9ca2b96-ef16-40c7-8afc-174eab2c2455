import { useApi } from "@/contexts/ApiProvider";
import { useEffect, useState } from "react";
import LoaderPage from "@/components/loaders/LoaderPage";
import { useSnack } from "@/contexts/SnackProvider";
import { Stack, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import { useTranslation } from "@/hooks/useTranslation";
import { H2 } from "@/components/ui/Typography";
import ChannelWorkflowExport from "@/components/channel/channelWorkflowExport/ChannelWorkflowExport";
import type { ChannelWorkflowExport as ChannelWorkflowExportType } from "@/components/channel/channelWorkflowExport/types";
import NoResultValue from "@/components/ui/NoResultValue";
import type { Channel } from "@/router/pages/diffusion/channelsV2/type";

type MiraklMappingProps = {
  channel: Channel | null;
};

export default function MiraklMapping({ channel }: MiraklMappingProps) {
  const snack = useSnack();
  const api = useApi();
  const { t } = useTranslation();

  const [channelWorkflowExports, setChannelWorkflowExports] = useState<ChannelWorkflowExportType[] | null>(null);
  const [isMappingsLoading, setIsMappingsLoading] = useState(true);
  const [selectedChannelWorkflowExport, setSelectedChannelWorkflowExport] = useState<null | ChannelWorkflowExportType>(null);

  const getChannelExportMappingPossibilities = async () => {
    setIsMappingsLoading(true);
    try {
      const data = (await api.get(`/api/channel-workflow-exports/${channel?.code}/possibilities`)) as { "hydra:member": ChannelWorkflowExportType[] };
      setChannelWorkflowExports(data["hydra:member"]);
    } catch (error) {
      console.log(error);
      snack.error("errors.loading");
    } finally {
      setIsMappingsLoading(false);
    }
  };

  useEffect(() => {
    if (!channelWorkflowExports) {
      getChannelExportMappingPossibilities();
      return;
    }

    if (selectedChannelWorkflowExport) {
      const updatedDefinition = channelWorkflowExports.find((e) => e.uuid === selectedChannelWorkflowExport.uuid);

      setSelectedChannelWorkflowExport(null);

      setTimeout(() => setSelectedChannelWorkflowExport(updatedDefinition ?? null), 200);
    }
  }, [channelWorkflowExports]);

  if (isMappingsLoading) return <LoaderPage />;

  if (!channelWorkflowExports) return null;

  return (
    <>
      <H2>{t("items.CatalogScope.attribute_mapping")}</H2>
      <Stack>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t("items.CatalogScope.type")}</TableCell>
                <TableCell>{t("items.CatalogScope.locale")}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {channelWorkflowExports.map((channelWorkflowExport) => (
                <TableRow key={channelWorkflowExport["@id"]} onClick={() => setSelectedChannelWorkflowExport(channelWorkflowExport)}>
                  <TableCell>{channelWorkflowExport.type}</TableCell>
                  <TableCell>{channelWorkflowExport.locale ?? "GLOBAL"}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Stack>

      {null === selectedChannelWorkflowExport ? <NoResultValue /> : <ChannelWorkflowExport channelWorkflowExport={selectedChannelWorkflowExport} onChange={getChannelExportMappingPossibilities} />}
    </>
  );
}
