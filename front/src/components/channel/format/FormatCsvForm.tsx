import { type ReactElement } from "react";
import { useTranslation } from "@/hooks/useTranslation";
import type { FormatProps } from "@/components/channel/format/Format";
import { FormControl, FormLabel, MenuItem, Select } from "@mui/material";
import ViewHeadlineIcon from "@mui/icons-material/ViewHeadline";
import { CsvSeparatorEnum } from "@/enums";

export default function FormatCsvForm({ format, onChange }: FormatProps): ReactElement {
  const { t } = useTranslation("components.channel.format");

  const DELIMITERS = [
    {
      label: "CSV (,)",
      parameters: { delimiter: CsvSeparatorEnum.COMMA },
      icon: <ViewHeadlineIcon sx={{ fontSize: 16 }} />,
    },
    {
      label: "CSV (;)",
      parameters: { delimiter: CsvSeparatorEnum.SEMICOLON },
      icon: <ViewHeadlineIcon sx={{ fontSize: 16 }} />,
    },
    {
      label: "CSV (|)",
      parameters: { delimiter: CsvSeparatorEnum.PIPE },
      icon: <ViewHeadlineIcon sx={{ fontSize: 16 }} />,
    },
    {
      label: "CSV (tab)",
      parameters: { delimiter: CsvSeparatorEnum.TAB },
      icon: <ViewHeadlineIcon sx={{ fontSize: 16 }} />,
    },
  ];

  return (
    <FormControl>
      <FormLabel>{t("select_separator")}</FormLabel>
      <Select
        value={format.parameters?.delimiter ?? ""}
        onChange={(e) => {
          onChange((prevState) => ({
            ...prevState,
            parameters: {
              ...prevState.parameters,
              delimiter: e.target.value,
            },
          }));
        }}
        defaultOpen={false}
      >
        {DELIMITERS.map((value, index) => (
          <MenuItem key={index} value={value.parameters.delimiter}>
            {value.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
}
