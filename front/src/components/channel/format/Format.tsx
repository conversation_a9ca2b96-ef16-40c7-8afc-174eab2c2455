import type { Dispatch, ReactElement, SetStateAction } from "react";
import SimpleTabs from "@/components/ui/tabs/SimpleTabs";
import { useTranslation } from "@/hooks/useTranslation";
import type { SimpleTab } from "@/components/ui/tabs/types";
import type { Format as FormatType } from "@/components/channel/format/types";
import { ChannelFormatEnum } from "@/enums/ChannelFormatEnum";
import FormatCsvForm from "@/components/channel/format/FormatCsvForm";
import Typography from "@/components/ui/Typography";

export type FormatProps = {
  format: FormatType;
  onChange: Dispatch<SetStateAction<FormatType>>;
};

export default function Format({ format, onChange }: FormatProps): ReactElement {
  const { t } = useTranslation("components.channel.format");

  const tabs: SimpleTab<ChannelFormatEnum>[] = [
    {
      key: ChannelFormatEnum.CSV,
      children: <FormatCsvForm format={format} onChange={onChange} />,
      label: `enums.CHANNEL_FORMAT_TYPES.${ChannelFormatEnum.CSV}`,
    },
    {
      key: ChannelFormatEnum.JSON,
      children: <Typography>{t("no_more_todo")}</Typography>,
      label: `enums.CHANNEL_FORMAT_TYPES.${ChannelFormatEnum.JSON}`,
    },
    {
      key: ChannelFormatEnum.XML,
      children: <Typography>{t("no_more_todo")}</Typography>,
      label: `enums.CHANNEL_FORMAT_TYPES.${ChannelFormatEnum.XML}`,
    },
  ];

  const setSelectedAdapterType = (key: ChannelFormatEnum) => {
    onChange((prev) => ({ ...prev, type: key, parameters: {} }));
  };

  return (
    <SimpleTabs tabs={tabs.filter((e) => format.definition?.types.includes(e.key))} onChange={(tab: SimpleTab<ChannelFormatEnum>) => setSelectedAdapterType(tab.key)} isActive={(tab) => format.type === tab.key}>
      <p>{t("choose_one")}</p>
    </SimpleTabs>
  );
}
