import type {ChannelScheduleTypeEnum} from "@/enums/ChannelScheduleTypeEnum";
import type {UiDefinition} from "@/types/definition/UiDefinition";

export type Schedule = {
  type: ChannelScheduleTypeEnum | null;
  parameters: {
    hours?: number[];
    minutes?: number[];
  };
  definition: null|ScheduleDefinition;
}

export type ScheduleDefinition = {
  ui: UiDefinition;
  sync: ScheduleSyncDefinition | null;
  types: ChannelScheduleTypeEnum[];
  max: number | null;
}

export type ScheduleSyncDefinition = {
  wait: string | null;
}
