import {useTranslation} from "@/hooks/useTranslation";
import {ChannelScheduleTypeEnum} from "@/enums/ChannelScheduleTypeEnum";
import type {Schedule} from "@/components/channel/schedule/types";
import type {SimpleTab} from "@/components/ui/tabs/types";
import ScheduleHoursForm from "@/components/channel/schedule/ScheduleHoursForm";
import ScheduleTenMinutes from "@/components/channel/schedule/ScheduleTenMinutes";
import SimpleTabs from "@/components/ui/tabs/SimpleTabs";
import Typography from "@/components/ui/Typography";
import type {Dispatch, SetStateAction} from "react";
import ScheduleCustom from "@/components/channel/schedule/ScheduleCustom";

type ScheduleFormProps = {
  schedule: Schedule;
  onChange: Dispatch<SetStateAction<Schedule>>;
}

export default function ScheduleForm({schedule, onChange}: ScheduleFormProps) {
  const {t} = useTranslation("components.channel.schedule");

  const tabs: SimpleTab<ChannelScheduleTypeEnum>[] = [{
    key: ChannelScheduleTypeEnum.ONE_HOUR,
    children: <ScheduleHoursForm schedule={schedule} onChange={onChange} />,
    label: `enums.CHANNEL_SCHEDULE_TYPES.${ChannelScheduleTypeEnum.ONE_HOUR}`,
  }, {
    key: ChannelScheduleTypeEnum.TEN_MINUTES,
    children: <ScheduleTenMinutes schedule={schedule} onChange={onChange} />,
    label: `enums.CHANNEL_SCHEDULE_TYPES.${ChannelScheduleTypeEnum.TEN_MINUTES}`,
  }, {
    key: ChannelScheduleTypeEnum.CUSTOM,
    children: <ScheduleCustom />,
    label: `enums.CHANNEL_SCHEDULE_TYPES.${ChannelScheduleTypeEnum.CUSTOM}`,
  }];

  const setSelectedType = (type: ChannelScheduleTypeEnum) => {
    onChange(prev => ({...prev, type, parameters: {}}))
  }

  return (
    <SimpleTabs
      tabs={tabs.filter(t => schedule.definition?.types.includes(t.key))}
      onChange={(tab: SimpleTab<ChannelScheduleTypeEnum>) => setSelectedType(tab.key)}
      isActive={(tab => schedule.type === tab.key)}
    >
      <Typography variant="body1">{t("choose_one")}</Typography>
    </SimpleTabs>
  );
}
