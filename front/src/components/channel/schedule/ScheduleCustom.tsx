import { useTranslation } from "@/hooks/useTranslation";
import { Button, MenuItem, Select, Stack, Typography, type SelectChangeEvent, Popover, Box, IconButton } from "@mui/material";
import { useState } from "react";
import AccessTimeIcon from "@mui/icons-material/AccessTime";

type Selections = {
  month: string[];
  week: string[];
  day: string[];
  hour: string[];
  minute: string[];
};

type TimeType = keyof Selections;

export default function ScheduleCustom() {
  const { t } = useTranslation("components.ScheduleCustom");

  const [currentType, setCurrentType] = useState<TimeType>("month");
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [selections, setSelections] = useState<Selections>({
    month: [],
    week: [],
    day: [],
    hour: [],
    minute: [],
  });

  const baseTimes = ["month", "week", "day", "hour"];

  const months = ["*", ...Array.from({ length: 12 }, (_, i) => i.toString())];
  const weeks = ["*", "0", "1", "2"];
  const days = ["*", "1", "2", "3", "4", "5", "6", "0"]; // sunday = 0
  const hours = ["*", ...Array.from({ length: 24 }, (_, i) => i.toString())];
  const minutes = ["0", "15", "30"];

  const handleReset = (): void => {
    setSelections({
      month: [],
      week: [],
      day: [],
      hour: [],
      minute: [],
    });
  };

  const handleTypeChange = (newType: TimeType): void => {
    setCurrentType(newType);
    handleReset();
  };

  const handleValueChange = (type: TimeType, newValue: string[]): void => {
    setSelections((prev) => ({
      ...prev,
      [type]: newValue,
    }));
  };

  const getAvailableSelects = (): TimeType[] => {
    const typeOrder: TimeType[] = ["month", "week", "day", "hour", "minute"];
    const currentIndex = typeOrder.indexOf(currentType);
    return typeOrder.slice(currentIndex);
  };

  const getOptionsForType = (type: TimeType): string[] => {
    switch (type) {
      case "month":
        return months;
      case "week":
        return weeks;
      case "day":
        return days;
      case "hour":
        return hours;
      case "minute":
        return minutes;
    }
  };

  const getCronExpression = (): string => {
    const minute = selections.minute.length ? selections.minute.join(",") : "*";
    const hour = selections.hour.length ? selections.hour.join(",") : "*";
    const day = selections.day.length ? selections.day.join(",") : "*";
    const month = selections.month.length ? selections.month.join(",") : "*";
    const week = selections.week.length ? selections.week.join(",") : "*";

    return `${minute} ${hour} ${day} ${month} ${week}`;
  };

  return (
    <Stack>
      <Stack direction="row" gap={1} alignItems="center">
        <Typography>{t("choose_recurrence")}</Typography>
      </Stack>
      <Stack direction="row" gap={1} alignItems="center">
        <Typography>{t("every")}</Typography>
        <Select value={currentType} onChange={(e: SelectChangeEvent<TimeType>) => handleTypeChange(e.target.value as TimeType)}>
          {baseTimes.map((time, index) => (
            <MenuItem key={`${time}-${index}`} value={time}>
              {t(`base_times.${time}`)}
            </MenuItem>
          ))}
        </Select>
        {getAvailableSelects().map((type, typeIndex) => (
          <Stack key={`${type}-${typeIndex}`} direction="row" gap={1} alignItems="center">
            <Typography>{type === currentType ? t("of") : t("every").toLowerCase()}</Typography>
            {type === "hour" ? (
              <>
                <Select multiple value={selections[type]} onChange={(e: SelectChangeEvent<string[]>) => handleValueChange(type, e.target.value as string[])} renderValue={(selected) => selected.join(", ")}>
                  {getOptionsForType(type).map((option, optionIndex) => (
                    <MenuItem key={`${option}-${optionIndex}`} value={option}>
                      {t(`options.${type}.${option}`)}
                    </MenuItem>
                  ))}
                </Select>
                <IconButton onClick={(e) => setAnchorEl(e.currentTarget)}>
                  <AccessTimeIcon />
                </IconButton>
                <Popover
                  open={Boolean(anchorEl)}
                  anchorEl={anchorEl}
                  onClose={() => setAnchorEl(null)}
                  anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "left",
                  }}
                >
                  <Box
                    sx={{
                      p: 2,
                      display: "grid",
                      gridTemplateColumns: "repeat(6, 1fr)",
                      gap: 0.5,
                    }}
                  >
                    <Button
                      size="small"
                      variant={selections.hour.includes("*") ? "contained" : "outlined"}
                      onClick={() => {
                        const newValue = selections.hour.includes("*") ? selections.hour.filter((h) => h !== "*") : ["*"];
                        handleValueChange("hour", newValue);
                      }}
                      sx={{ minWidth: 45, gridColumn: "span 6" }}
                    >
                      {t("options.hour.*")}
                    </Button>
                    {Array.from({ length: 24 }, (_, i) => i).map((hour) => (
                      <Button
                        key={hour}
                        size="small"
                        variant={selections.hour.includes(hour.toString()) ? "contained" : "outlined"}
                        onClick={() => {
                          const newValue = selections.hour.includes(hour.toString()) ? selections.hour.filter((h) => h !== hour.toString()) : [...selections.hour, hour.toString()];
                          handleValueChange("hour", newValue);
                        }}
                        sx={{ minWidth: 45 }}
                      >
                        {`${hour}h`}
                      </Button>
                    ))}
                  </Box>
                </Popover>
              </>
            ) : (
              <Select multiple value={selections[type]} onChange={(e: SelectChangeEvent<string[]>) => handleValueChange(type, e.target.value as string[])}>
                {getOptionsForType(type).map((option, optionIndex) => (
                  <MenuItem key={`${option}-${optionIndex}`} value={option}>
                    {t(`options.${type}.${option}`)}
                  </MenuItem>
                ))}
              </Select>
            )}
          </Stack>
        ))}
        <Button onClick={handleReset} variant="outlined">
          {t("reset")}
        </Button>
      </Stack>
      <Typography>
        {t("cron_expression")} {getCronExpression()}
      </Typography>
    </Stack>
  );
}
