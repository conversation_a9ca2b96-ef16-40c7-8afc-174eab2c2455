import type { ChannelWorkflowExport as ChannelWorkflowExportType } from "@/components/channel/channelWorkflowExport/types";
import { H3 } from "@/components/ui/Typography";
import { useTranslation } from "@/hooks/useTranslation";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import { type ReactElement, useState } from "react";
import ScheduleForm from "@/components/channel/schedule/ScheduleForm";
import type { Schedule } from "@/components/channel/schedule/types";
import { useApi } from "@/contexts/ApiProvider";
import Source from "@/components/channel/source/Source";
import type { Source as SourceType } from "@/components/channel/source/types";
import type { Format as FormatType } from "@/components/channel/format/types";
import Format from "@/components/channel/format/Format";
import { useSnack } from "@/contexts/SnackProvider";
import type {Mapping as MappingType} from "@/components/channel/mapping/types";
import FormChannelWorkflowExport from "@/components/forms/FormChannelWorkflowExport";

type ChannelWorkfloxExportProps = {
  channelWorkflowExport: ChannelWorkflowExportType;
  onChange(): void;
};
export default function ChannelWorkflowExport({ channelWorkflowExport, onChange }: ChannelWorkfloxExportProps) {
  const { t } = useTranslation();
  const api = useApi();
  const snack = useSnack();

  const [activeStep, setActiveStep] = useState<number>(0);
  const [schedule, setSchedule] = useState<Schedule>(channelWorkflowExport.schedule ?? ({} as Schedule));
  const [source, setSource] = useState<SourceType>(channelWorkflowExport.source ?? ({} as SourceType));
  const [format, setFormat] = useState<FormatType>(channelWorkflowExport.format ?? ({} as FormatType));
  const [mapping, setMapping] = useState<MappingType>(channelWorkflowExport.mapping ?? ({} as MappingType));

  const save = async () => {
    try {
      await api.put(channelWorkflowExport["@id"], {
        ...channelWorkflowExport,
        schedule: Object.keys(schedule).length === 0 ? null : schedule,
        source: Object.keys(source).length === 0 ? null : source,
        format: Object.keys(format).length === 0 ? null : format,
        mapping: Object.keys(mapping).length === 0 ? null : mapping,
      });
    } catch (e) {
      if (e && typeof e === 'object' && 'violations' in e) {
        (e.violations as Array<{message: string; propertyPath: string}>).forEach((err) => {
          snack.error(`${err.message} (${err.propertyPath})`);
        });
      } else {
        snack.error(t("errors.common"));
      }
    }

    onChange();
  };

  return (
    <>
      <Badge badgeContent={channelWorkflowExport.uiDefinition.isValid ? "ok" : "error"} color={channelWorkflowExport.uiDefinition.isValid ? "success" : "error"} sx={{ width: "fit-content" }}>
        <H3>{t(channelWorkflowExport.names)}</H3>
      </Badge>
      <Stack>
        <Stepper activeStep={activeStep}>
          <Step>
            <WorkflowStep setActiveStep={() => setActiveStep(0)} isEnabled={channelWorkflowExport.schedule?.definition?.ui?.isEnabled ?? false}>
              {t("items.ChannelSchedule._singular")}
            </WorkflowStep>
          </Step>
          <Step>
            <WorkflowStep setActiveStep={() => setActiveStep(1)} isEnabled={channelWorkflowExport.source?.definition?.ui?.isEnabled ?? false}>
              {t("items.ChannelSource._singular")}
            </WorkflowStep>
          </Step>
          <Step>
            <WorkflowStep setActiveStep={() => setActiveStep(2)} isEnabled={channelWorkflowExport.format?.definition?.ui?.isEnabled ?? false}>
              {t("items.ChannelFormat._singular")}
            </WorkflowStep>
          </Step>
          <Step>
            <WorkflowStep setActiveStep={() => setActiveStep(3)} isEnabled={channelWorkflowExport.mapping?.definition?.ui?.isEnabled ?? false}>
              {t("items.ChannelMapping._singular")}
            </WorkflowStep>
          </Step>
          <Step>
            <WorkflowStep setActiveStep={() => setActiveStep(4)} isEnabled={channelWorkflowExport.adapter?.definition?.ui?.isEnabled ?? false}>
              {t("items.ChannelAdapter._singular")}
            </WorkflowStep>
          </Step>
        </Stepper>
      </Stack>
      {0 === activeStep ? <ScheduleForm schedule={schedule} onChange={setSchedule} /> : null}
      {1 === activeStep ? <Source source={source} onChange={setSource} /> : null}
      {2 === activeStep ? <Format format={format} onChange={setFormat} /> : null}
      {3 === activeStep ? <FormChannelWorkflowExport item={mapping} source={source} format={format} onChange={setMapping} /> : null}
      {/* {4 === activeStep ? <p>adapter</p> : null} */}
      <Button onClick={save}>{t("actions.save")}</Button>
    </>
  );
}

function WorkflowStep({ isEnabled, children, setActiveStep }: { isEnabled: boolean; children: ReactElement; setActiveStep(): void }) {
  return (
    <StepLabel>
      <Button disabled={!isEnabled} onClick={() => setActiveStep()}>
        {children}
      </Button>
    </StepLabel>
  );
}
