import ITEM_TYPES from "@/enums/ITEM_TYPES";
import Mapping from "@/components/mapping/Mapping";

export default function FormChannelWorkflowExport({ item = {}, source, format, onChange }) {
  return (
    <Mapping
      variant={ITEM_TYPES.EXPORT}
      source={source}
      format={format}
      mapping={item}
      onChange={(mapping) => onChange(mapping)}
      errors={false}
      canAdd={false}
      canUpdatePosition={false}
    />
  );
}
