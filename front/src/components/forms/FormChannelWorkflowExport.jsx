import useForm from "@/hooks/useForm";
import FormWrapper from "../ui/form/FormWrapper";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { CircularProgress, Stack } from "@mui/material";
import FormTranslateField from "../ui/form/inputs/FormTranslateField";
import { FormLayoutTab } from "../ui/form/FormLayouts";
import ButtonSave from "../buttons/ButtonSave";
import FormatSelector from "./formExport/FormatSelector";
import AdapterSelector from "./formExport/AdapterSelector";
import CronScheduler from "./formExport/CronScheduler";
import RowLabel from "../ui/inputs/RowLabel";
import { useTranslation } from "@/hooks/useTranslation";
import FormTabs from "@/theme/components/FormTabs";
import { Suspense, useEffect, useState } from "react";
import StepPanel from "../ui/stepper/StepPanel";
import { useSnack } from "@/contexts/SnackProvider";
import Mapping from "@/components/mapping/Mapping.jsx";
import { useApi } from "@/contexts/ApiProvider";
import LoaderPage from "@/components/loaders/LoaderPage";

export default function FormChannelWorkflowExport({ item = {}, onSave, setIsDirty }) {
  const { t } = useTranslation();
  const snack = useSnack();
  const api = useApi();

  const [activeTab, setActiveTab] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  const fullForm = useForm({
    type: ITEM_TYPES.EXPORT,
    defaultValues: item,
    url: "/api/channel-workflow-exports",
    onSuccess: () => onSave(),
    onError: () => snack.error(t("common.error")),
    onDirty: (e) => setIsDirty(e),
  });

  const binaryUrl = api.image.binary(item?.["@id"]);
  const downloadUrl = api.image.download(item?.["@id"]);

  const getMappingDefinition = async () => {
    setIsLoading(true);
    try {
      const mappingData = await api.get(`/api/channel-workflow-exports/${item.channel.replace("/api/catalog-scopes/", "")}/mapping-definitions/${item.uuid}`);
      const existingMapping = fullForm.getField("mapping");
      fullForm.setField({
        ...mappingData,
        columns: mappingData.columns.map((column) => {
          const existingColumn = existingMapping?.columns.find(c => c.out.property === column.out.property);
          return {
            ...column,
            out: { ...column.out, disabled: true },
            in: existingColumn ? existingColumn.in : { type: "ExIgnoreMeAccessor" }
          };
        })
      }, "mapping");
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    getMappingDefinition();
  }, []);

  return (
    <FormWrapper form={fullForm}>
      <RowLabel label={item?.uuid} link={binaryUrl} copy={binaryUrl} download={downloadUrl} />
      <FormLayoutTab>
        <FormTabs
          tabs={[
            t("items.HubOrder.tabs.informations"),
            isLoading ? (
              <Stack key="loading" direction="row" gap={1} alignItems="center">
                <span>{t("items.Export.mapping")}</span>
                <CircularProgress size={12} color="secondary" />
              </Stack>
            ) : (
              `${t("items.Export.mapping")} (${fullForm.getField("mapping")?.columns.length ?? 0})`
            ),
          ]}
          activeTab={activeTab}
          onChange={(e, val) => setActiveTab(val)}
        />
        <StepPanel style={{ height: "100%" }} value={0} index={activeTab}>
          <Stack p={2}>
            <FormTranslateField required name="names" form={fullForm} />
            <FormatSelector variant={ITEM_TYPES.EXPORT} format={fullForm?.form?.format} onChange={(format) => fullForm.setField(format, "format")} />
            <AdapterSelector variant={ITEM_TYPES.EXPORT} adapter={fullForm?.form?.adapter} onChange={(adapter) => fullForm.setField(adapter, "adapter")} />
            <CronScheduler variant={ITEM_TYPES.EXPORT} schedule={fullForm?.form?.schedule} onChange={(schedule) => fullForm.setField(schedule, "schedule")} errors={fullForm.getErrorsOf("schedule.cron")} />
          </Stack>
        </StepPanel>
        <StepPanel style={{ height: "100%" }} value={1} index={activeTab}>
          {isLoading ? (
            <Stack p={2}>
              <LoaderPage />
            </Stack>
          ) : (
            <Suspense fallback={null}>
              <Mapping
                variant={ITEM_TYPES.EXPORT}
                source={fullForm.getField("source")}
                format={fullForm.getField("format")}
                mapping={fullForm?.form?.mapping}
                onChange={(mapping) => fullForm.setField(mapping, "mapping")}
                errors={fullForm.getErrorsOf("mapping.columns")}
                canAdd={false}
                canUpdatePosition={false}
              />
            </Suspense>
          )}
        </StepPanel>
      </FormLayoutTab>
      <Stack direction="row" justifyContent="flex-end">
        <ButtonSave isReadOnly={fullForm.isReadOnly} />
      </Stack>
    </FormWrapper>
  );
}
