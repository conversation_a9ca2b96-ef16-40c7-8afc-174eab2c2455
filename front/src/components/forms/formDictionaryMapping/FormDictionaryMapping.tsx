import ButtonSave from "@/components/buttons/ButtonSave";
import {InputText} from "@/components/filters/inputs/InputText";
import SelectDictionary from "@/components/selects/SelectDictionary";
import {FormLayout} from "@/components/ui/form/FormLayouts";
import FormWrapper from "@/components/ui/form/FormWrapper";
import InputsSkeleton from "@/components/ui/inputs/InputsSkeleton";
import {ApiTypeEnum} from "@/enums";
import useForm from "@/hooks/useForm";
import type {DictionaryMapping} from "@/resources";
import {Stack} from "@mui/material";
import {useTranslation} from "react-i18next";

type Props = {
  item: DictionaryMapping | null;
  onSuccess: () => void;
  onDirty?: (isDirty: boolean) => void;
};

export default function FormDictionaryMapping({item, onSuccess, onDirty}: Props) {
  const form = useForm({
    type: ApiTypeEnum.DICTIONARY_MAPPING,
    defaultValues: {...item, dictionary: item?.dictionary?.["@id"] || item?.dictionary},
    url: "/api/dictionary-mappings",
    onSuccess,
    onDirty,
  });

  const {t} = useTranslation();

  return (
    <FormWrapper form={form}>
      <FormLayout>
        <SelectDictionary getterKey="@id" onChange={(e) => form.setField(e, "dictionary")} defaultValue={form.defaultValues.dictionary} />
        <InputsSkeleton label={t("items.DictionaryMapping.from")} required error={form.getErrorsOf("from")[0] || null} fullWidth>
          <InputText autoFocus required defaultValue={form.defaultValues?.from || ""} {...form.register("from")} placeholder={t("items.DictionaryMapping.from")} />
        </InputsSkeleton>
        <InputsSkeleton label={t("items.DictionaryMapping.to")} required error={form.getErrorsOf("to")[0] || null} fullWidth>
          <InputText required defaultValue={form.defaultValues?.to || ""} {...form.register("to")} placeholder={t("items.DictionaryMapping.to")} />
        </InputsSkeleton>
      </FormLayout>
      <Stack direction="row" justifyContent="flex-end">
        <ButtonSave />
      </Stack>
    </FormWrapper>
  );
}
