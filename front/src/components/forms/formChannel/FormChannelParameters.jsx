import FormMagentoParameters from "../FormMagentoParameters";
import FormMiraklParameters from "@/components/forms/FormMiraklParameters";
import { ChannelTypeEnum } from "@/enums";

export default function FormChannelParameters({ fullForm }) {
  if (fullForm.form.type === ChannelTypeEnum.MAGENTO) {
    return <FormMagentoParameters fullForm={fullForm} withStoreCode={true} />;
  }

  if (fullForm.form.type === ChannelTypeEnum.MAGENTO_ORDERS) {
    return <FormMagentoParameters fullForm={fullForm} withStoreCode={false} />;
  }
  if (fullForm.form.type === ChannelTypeEnum.MIRAKL) {
    return <FormMiraklParameters fullForm={fullForm} />;
  }

  return null;
}
