import ButtonSave from "@/components/buttons/ButtonSave";
import DrawerDictionaryMapping from "@/components/drawers/DrawerDictionaryMapping";
import LightTableDictionaryMapping from "@/components/lightTable/LightTableDictionaryMapping";
import {FormLayoutTab} from "@/components/ui/form/FormLayouts";
import FormWrapper from "@/components/ui/form/FormWrapper";
import FormTranslateField from "@/components/ui/form/inputs/FormTranslateField";
import InputCode from "@/components/ui/inputs/InputCode";
import StepPanel from "@/components/ui/stepper/StepPanel";
import {ApiTypeEnum, DirectionEnum} from "@/enums";
import useForm from "@/hooks/useForm";
import {usePaginator} from "@/hooks/usePaginator";
import {useTranslation} from "@/hooks/useTranslation";
import type {Dictionary, DictionaryMapping} from "@/resources";
import FormTabs from "@/theme/components/FormTabs";
import ItemUtils from "@/utils/item.utils";
import {Add} from "@mui/icons-material";
import {But<PERSON>, Stack} from "@mui/material";
import {useState} from "react";

type Props = {
  dictionary: Dictionary | null;
  onSuccess: () => void;
  onDirty: (isDirty: boolean) => void;
};

type NewDictionaryMapping = Partial<DictionaryMapping>;

export default function FormDictionary({dictionary, onSuccess, onDirty}: Props) {
  const form = useForm({
    type: ApiTypeEnum.DICTIONARY,
    defaultValues: dictionary,
    url: "/api/dictionaries",
    onSuccess,
    onDirty,
  });
  const {t} = useTranslation();
  const [currentTab, setCurrentTab] = useState(0);
  const [openDictionaryMapping, setOpenDictionaryMapping] = useState<NewDictionaryMapping | Dictionary | null>(null);

  const paginatorDictionaryMappings = usePaginator(
    ApiTypeEnum.DICTIONARY_MAPPING,
    "/api/dictionary-mappings",
    {
      dictionary: dictionary?.["@id"],
    },
    {from: DirectionEnum.ASC},
  );

  const handleSuccessFormDictionaryMappings = () => {
    setOpenDictionaryMapping(null);
    paginatorDictionaryMappings.goto(1);
  };

  return (
    <>
      <FormWrapper form={form}>
        <FormLayoutTab>
          <FormTabs
            tabs={[t("items.Dictionary._singular"), t("items.DictionaryMapping._")]}
            activeTab={currentTab}
            disabled={dictionary?.["@id"] ? [] : [1]}
            onChange={(e, val: number) => {
              setCurrentTab(val);
            }}
          />
          <StepPanel value={0} index={currentTab}>
            <Stack p={2}>
              <InputCode form={form} />
              <FormTranslateField required form={form} name="names" />
            </Stack>
          </StepPanel>
          <StepPanel value={1} index={currentTab}>
            <Stack p={2}>
              <LightTableDictionaryMapping paginator={paginatorDictionaryMappings} open={setOpenDictionaryMapping} />
              <Stack justifyContent="center" alignItems="center">
                <Button
                  fullWidth
                  variant="text"
                  startIcon={<Add />}
                  onClick={() =>
                    setOpenDictionaryMapping({
                      ...ItemUtils.getDefault(ApiTypeEnum.DICTIONARY_MAPPING),
                      dictionary: dictionary?.["@id"],
                    })
                  }
                >
                  {t("items.DictionaryMapping._create")}
                </Button>
              </Stack>
            </Stack>
          </StepPanel>
        </FormLayoutTab>
        <Stack direction="row" justifyContent="flex-end">
          <ButtonSave isReadOnly={form.isReadOnly} />
        </Stack>
      </FormWrapper>
      <DrawerDictionaryMapping dictionaryMapping={openDictionaryMapping} onClose={() => setOpenDictionaryMapping(null)} onSuccess={handleSuccessFormDictionaryMappings} onDelete={handleSuccessFormDictionaryMappings} />
    </>
  );
}
