import FormWrapper from "@/components/ui/form/FormWrapper";
import { Input } from "@/components/ui/inputs/index";
import useForm from "@/hooks/useForm";
import { useTranslation } from "@/hooks/useTranslation";
import FormTabs from "@/theme/components/FormTabs";
import { Stack } from "@mui/material";
import { useEffect, useState } from "react";
import ButtonSave from "../buttons/ButtonSave";
import FilterForm from "../filters/FilterForm";
import { FormLayoutTab } from "../ui/form/FormLayouts";
import BuilderInputsAttributes from "../ui/inputBuilder/BuilderInputsAttributes";
import RowLabel from "../ui/inputs/RowLabel";
import { ApiTypeEnum } from "@/enums";
import StepPanel from "../ui/stepper/StepPanel";

export default function FormCategory({ attributesGroups, item, onSave, setIsDirty, openAttributeGroups, type }) {
  const { t } = useTranslation();

  const form = useForm({
    type: ApiTypeEnum.CATEGORY,
    defaultValues: item,
    url: "/api/categories",
    onSuccess: () => onSave(form.form),
    onDirty: (e) => setIsDirty(e),
  });

  const { register, setField, setForm, form: fullform } = form;

  const [activeTab, setActiveTab] = useState(0);
  const [filters, setFilters] = useState(fullform?.filters || []);

  useEffect(() => {
    setField(filters, "filters");
  }, [filters]);

  const handleChangeAttributeInput = (code, value) => {
    const data = fullform?.values?.[code] || [];
    const defaultValue = data.filter((e) => e.locale !== value.locale || e.scope !== value.scope);
    setForm((form) => ({
      ...form,
      values: {
        ...form.values,
        [code]: [...defaultValue, value],
      },
    }));
  };

  useEffect(() => {
    setField(attributesGroups, "attributeGroups");
  }, [attributesGroups]);

  return (
    <FormWrapper form={form}>
      <RowLabel label={item?.uuid} copy={item?.uuid} />
      <FormLayoutTab>
        <FormTabs
          tabs={[t("items.Catalog.main"), t("items.Catalog.filters")]}
          activeTab={activeTab}
          onChange={(e, val) => {
            setActiveTab(val);
          }}
        />
        <StepPanel value={0} index={activeTab}>
          <Stack p={2}>
            <Input required autoFocus autoComplete="off" fullWidth {...register("name", true)} />
            <BuilderInputsAttributes
              item={item}
              openAttributeGroups={openAttributeGroups}
              attributeGroups={attributesGroups}
              onChange={handleChangeAttributeInput}
              defaultValues={fullform?.values}
            />
          </Stack>
        </StepPanel>
        <StepPanel value={1} index={activeTab}>
          <Stack p={2}>
            <FilterForm type={type ?? ApiTypeEnum.CATEGORY} filters={filters} onChange={setFilters} />
          </Stack>
        </StepPanel>
      </FormLayoutTab>
      <Stack direction="row" justifyContent="flex-end">
        <ButtonSave isReadOnly={form.isReadOnly} />
      </Stack>
    </FormWrapper>
  );
}
