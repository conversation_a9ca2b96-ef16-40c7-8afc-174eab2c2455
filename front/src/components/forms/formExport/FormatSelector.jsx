import { Button, ButtonGroup } from "@mui/material";
import CHANNEL_FORMAT_TYPES from "@/enums/CHANNEL_FORMAT_TYPES";
import ViewHeadlineIcon from "@mui/icons-material/ViewHeadline";
import DataObjectIcon from "@mui/icons-material/DataObject";
import DividerLeft from "@/components/ui/dividers/DividerLeft";
import SettingsEthernetIcon from "@mui/icons-material/SettingsEthernet";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { useTranslation } from "@/hooks/useTranslation";
import { CsvSeparatorEnum } from "@/enums";

const FORMATS = [{
  label: "CSV (,)",
  type: CHANNEL_FORMAT_TYPES.CSV,
  parameters: { delimiter: CsvSeparatorEnum.COMMA },
  icon: <ViewHeadlineIcon sx={{ fontSize: 16 }} />,
  variants: [ITEM_TYPES.EXPORT, ITEM_TYPES.IMPORT],
}, {
  label: "CSV (;)",
  type: CHANNEL_FORMAT_TYPES.CSV,
  parameters: { delimiter: CsvSeparatorEnum.SEMICOLON },
  icon: <ViewHeadlineIcon sx={{ fontSize: 16 }} />,
  variants: [ITEM_TYPES.EXPORT, ITEM_TYPES.IMPORT],
}, {
  label: "CSV (|)",
  type: CHANNEL_FORMAT_TYPES.CSV,
  parameters: { delimiter: CsvSeparatorEnum.PIPE },
  icon: <ViewHeadlineIcon sx={{ fontSize: 16 }} />,
  variants: [ITEM_TYPES.EXPORT, ITEM_TYPES.IMPORT],
}, {
  label: "CSV (tab)",
  type: CHANNEL_FORMAT_TYPES.CSV,
  parameters: { delimiter: CsvSeparatorEnum.TAB },
  icon: <ViewHeadlineIcon sx={{ fontSize: 16 }} />,
  variants: [ITEM_TYPES.EXPORT, ITEM_TYPES.IMPORT],
}, {
  label: "JSON",
  type: CHANNEL_FORMAT_TYPES.JSON,
  parameters: {},
  icon: <DataObjectIcon sx={{ fontSize: 16 }} />,
  variants: [ITEM_TYPES.EXPORT, ITEM_TYPES.IMPORT],
}, {
  label: "XML",
  type: CHANNEL_FORMAT_TYPES.XML,
  parameters: {},
  icon: <SettingsEthernetIcon sx={{ fontSize: 16 }} />,
  variants: [ITEM_TYPES.EXPORT, ITEM_TYPES.IMPORT],
}];

export default function FormatSelector({ variant, format, onChange }) {
  const { t } = useTranslation();

  const isSelected = f => f.type === format?.type && f.parameters.delimiter === format?.parameters.delimiter;

  return (
    <>
      <DividerLeft label={t(`items.${variant}.format`)} />
      <ButtonGroup variant="outlined">
        {FORMATS.map((f, i) => (
          <Button
            key={i}
            onClick={() => onChange({ type: f.type, parameters: f.parameters })}
            variant={isSelected(f) ? "contained" : "outlined"}
            startIcon={f.icon}
            disabled={!f.variants.includes(variant)}
          >
            {f.label}
          </Button>
        ))}
      </ButtonGroup>
    </>
  );
}
