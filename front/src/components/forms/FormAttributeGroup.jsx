import InputCode from "@/components/ui/inputs/InputCode";
import { useSnack } from "@/contexts/SnackProvider";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import useForm from "@/hooks/useForm";
import { usePaginator } from "@/hooks/usePaginator";
import { useTranslation } from "@/hooks/useTranslation";
import FormTabs from "@/theme/components/FormTabs";
import ItemUtils from "@/utils/item.utils";
import { Add } from "@mui/icons-material";
import { Button, Stack } from "@mui/material";
import { useState } from "react";
import DrawerAttribute from "../drawers/DrawerAttribute";
import ButtonSave from "../buttons/ButtonSave";
import { FormLayoutTab } from "../ui/form/FormLayouts";
import FormWrapper from "../ui/form/FormWrapper";
import FormTranslateField from "../ui/form/inputs/FormTranslateField";
import Paginator from "../ui/pagination/Paginator";
import SelectDocumentType from "../selects/SelectDocumentType";
import BtnStatusSwitcherItem from "../ui/item/btns/BtnStatusSwitcherItem";
import { useUser } from "@/contexts/UserProvider";
import StepPanel from "../ui/stepper/StepPanel";
import LightTableDragAttribute from "../lightTable/LightTableDragAttribute";
import { DirectionEnum } from "@/enums";

export default function FormAttributeGroup({ item = {}, onSave, setIsDirty }) {
  const { t } = useTranslation();
  const { rights } = useUser();

  const form = useForm({
    type: ITEM_TYPES.ATTRIBUTE_GROUP,
    id: item?.["@type"],
    defaultValues: item,
    url: "/api/attribute-groups",
    onSuccess: () => onSave(),
    onDirty: (e) => setIsDirty(e),
  });

  const [activeTab, setActiveTab] = useState(0);

  const attributesPaginator = usePaginator(ITEM_TYPES.ATTRIBUTE, "/api/attributes", { attributeGroup: item?.["@id"] }, { position: DirectionEnum.ASC }, false);

  const snack = useSnack();

  const handleDragndrop = () => snack.success("items.Attribute._position_saved");

  const handleChangeTab = (event, newValue) => {
    setActiveTab(newValue);
  };

  const [openAttribute, setOpenAttribute] = useState(null);

  return (
    <FormWrapper form={form}>
      <FormLayoutTab>
        {item?.["@id"] ? <FormTabs tabs={[t("items.AttributeGroup._singular"), t("items.Attribute._")]} activeTab={activeTab} onChange={handleChangeTab} /> : null}
        <StepPanel value={0} index={activeTab}>
          <Stack p={2}>
            <InputCode form={form} />
            <FormTranslateField required form={form} name="names" />
            <SelectDocumentType getterKey="@id" onChange={(value) => form.setField(value, "type")} defaultValue={form.getField("type") || ""} />
            <BtnStatusSwitcherItem item={item} onChange={(val) => form.setField(val, "status")} disabled={!rights.isEditable(item)} />
          </Stack>
        </StepPanel>
        <StepPanel value={1} index={activeTab}>
          <Stack p={2}>
            {activeTab === 1 ? (
              <Paginator paginator={attributesPaginator}>
                <LightTableDragAttribute paginator={attributesPaginator} onSuccess={handleDragndrop} open={(item) => setOpenAttribute(item)} />
              </Paginator>
            ) : null}
            <Stack justifyContent="center" alignItems="center" p={2}>
              <Button
                fullWidth
                variant="text"
                startIcon={<Add />}
                onClick={() =>
                  setOpenAttribute({
                    ...ItemUtils.getDefault(ITEM_TYPES.ATTRIBUTE),
                    attributeGroup: item?.["@id"],
                    position: attributesPaginator.items.length,
                  })
                }
              >
                {t("items.Attribute._create")}
              </Button>
            </Stack>
          </Stack>
        </StepPanel>
        <DrawerAttribute
          item={openAttribute}
          onChange={() => {
            setOpenAttribute(null);
            void attributesPaginator.goto(1);
          }}
          onClose={() => setOpenAttribute(null)}
          onRefresh={() => void attributesPaginator.goto(1)}
        />
      </FormLayoutTab>
      {activeTab === 1 ? null : (
        <Stack direction="row" justifyContent="flex-end">
          <ButtonSave isReadOnly={form.isReadOnly} />
        </Stack>
      )}
    </FormWrapper>
  );
}
