import { MediaInput } from "@/components/ui/inputs";
import InputCode from "@/components/ui/inputs/InputCode";
import { useUser } from "@/contexts/UserProvider";
import useForm from "@/hooks/useForm";
import { useTranslation } from "@/hooks/useTranslation";
import FormTabs from "@/theme/components/FormTabs";
import { Stack } from "@mui/material";
import { useEffect, useState } from "react";
import SelectDocumentType from "../selects/SelectDocumentType";
import ButtonSave from "../buttons/ButtonSave";
import { FormLayoutTab } from "../ui/form/FormLayouts";
import BtnStatusSwitcherItem from "../ui/item/btns/BtnStatusSwitcherItem";
import FormWrapper from "../ui/form/FormWrapper";
import StepPanel from "../ui/stepper/StepPanel";
import FormTranslateField from "../ui/form/inputs/FormTranslateField";
import FilterForm from "../filters/FilterForm";
import { ApiTypeEnum } from "@/enums";

export default function FormCatalog({ item = {}, onSave, setIsDirty }) {
  const { rights } = useUser();

  if (!rights.canEditCatalogs()) {
    return;
  }

  const { t } = useTranslation();

  const fullForm = useForm({
    type: ApiTypeEnum.CATALOG,
    defaultValues: item,
    url: "/api/catalogs",
    onSuccess: () => onSave(),
    onDirty: (e) => setIsDirty(e),
  });

  const { register, form, setField, getField } = fullForm;

  const [filters, setFilters] = useState(form?.filters || []);
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    if (JSON.stringify(item.filters) !== JSON.stringify(filters) || filters.filters.length === 0) {
      setField(filters, "filters");
    }
  }, [filters]);

  return (
    <FormWrapper form={fullForm}>
      <FormLayoutTab>
        <FormTabs
          tabs={[t("items.Catalog.main"), t("items.Catalog.filters")]}
          activeTab={activeTab}
          onChange={(e, val) => {
            setActiveTab(val);
          }}
        />
        <StepPanel value={0} index={activeTab}>
          <Stack p={2}>
            <InputCode form={fullForm} />
            <FormTranslateField required form={fullForm} name="names" />
            <SelectDocumentType {...register("type")} getterKey="@id" onChange={(value) => setField(value, "type")} required defaultValue={getField("type") || ""} />
            <MediaInput selected={form.upload} formLabel={t("inputs.image")} handleUpload={(e) => setField(e, "upload")} accept="image/*" {...register("upload")} />
            <BtnStatusSwitcherItem item={item} onChange={(val) => setField(val, "status")} disabled={!rights.isEditable(item)} />
          </Stack>
        </StepPanel>
        <StepPanel value={1} index={activeTab} style={{ height: "100%", overflow: "auto", padding: 20 }}>
          <Stack>
            <FilterForm filters={filters} onChange={setFilters} type={item?.type} />
          </Stack>
        </StepPanel>
      </FormLayoutTab>
      <Stack direction="row" justifyContent="flex-end">
        <ButtonSave isReadOnly={fullForm.isReadOnly} />
      </Stack>
    </FormWrapper>
  );
}
