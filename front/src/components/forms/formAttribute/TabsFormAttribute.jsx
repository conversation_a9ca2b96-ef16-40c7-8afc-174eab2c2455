import DrawerAttributeOption from "@/components/drawers/DrawerAttributeOption";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { usePaginator } from "@/hooks/usePaginator";
import ItemUtils from "@/utils/item.utils";
import { Add } from "@mui/icons-material";
import { Button, Stack } from "@mui/material";
import { useState } from "react";
import { useTranslation } from "@/hooks/useTranslation";
import FormAttribute from "./FormAttribute";
import StepPanel from "@/components/ui/stepper/StepPanel";
import Paginator from "@/components/ui/pagination/Paginator";
import { FormLayoutTab } from "@/components/ui/form/FormLayouts";
import FormWrapper from "@/components/ui/form/FormWrapper";
import useForm from "@/hooks/useForm";
import { useAttributes } from "@/contexts/AttributesProvider";
import ButtonSave from "@/components/buttons/ButtonSave";
import LightTableAttributeOptions from "@/components/lightTable/LightTableAttributeOptions";
import FormTabs from "@/theme/components/FormTabs";
import { AttributeTypeEnum, DirectionEnum } from "@/enums";

export default function TabsFormAttribute({ item = {}, onSave, setIsDirty }) {
  const { t } = useTranslation();

  const attributeOptionsPaginator = usePaginator(
    ITEM_TYPES.ATTRIBUTE_OPTION,
    "/api/attribute-options",
    {
      attribute: item?.["@id"],
    },
    { code: DirectionEnum.ASC },
  );

  const [activeTab, setActiveTab] = useState(0);
  const [openAttributeOptions, setOpenAttributeOptions] = useState(false);

  const handleChangeTab = (event, newValue) => {
    setActiveTab(newValue);
  };

  const isTypeSelect = item?.type === AttributeTypeEnum.SELECT || item?.type === AttributeTypeEnum.MULTISELECT;

  const { fetchAttributes } = useAttributes();

  const handleSuccess = () => {
    onSave();
    fetchAttributes();
  };

  const fullForm = useForm({
    type: ITEM_TYPES.ATTRIBUTE,
    defaultValues: item,
    url: "/api/attributes",
    onSuccess: () => handleSuccess(),
    onDirty: (e) => setIsDirty(e),
  });

  return (
    <FormWrapper form={fullForm}>
      <FormLayoutTab>
        {item?.["@id"] && isTypeSelect ? <FormTabs tabs={[t("items.Attribute._singular"), t("items.AttributeOption._")]} activeTab={activeTab} onChange={handleChangeTab} /> : null}
        <StepPanel value={0} index={activeTab}>
          <FormAttribute item={item} fullForm={fullForm} />
        </StepPanel>
        <StepPanel value={1} index={activeTab}>
          <Stack p={2}>
            <Paginator paginator={attributeOptionsPaginator} forceToolbar>
              <LightTableAttributeOptions paginator={attributeOptionsPaginator} open={(item) => setOpenAttributeOptions(item)} />
            </Paginator>
            <Stack justifyContent="center" alignItems="center">
              <Button
                fullWidth
                variant="text"
                startIcon={<Add />}
                onClick={() =>
                  setOpenAttributeOptions({
                    ...ItemUtils.getDefault(ITEM_TYPES.ATTRIBUTE_OPTION),
                    attribute: item?.["@id"],
                  })
                }
              >
                {t("items.Attribute._create")}
              </Button>
            </Stack>
          </Stack>
        </StepPanel>
        <DrawerAttributeOption
          item={openAttributeOptions}
          onChange={() => {
            setOpenAttributeOptions(null);
            void attributeOptionsPaginator.goto(1);
          }}
          onClose={() => setOpenAttributeOptions(null)}
          onRefresh={() => void attributeOptionsPaginator.goto(1)}
        />
      </FormLayoutTab>
      {activeTab === 1 ? null : (
        <Stack direction="row" justifyContent="flex-end">
          <ButtonSave isReadOnly={fullForm.isReadOnly} />
        </Stack>
      )}
    </FormWrapper>
  );
}
