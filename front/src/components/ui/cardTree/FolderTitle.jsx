import { H4 } from "@/components/ui/Typography";
import { Box, useTheme } from "@mui/material";
import { Link, useLocation } from "react-router";

export default function FolderTitle({ title }) {
  const theme = useTheme();
  const location = useLocation();

  return (
    <Box
      display="flex"
      gap={2}
      px={2}
      py={1}
      justifyContent="space-between"
      alignItems="center"
      sx={{
        borderBottom: `1px solid ${theme.palette.border.main}`,
        cursor: "pointer",
        color: "inherit",
      }}
      component={Link}
      to={location.pathname}
    >
      <H4>{title}</H4>
    </Box>
  );
}
