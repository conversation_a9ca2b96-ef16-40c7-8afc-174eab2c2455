import { CarouselMedias } from "@/theme/components/Image";
import InputTextCollection from "./InputTextCollection";

const spread = "...";

export function AssetCollectionNormal({ value }) {
  return <CarouselMedias medias={value} width={200} />;
}

export function AssetCollectionLight({ value }) {
  if (value.length === 1) {
    return value.map((elem, index) => <span key={index}>{elem}</span>);
  }
  return (
    <ul style={{ margin: 0 }}>
      {value.map((elem, index) => (index < 3 ? index < 2 ? <li key={index}>{elem}</li> : <li key={index}>{spread}</li> : null))}
    </ul>
  );
}

export function AssetCollectionEditable({ defaultValue, isRequired, onChange, disabled }) {
  return <InputTextCollection defaultValue={defaultValue} required={isRequired} onChange={onChange} disabled={disabled} />;
}
