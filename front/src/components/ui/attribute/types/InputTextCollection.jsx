import { I<PERSON><PERSON><PERSON><PERSON>, Stack, useTheme } from "@mui/material";
import { useCallback, useRef, useState } from "react";
import { Input } from "@/components/ui/inputs";
import { useTranslation } from "@/hooks/useTranslation";
import { Add } from "@mui/icons-material";
import ItemDraggable from "./jsonOptions/referenceGroups/ItemDraggable";

export default function InputTextCollection({ defaultValue, onChange, required, disabled, placeholder = null }) {
  const theme = useTheme();
  const { t } = useTranslation();
  const inputRef = useRef(null);

  const [values, setValues] = useState(defaultValue ?? []);
  const [text, setText] = useState("");

  const handleAddItem = useCallback(
    (item) => {
      setValues((prev) => {
        const newValues = [...prev, item];
        onChange(newValues);
        return newValues;
      });
    },
    [values, setValues, onChange],
  );

  const handleDeleteItem = useCallback(
    (index) => {
      setValues((prev) => {
        const newValues = prev.filter((_, i) => i !== index);
        onChange(newValues);
        return newValues;
      });
    },
    [onChange],
  );

  const moveItem = useCallback(
    (dragIndex, hoverIndex) => {
      if (disabled) return;
      setValues((prev) => {
        const newValues = [...prev];
        const [draggedItem] = newValues.splice(dragIndex, 1);
        newValues.splice(hoverIndex, 0, draggedItem);
        onChange(newValues);
        return newValues;
      });
    },
    [onChange],
  );

  const handleAdd = () => {
    if (text.trim() === "") return;
    handleAddItem(text);
    setText("");
    inputRef.current.focus();
  };

  return (
    <Stack gap={1} pt={2} borderTop={`1px solid ${theme.palette.border.main}`} overflow="hidden">
      {values.length
        ? values.map((value, index) => (
          <ItemDraggable key={index} index={index} item={value} moveItem={moveItem} handleDeleteItem={handleDeleteItem} disabled={disabled} />
          ))
        : null}
      <Input
        inputRef={inputRef}
        fullWidth
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            e.preventDefault();
            handleAdd();
          }
        }}
        value={text}
        onChange={(e) => setText(e.target.value)}
        placeholder={placeholder ?? t("actions.add_reference")}
        required={values.length > 0 ? false : required}
        disabled={disabled}
        end={
          <IconButton size="small" onClick={handleAdd} disabled={disabled}>
            <Add fontSize="small" />
          </IconButton>
        }
      />
    </Stack>
  );
}
