import Ellipsis from "@/components/ui/Ellipsis";
import InputTextCollection from "./InputTextCollection";

const spread = "...";

export function TextCollectionNormal({ value }) {
  return (
    <ul
      style={{
        listStyle: "inside",
        overflow: "hidden",
      }}
    >
      {typeof value === "string" ? <li>{value}</li> : value.map((elem, index) => <Ellipsis key={index} component="li" text={elem} />)}
    </ul>
  );
}

export function TextCollectionLight({ value }) {
  return (
    <ul style={{ margin: 0, listStyle: "inside", overflow: "hidden" }}>
      {typeof value === "string" ? (
        <li>{value}</li>
      ) : (
        value.map((elem, index) =>
          index < 3 ? index < 2 ? <Ellipsis key={index} component="li" text={elem} /> : <li key={index}>{spread}</li> : null,
        )
      )}
    </ul>
  );
}

export function TextCollectionEditable({ defaultValue, isRequired, onChange, disabled }) {
  return <InputTextCollection defaultValue={defaultValue} required={isRequired} onChange={onChange} disabled={disabled} />;
}
