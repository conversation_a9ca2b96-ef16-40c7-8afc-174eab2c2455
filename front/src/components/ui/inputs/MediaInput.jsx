import { Box, Button, CircularProgress, FormControl, FormLabel, useTheme } from "@mui/material";
import { useCallback, useRef, useState } from "react";
import { useDropzone } from "react-dropzone";
import { Body1, Subtitle2 } from "@/components/ui/Typography";
import { useTranslation } from "@/hooks/useTranslation";
import { Add, Delete, Edit } from "@mui/icons-material";
import { useSettings } from "@/contexts/SettingsProvider";
import { useApi } from "@/contexts/ApiProvider";
import { ButtonError, ButtonPrimary } from "../Button";

export default function MediaInput({
  selected, // Media
  isLoading,
  accept,
  onAdd,
  onEdit,
  handleUpload,
  error,
  disabled,
  required,
  formControlProps,
  formLabel,
}) {
  const theme = useTheme();
  const { t } = useTranslation();
  const { settings } = useSettings();
  const api = useApi();

  const [errors, setErrors] = useState(null);
  const inputRef = useRef(null);

  const clickInput = () => inputRef?.current.click();

  const uploadImage = async (file) => {
    return new Promise((resolve, reject) => {
      if (file) {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onloadend = () => {
          const blob = new Blob([file], { name: file.name });

          const formData = new FormData();
          formData.append("file", blob, file.name);
          formData.append("type", file.type);
          formData.append("name", file.name);

          api
            .postFormData("/api/uploads", formData)
            .then((result) => {
              resolve(result);
            })
            .catch((e) => {
              reject(e);
            });
        };
      }
    });
  };

  const onChange = async (e) => {
    try {
      if (e?.[0]) {
        setErrors(null);
        const image = await uploadImage(e?.[0]);
        handleUpload(image["@id"], image);
      }
    } catch (error) {
      if (error?.response && error.response.violations && Array.isArray(error.response.violations)) {
        let flatErrors = [];

        error.response.violations.forEach((violation) => {
          flatErrors.push(violation.message);
        });
        setErrors(flatErrors);
      } else {
        setErrors([t("common.error")]);
      }
    }
  };

  const onDrop = useCallback((acceptedFiles) => {
    onChange(acceptedFiles);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
  });

  return (
    <fieldset disabled={disabled}>
      <FormControl error={!!error} disabled={disabled} required={required} {...formControlProps} fullWidth>
        <FormLabel>{formLabel}</FormLabel>
        {!selected ? (
          <Box
            {...getRootProps({
              onClick: (event) => event.stopPropagation(),
            })}
            color={disabled ? "text.disabled" : "primary"}
          >
            <Button
              color={disabled ? "inherit" : "primary"}
              variant="outlined"
              sx={{
                padding: theme.spacing(1),
                cursor: disabled ? "not-allowed" : "pointer",
                width: "100%",
                borderStyle: isDragActive ? "dashed !important" : "solid",
              }}
              onClick={onAdd || clickInput}
            >
              {isLoading ? (
                <CircularProgress />
              ) : (
                <>
                  <Add color={disabled ? "text.disabled" : "primary"} />
                  <Body1 color={disabled ? "text.disabled" : "primary"}>{t("items.Media._add")}</Body1>
                </>
              )}
            </Button>
          </Box>
        ) : (
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            {...getRootProps({
              onClick: (event) => event.stopPropagation(),
            })}
          >
            <Box
              sx={{
                height: "160px",
                width: "160px",
                background: `transparent url(${api.image.thumbnail(selected)}) no-repeat center`,
                backgroundSize: "cover",
                opacity: disabled ? 0.5 : 1,
              }}
            />
            <Box display="flex" gap={2}>
              <ButtonError variant="outlined" startIcon={<Delete />} onClick={() => handleUpload(null)} disabled={disabled}>
                {t("actions.delete")}
              </ButtonError>
              <ButtonPrimary variant="outlined" startIcon={<Edit />} onClick={onEdit || clickInput} disabled={disabled}>
                {t("actions.update")}
              </ButtonPrimary>
            </Box>
          </Box>
        )}
        {errors ? (
          <>
            {errors.map((error, idx) => (
              <Subtitle2 key={idx} style={{ color: theme.palette.error.main }}>
                {error}
              </Subtitle2>
            ))}
          </>
        ) : !selected ? (
          <Subtitle2 color={disabled ? "text.disabled" : "inherit"}>
            {`(max ${settings.server.uploadMaxFilesizeLabel})`}
          </Subtitle2>
        ) : null}
        <input type="file" hidden {...getInputProps()} ref={inputRef} accept={accept} />
      </FormControl>
    </fieldset>
  );
}
