import { useTranslation } from "@/hooks/useTranslation";
import InputTextCollection from "../../attribute/types/InputTextCollection";
import InputsSkeleton from "../InputsSkeleton";

export default function InputAllowedDomains({ defaultValue, onChange, type }) {
  const { getParametersTexts } = useTranslation();
  const { label, helper, placeholder } = getParametersTexts(type);

  return (
    <InputsSkeleton label={label} fullWidth helper={helper}>
      <InputTextCollection defaultValue={defaultValue} required={false} onChange={onChange} disabled={false} placeholder={placeholder} />
    </InputsSkeleton>
  );
}
