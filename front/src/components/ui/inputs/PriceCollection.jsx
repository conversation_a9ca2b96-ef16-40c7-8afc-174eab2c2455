import { Icon<PERSON><PERSON>on, InputAdornment, Stack } from "@mui/material";
import { useEffect, useState } from "react";
import Input from "./Input";
import SelectCurrency from "../../selects/SelectCurrency";
import { Add, Remove } from "@mui/icons-material";
import { useTranslation } from "@/hooks/useTranslation";

export default function PriceCollection({ defaultValue, onChange, required, disabled }) {
  const [value, setValue] = useState(defaultValue || [{ amount: "", currency: "" }]);
  const { t } = useTranslation();

  const add = () => {
    setValue([...value, { amount: "", currency: "" }]);
  };

  const remove = (index) => {
    const newValues = value.filter((e, i) => i !== index);
    setValue(newValues);
  };

  const changeValue = (index, field, val, field2, val2) => {
    if (field2) {
      const newValue = [...value];
      newValue[index][field] = val;
      newValue[index][field2] = val2;
      setValue(newValue);
    } else {
      const newValue = [...value];
      newValue[index][field] = val;
      setValue(newValue);
    }
  };

  useEffect(() => {
    const data = value.filter((e) => e.amount?.length && e.currency?.length);
    onChange(data);
  }, [value]);

  return (
    <Stack>
      {value.map((val, index) => {
        return (
          <Stack direction="row" key={index}>
            <Input
              value={val?.amount}
              required={required}
              type="number"
              InputProps={{
                inputProps: { min: 0, step: "0.01" },
                endAdornment: <InputAdornment position="end">{val?.symbol}</InputAdornment>,
              }}
              onChange={(e) => changeValue(index, "amount", e.target.value)}
              fullWidth
              placeholder={t("common.amount")}
              disabled={disabled}
            />
            <SelectCurrency
              getterKey="code"
              noLabel
              defaultValue={val?.currency}
              optionDisabled={(option) => (value.find((e) => e.currency === option?.code) ? "Value already set" : false)}
              onChange={(val) => {
                changeValue(index, "currency", val?.code, "symbol", val?.symbol);
              }}
              disabled={!!disabled}
            />
            {disabled ? null : index + 1 === value?.length ? (
              <IconButton onClick={add}>
                <Add />
              </IconButton>
            ) : (
              <IconButton onClick={() => remove(index)}>
                <Remove />
              </IconButton>
            )}
          </Stack>
        );
      })}
    </Stack>
  );
}
