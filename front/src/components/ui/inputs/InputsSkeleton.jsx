import { FormControl, FormHelperText, FormLabel } from "@mui/material";

export default function InputsSkeleton({ helper = null, error, label = null, disabled = false, required = false, fullWidth = false, children, isLoading = false }) {
  return (
    <FormControl error={!!error} disabled={disabled || isLoading} required={required} fullWidth={fullWidth}>
      {label ? <FormLabel> {label} </FormLabel> : null}
      {children}
      {helper && !error ? <FormHelperText>{helper}</FormHelperText> : null}
      {error ? <FormHelperText>{error}</FormHelperText> : null}
    </FormControl>
  );
}
