import { InputAdornment, Stack } from "@mui/material";
import Input from "./Input";
import SelectCurrency from "../../selects/SelectCurrency";
import { useTranslation } from "@/hooks/useTranslation";

export default function Price({ defaultValue, onChange, required, disabled }) {
  const { t } = useTranslation();

  const value = {
    ...{
      amount: null,
      currency: null,
      symbol: null,
    },
    ...(defaultValue ?? {}),
  };

  const handleChange = (o) => {
    onChange({ ...value, ...o });
  };

  return (
    <Stack direction="row">
      <Input
        value={value.amount}
        required={required}
        type="number"
        InputProps={{
          inputProps: { min: 0, step: "0.01" },
          endAdornment: <InputAdornment position="end">{value.symbol}</InputAdornment>,
        }}
        onChange={(e) => handleChange({ amount: +e.target.value })}
        fullWidth
        placeholder={t("common.amount")}
        disabled={disabled}
      />
      <SelectCurrency getterKey="code" noLabel defaultValue={value.currency} onChange={(e) => handleChange({ currency: e.code, symbol: e.symbol })} disabled={disabled} />
    </Stack>
  );
}
