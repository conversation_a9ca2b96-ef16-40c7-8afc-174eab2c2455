import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DateRangePicker as DateRangePickerMui } from "@mui/x-date-pickers-pro/DateRangePicker";
import { SingleInputDateRangeField } from "@mui/x-date-pickers-pro/SingleInputDateRangeField";
import { useTranslation } from "react-i18next";
import { useUser } from "@/contexts/UserProvider";
import dayjs from "@/libs/dayjs";

export default function DateRangePicker({ dateFrom, dateTo, resetValue = [null, null], onChange }) {
  const { t } = useTranslation();
  const { preferences } = useUser();

  const shortcutsItems = [{
    label: t("components.DateRangePicker.lastMonth"),
    getValue: () => [
      dayjs.from().subtract(1, "month").startOf("month"),
      dayjs.to().subtract(1, "month").endOf("month"),
    ],
  }, {
    label: t("components.DateRangePicker.lastWeek"),
    getValue: () => [
      dayjs.from().subtract(7, "day").startOf("isoWeek"),
      dayjs.to().subtract(7, "day").endOf("isoWeek"),
    ],
  }, {
    label: t("components.DateRangePicker.last7Days"),
    getValue: () => [
      dayjs.from().subtract(7, "day"),
      dayjs.to().set("hour", 23),
    ],
  }, {
    label: t("components.DateRangePicker.today"),
    getValue: () => [
      dayjs.from().startOf("day"),
      dayjs.to().endOf("day"),
    ],
  }, {
    label: t("components.DateRangePicker.thisWeek"),
    getValue: () => [
      dayjs.from().startOf("isoWeek"),
      dayjs.to(),
    ],
  }, {
    label: t("components.DateRangePicker.currentMonth"),
    getValue: () => [
      dayjs.from().startOf("month"),
      dayjs.to(),
    ],
  }, {
    label: t("components.DateRangePicker.thisYear"),
    getValue: () => [
      dayjs.from().startOf("year"),
      dayjs.to(),
    ],
  }, {
    label: t("common.reset"),
    getValue: () => resetValue,
  }];

  const adapterLocale = preferences.getLang().split("_")[0];

  const defaultDate = [
    dateFrom ? dayjs(dateFrom).asFrom() : null,
    dateTo ? dayjs(dateTo).asTo() : null,
  ];

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale={adapterLocale}>
      <DateRangePickerMui
        slots={{ field: SingleInputDateRangeField }}
        sx={{ width: "100%" }}
        slotProps={{
          field: {
            format: "DD MMM YY",
            placeholder: t("components.DateRangePicker.placeholder"),
          },
          shortcuts: {
            items: shortcutsItems,
            style: {
              borderRight: "1px solid #e0e0e0",
              maxHeight: "none",
            },
          },
        }}
        maxDate={dayjs()}
        calendars={1}
        format="DD/MM/YYYY"
        defaultValue={defaultDate}
        onChange={(newValue) => {
          if (2 !== newValue.length) {
            onChange(resetValue);
            return;
          }

          const from = newValue[0];
          const to = newValue[1];
          if (!from || !from.isValid() || !to || !to.isValid()) {
            onChange(resetValue);
            return;
          }

          onChange([
            from.asFrom().toISOString(),
            to.asTo().toISOString(),
          ]);
        }}
      />
    </LocalizationProvider>
  );
}
