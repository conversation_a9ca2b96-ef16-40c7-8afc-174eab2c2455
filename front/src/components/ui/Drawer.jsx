import ErrorBoundary from "@/components/ui/ErrorBoundary";
import { Drawer as DrawerMui, Stack, styled } from "@mui/material";
import { ButtonPrimary } from "./Button";
import { useTranslation } from "@/hooks/useTranslation";
import { ArrowBack } from "@mui/icons-material";

const DrawerCustom = styled(DrawerMui, {
  shouldForwardProp: (prop) => prop !== "width",
})(({ width }) => ({
  left: "unset !important",
  right: "0 !important",

  "& > .MuiDrawer-paper": {
    zIndex: "1300 !important",
    display: "flex",
    flexDirection: "column",
    width: width || "797px",
    "@media screen and (max-width: 765px)": {
      width: "100vw",
    },
    "@media screen and (max-width: 1280px)": {
      paddingLeft: "40px",
      paddingRight: "40px",
    },
    padding: "20px",
    paddingLeft: "40px",
    paddingRight: "40px",
    boxShadow: "none",
    marginTop: 0,
  },
}));

export default function Drawer({ open, onClose, width, children }) {
  const { t } = useTranslation();

  return (
    <ErrorBoundary>
      <DrawerCustom open={open} onClose={onClose} anchor="right" width={width}>
        <Stack direction="row">
          <ButtonPrimary variant="text" onClick={onClose} startIcon={<ArrowBack />}>
            <strong>{t("actions.close")}</strong>
          </ButtonPrimary>
        </Stack>
        {children}
      </DrawerCustom>
    </ErrorBoundary>
  );
}
