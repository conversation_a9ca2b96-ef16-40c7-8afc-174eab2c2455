import { Body1, H6 } from "@/components/ui/Typography";
import { Paper, Stack } from "@mui/material";
import DashboardPieChart from "./DashboardPieChart";
import DashboardStat from "./DashboardStat";
import { useNavigate } from "react-router";
import { generateLink } from "../../dashboards/DashboardOptimization";

export default function DashboardCard({ title, data, count, params, hasOnClick = false, hasHoverMain = true, isSmall = false, helper = "" }) {
  const navigate = useNavigate();

  return (
    <Stack
      component={Paper}
      px={isSmall ? 3 : 4}
      py={isSmall ? 2 : 3}
      gap={isSmall ? 2 : 4}
      sx={{
        "&:hover": {
          backgroundColor: hasOnClick && hasHoverMain ? "primary.lighter" : "",
        },
      }}
    >
      <H6>{title}</H6>
      <Stack display="grid" gridTemplateColumns="150px 1fr" alignItems="flex-start" gap={isSmall ? 0 : 5}>
        <Stack>
          <DashboardPieChart data={data} count={count} isSmall={isSmall} />
        </Stack>
        <Stack flexGrow={1} display="grid" gridTemplateColumns="repeat(2, 1fr)" height="auto">
          {data.map((item) => (
            <DashboardStat
              key={item.name}
              item={item}
              onClick={
                hasOnClick && item.name !== "valid"
                  ? (e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      navigate(generateLink({ ...params, type: item.name }));
                    }
                  : null
              }
            />
          ))}
        </Stack>
      </Stack>
      <Body1 color="textSecondary">{helper}</Body1>
    </Stack>
  );
}
