import { useSettings } from "@/contexts/SettingsProvider";
import { useTasks } from "@/contexts/TasksProvider";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { usePaginator } from "@/hooks/usePaginator";
import { useTranslation } from "@/hooks/useTranslation";
import { Image, ImagesValues } from "@/theme/components/Image";
import { Body1, H6, Subtitle1, Subtitle2 } from "@/components/ui/Typography";
import theme from "@/theme/theme";
import ArrayUtils from "@/utils/array.utils";
import ItemUtils from "@/utils/item.utils";
import { CloudUpload } from "@mui/icons-material";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import ArticleIcon from "@mui/icons-material/Article";
import FolderIcon from "@mui/icons-material/Folder";
import { Box, CircularProgress, Divider, IconButton, List, ListItemButton, ListItemIcon, ListItemText, Pagination, PaginationItem, Stack } from "@mui/material";
import { useEffect, useState } from "react";
import { useDropzone } from "react-dropzone";
import BreadcrumbsPicker from "../BreadcrumbsPicker";
import InputQ from "../filters/InputQ";
import IconButtonAddFolder from "../iconButtons/IconButtonAddFolder";
import Tooltip from "@/components/ui/Tooltip";
import { useAttributes } from "@/contexts/AttributesProvider";
import { useUser } from "@/contexts/UserProvider";
import Light from "../attribute/Light";
import SelectProductFile from "../../selects/SelectProductFile";
import { useApi } from "@/contexts/ApiProvider";
import useDidMountEffect from "@/hooks/useDidMountEffect";
import TASK_TYPES from "@/enums/TASK_TYPES";
import { DirectionEnum } from "@/enums";

export default function TreeExplorer({ type, treeType, selection }) {
  const { t } = useTranslation();
  const { settings } = useSettings();
  const { user, preferences } = useUser();
  const api = useApi();
  const { attributes, getValueData } = useAttributes();

  const [current, setCurrent] = useState(null);
  const [all, setAll] = useState([]);
  const [search, setSearch] = useState(null);

  const [loading, setLoading] = useState(false);

  const [items, setItems] = useState([]);

  const paginator = usePaginator(
    type,
    ItemUtils.getIri(type),
    { ...(type !== ITEM_TYPES.PRODUCT ? { "order[name]": DirectionEnum.ASC } : {}) },
    {},
    type === ITEM_TYPES.PRODUCT || type === ITEM_TYPES.MEDIA || type === ITEM_TYPES.CONTENT,
    !current
      ? {
          "exists[folder]": false,
        }
      : { folder: current.id },
    null,
  );

  useEffect(() => {
    if (search !== null) {
      submitSearch();
    }
  }, [search]);

  useEffect(() => {
    if (type !== ITEM_TYPES.PRODUCT) {
      if (paginator?.page !== 1) {
        setItems((prev) => [...prev, ...paginator.items]);
      }
      setItems(paginator.items);
    } else if (type === ITEM_TYPES.PRODUCT && Object.keys(paginator.filters).find((key) => key.startsWith("sku["))) {
      setItems(paginator.items);
    } else {
      setItems((prev) => [...prev, ...paginator.items]);
    }
    setLoading(false);
  }, [paginator.items]);

  const treePaginator =
    type !== ITEM_TYPES.PRODUCT
      ? usePaginator(
          treeType,
          ItemUtils.getIri(treeType),
          { "order[name]": DirectionEnum.ASC },
          {},
          false,
          !current
            ? {
                "exists[parent]": false,
              }
            : {
                parent: current.id,
              },
          null,
        )
      : null;

  const submitSearch = () => {
    if (current) {
      void paginator.fetch({ folder: current.id, q: search });
      void treePaginator?.fetch({ parent: current.id, name: search });
    } else {
      void paginator.fetch({
        "exists[folder]": false,
        q: search,
      });
      void treePaginator?.fetch({ "exists[parent]": false, name: search });
    }
  };

  useDidMountEffect(() => {
    if (current) {
      void treePaginator?.fetch({
        parent: current.id,
      });
      void paginator.fetch({ folder: current.id });
    } else {
      void treePaginator?.fetch({
        "exists[parent]": false,
      });
      void paginator.fetch({
        "exists[folder]": false,
      });
    }
  }, [current]);

  useEffect(() => {
    if (treePaginator) {
      setAll((prev) => {
        return ArrayUtils.deduplicate([...prev, ...treePaginator.items]);
      });
    }
  }, [treePaginator?.items]);

  //drag n drop
  const tasks = useTasks();
  const onDrop = (acceptedFiles) => {
    tasks.addTasks(TASK_TYPES.MEDIA, {
      files: acceptedFiles,
      folder: current,
      onSuccess: (res) => {
        res.length > 0 ? selection.add(res.map((e) => e.data)) : null;
        void paginator.goto(1);
        void treePaginator?.goto(1);
      },
    });
  };
  const { getRootProps, getInputProps, isDragActive } = useDropzone({ onDrop, noClick: true, noKeyboard: true });

  const handleFolderClick = (item) => {
    paginator.goto(1);
    setCurrent(item);
  };

  const mapSkus = (array) => {
    const obj = {};
    array.forEach((sku, index) => {
      obj[`sku[${index}]`] = sku;
    });
    return obj;
  };

  return (
    <Stack overflow="auto" width="100%" position="relative" {...(type === ITEM_TYPES.MEDIA ? getRootProps() : null)}>
      {isDragActive && type === ITEM_TYPES.MEDIA ? (
        <>
          <input {...getInputProps()} />
          <Stack
            gap={0}
            sx={{
              backgroundColor: `${theme.palette.primary.light}40`,
              position: "absolute",
              height: "100%",
              width: "100%",
              zIndex: 9999,
              top: 0,
              cursor: "grabbing",
              border: `1px dashed ${theme.palette.primary.main}`,
              borderRadius: "5px",
              backdropFilter: "blur(2px)",
              WebkitBackdropFilter: "blur(2px)",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CloudUpload color="primary" style={{ fontSize: "72px" }} />
            <Body1 color="primary">{t("actions.drop_files")}</Body1>
            <Body1 color="primary">{`(max ${settings.server.uploadMaxFilesizeLabel})`}</Body1>
          </Stack>
        </>
      ) : null}
      <Stack display="grid" gridTemplateRows="max-content max-content max-content 1fr max-content" gap={0} overflow="hidden" height={1}>
        <Stack
          component="form"
          direction="row"
          pt={0.5}
          width="100%"
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
        >
          {type === ITEM_TYPES.PRODUCT ? (
            <SelectProductFile
              getterKey="sku"
              noLabel
              multiple
              defaultValue={[]}
              onChange={(val) => {
                setLoading(true);
                if (val.length) {
                  paginator.fetch(
                    {
                      ...mapSkus(val),
                    },
                    null,
                    1,
                  );
                } else {
                  setItems([]);
                  void paginator.fetch({}, null, 1);
                }
              }}
            />
          ) : (
            <InputQ isLoading={paginator.isLoading || treePaginator?.isLoading} value={search} onChange={(value) => setSearch(value)} />
          )}
        </Stack>
        <Stack flexDirection="row" gap={1} py={1} px={2} alignItems="center">
          {type !== ITEM_TYPES.PRODUCT ? (
            current ? (
              <>
                <IconButton size="small" onClick={() => setCurrent(current.parent ? all.find((e) => e["@id"] === current.parent) : null)}>
                  <ArrowBackIosIcon fontSize="small" style={{ color: theme.palette.black }} />
                </IconButton>
                <H6>{current?.name}</H6>
              </>
            ) : (
              <Stack width={1} direction="row" justifyContent="space-between" alignItems="center">
                <H6>{settings.client.name}</H6>
                <IconButtonAddFolder
                  type={ITEM_TYPES.MEDIA_FOLDER}
                  parent={current}
                  onSuccess={() => {
                    void paginator.goto(1);
                    void treePaginator?.goto(1);
                  }}
                />
              </Stack>
            )
          ) : null}
        </Stack>
        <Divider />
        {paginator.isLoading || treePaginator?.isLoading || loading ? (
          <Stack alignItems="center" justifyContent="center" py={2}>
            <CircularProgress />
          </Stack>
        ) : !treePaginator?.items.length && !items.length ? (
          <Stack py={1} px={3}>
            <Subtitle1>{t("common.no_result")}</Subtitle1>
          </Stack>
        ) : (
          <Stack width="100%" height="100%" overflow="auto" alignItems="center">
            <List sx={{ width: "100%", height: "100%", overflow: "auto" }} dense>
              {paginator.page === 1
                ? treePaginator?.items.map((item, i) => (
                  <ListItemButton key={item.uuid + `${i}`} role="listitem" onDoubleClick={() => handleFolderClick(item)}>
                    <ListItemIcon>
                      <FolderIcon style={{ fontSize: "20px", color: theme.palette.black }} />
                    </ListItemIcon>
                    <ListItemText primary={item.name} />
                    <ListItemIcon>
                      <IconButton size="small" onClick={() => handleFolderClick(item)}>
                        <ArrowForwardIosIcon style={{ fontSize: "20px", color: theme.palette.black }} />
                      </IconButton>
                    </ListItemIcon>
                  </ListItemButton>
                  ))
                : null}
              {items.map((item, i) => (
                <ListItemButton key={`${item.uuid}${i}`} role="listitem" onClick={() => selection.handleSelect(item)} selected={selection.isSelected(item)}>
                  <ListItemIcon>
                    {type === ITEM_TYPES.MEDIA ? (
                      <Tooltip light title={<Image src={api.image.thumbnail(item["@id"])} width="250px" />} placement="top">
                        <Image src={api.image.thumbnail(item["@id"])} width="40px" />
                      </Tooltip>
                    ) : type === ITEM_TYPES.PRODUCT ? (
                      <Tooltip light title={<ImagesValues values={item.values} width="250px" />} placement="top">
                        {/* DIV HERE IS NEEDED TO FIX A BUG WITH TOOLTIP */}
                        <div>
                          <ImagesValues values={item.values} width="50px" />
                        </div>
                      </Tooltip>
                    ) : (
                      <ArticleIcon style={{ fontSize: "20px", color: theme.palette.primary.main }} />
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      item.name ? (
                        item.name
                      ) : getValueData(item.values, settings.ui[type.toLowerCase()].card.attribute2, preferences.getLang()) ? (
                        <Light
                          type={attributes?.find((e) => e.code === settings.ui[type.toLowerCase()].card.attribute2)?.type}
                          attribute={settings.ui[type.toLowerCase()].card.attribute2}
                          value={getValueData(item.values, settings.ui[type.toLowerCase()].card.attribute2, preferences.getLang(), user.scope)}
                          locale={preferences.getLang()}
                        />
                      ) : (
                        item.sku
                      )
                    }
                    secondary={getValueData(item.values, settings.ui[type.toLowerCase()].card.attribute2, preferences.getLang()) ? <Subtitle2 color="text.disabled">{item.sku}</Subtitle2> : null}
                  />
                </ListItemButton>
              ))}
            </List>
            {paginator.pageCount > 1 ? (
              <Pagination
                size="small"
                boundaryCount={1}
                siblingCount={0}
                count={paginator.pageCount}
                page={paginator.page}
                onChange={(_, page) => {
                  paginator.goto(page);
                }}
                renderItem={(item) => (
                  <PaginationItem
                    {...item}
                    // accessibility
                    tabIndex={item.page + 20}
                  />
                )}
              />
            ) : null}
          </Stack>
        )}
        {type !== ITEM_TYPES.PRODUCT ? (
          <Box pt={1}>
            <BreadcrumbsPicker current={current} setCurrent={setCurrent} all={all} />
          </Box>
        ) : null}
      </Stack>
    </Stack>
  );
}
