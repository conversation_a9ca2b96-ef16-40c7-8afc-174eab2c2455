import { FormLabel, Stack } from "@mui/material";
import { useTranslation } from "@/hooks/useTranslation";
import Translate<PERSON>ield from "@/components/ui/inputs/TranslateField";

export default function FormTranslateField({ form, label = null, name, required, helper = null, disabled = false, multiline = false, rows = 1, autoFocus = false }) {
  const { t } = useTranslation();

  const handleDestroyField = (lang) => {
    if (form) {
      form.setForm((prev) => {
        const newForm = { ...prev };
        try {
          delete newForm[name][lang];
        } catch (e) {
          console.log(e);
        }
        return newForm;
      });
    }
  };

  return (
    <Stack gap={0}>
      <FormLabel disabled={disabled}>
        {label || t(`items.${form.type}.${name}`)} {required ? "*" : null}
      </FormLabel>
      <TranslateField
        value={form.getField(name)}
        onChange={(e, lang) => form.setField({ ...form?.form?.names, [lang]: e }, "names")}
        error={form.getErrorsOf("names")?.[0]}
        required={required}
        helper={helper}
        disabled={disabled}
        multiline={multiline}
        rows={rows}
        autoFocus={autoFocus}
        onDeleteLang={handleDestroyField}
      />
    </Stack>
  );
}
