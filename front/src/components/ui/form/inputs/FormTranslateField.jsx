import { Add, Remove } from "@mui/icons-material";
import { Box, FormLabel, InputAdornment, MenuItem, Stack } from "@mui/material";
import { useEffect, useState } from "react";
import { useTranslation } from "@/hooks/useTranslation";
import { Input } from "@/components/ui/inputs";
import { useSettings } from "@/contexts/SettingsProvider";
import IconButton from "../../IconButton";
import ButtonMenu from "@/components/buttons/ButtonMenu";

export default function FormTranslateField({ form, label = null, name, required, value = null, error = null, helper = null, disabled = false, multiline = false, rows = 1, autoFocus = false }) {
  const { langs, defaultLang } = useSettings();
  const { register } = form;

  const { t, getHelper } = useTranslation();

  const [fields, setFields] = useState(Object.keys(form?.form?.[name] || {})?.length > 0 ? Object.keys(form?.form?.[name]) : [langs[0]]);
  const [localesOptions, setLocalesOptions] = useState(langs.filter((e) => !fields.includes(e)) || []);

  useEffect(() => {
    setLocalesOptions(langs?.filter((e) => !fields.includes(e)) || []);
  }, [fields]);

  const canDeleteLang = (lang) => lang !== defaultLang;

  const handleDestroyField = (lang) => {
    if (form) {
      setFields((prev) => prev.filter((e) => e !== lang));

      form.setForm((prev) => {
        const newForm = { ...prev };
        try {
          delete newForm[name][lang];
        } catch (e) {
          console.log(e);
        }
        return newForm;
      });
    }
  };

  const handleAddField = (e) => {
    localesOptions.length > 0 ? setFields((prev) => [...prev, e]) : null;
  };

  const handleChange = (e, lang, field) => {
    field.onChange(e);
  };

  return (
    <Stack gap={0}>
      <FormLabel disabled={disabled}>
        {label || t(`items.${form.type}.${name}`)} {required ? "*" : null}
      </FormLabel>
      <Stack gap={1}>
        {fields
          .sort((a, b) => langs.indexOf(a) - langs.indexOf(b))
          .filter((lang) => langs.includes(lang))
          .map((lang, i) => {
            const field = register(`${name}.${lang}`, true);

            return (
              <Box key={i}>
                <Input
                  autoFocus={autoFocus}
                  onChange={(e) => handleChange(e, lang, field)}
                  value={value || field.value}
                  helper={helper || getHelper(form.type, "names", disabled || field.disabled)}
                  error={error || field.errors[0]}
                  disabled={disabled || field.disabled}
                  fullWidth
                  multiline={multiline}
                  rows={rows}
                  InputProps={{
                    startAdornment: langs.length > 1 ? <InputAdornment position="start">{lang}</InputAdornment> : null,
                    endAdornment: !canDeleteLang(lang) ? (
                      localesOptions.length > 0 ? (
                        <InputAdornment position="end">
                          <ButtonMenu
                            button={
                              <IconButton size="small" disabled={field.disabled}>
                                <Add fontSize="small" />
                              </IconButton>
                            }
                          >
                            {localesOptions.map((e) => (
                              <MenuItem key={e} onClick={() => handleAddField(e)}>
                                {e}
                              </MenuItem>
                            ))}
                          </ButtonMenu>
                        </InputAdornment>
                      ) : null
                    ) : (
                      <IconButton onClick={() => handleDestroyField(lang)} disabled={disabled || field.disabled}>
                        <Remove fontSize="small" />
                      </IconButton>
                    ),
                  }}
                />
              </Box>
            );
          })}
      </Stack>
    </Stack>
  );
}
