import { Stack, styled } from "@mui/material";
import FormLoader from "./FormLoader";

export const Form = styled(Stack)(() => ({
  overflow: "hidden",
  height: "auto",
  width: "100%",
}));

export const Fieldset = styled("fieldset")(({ theme }) => ({
  // fieldset elements have some special styling and behavior that can interfere with layout properties like overflow
  // this is a workaround to remove those styles
  all: "unset",
  display: "flex",
  flexDirection: "column",
  gap: theme.spacing(2),
  height: "100%",
  width: "100%",
}));

export default function FormWrapper({ form, children }) {
  const { submit, isLoading, id } = form;

  return (
    <Form component="form" id={id} onSubmit={submit}>
      <FormLoader isLoading={isLoading} />
      <Fieldset>
        {children}
      </Fieldset>
    </Form>
  );
}
