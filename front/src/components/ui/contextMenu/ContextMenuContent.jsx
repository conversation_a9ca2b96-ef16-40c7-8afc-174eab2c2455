import ContextMenu from "@/components/ui/contextMenu/ContextMenu";
import { useTranslation } from "@/hooks/useTranslation";

export default function ContextMenuContent({ contextMenu, setContextMenu, handleUpdate, handleDelete }) {
  const { t } = useTranslation();
  return (
    <ContextMenu
      contextMenu={contextMenu}
      setContextMenu={setContextMenu}
      items={[
        { name: t("actions.update"), onClick: (item) => handleUpdate(item) },
        { name: t("actions.delete"), onClick: (item) => handleDelete(item) },
      ]}
    />
  );
}
