import ContextMenu from "@/components/ui/contextMenu/ContextMenu";
import { useTranslation } from "@/hooks/useTranslation";

export default function ContextMenuMedia({ contextMenu, setContextMenu, handleUpdate, handleDelete, handleUnzip = null }) {
  const { t } = useTranslation();
  return (
    <ContextMenu
      contextMenu={contextMenu}
      setContextMenu={setContextMenu}
      items={[
        ...[
          { name: t("actions.update"), onClick: (item) => handleUpdate(item) },
          { name: t("actions.delete"), onClick: (item) => handleDelete(item) },
        ],
        ...(handleUnzip ? ([{ name: t("actions.unzip"), onClick: (item) => handleUnzip(item) }]) : ([])),
      ]}
    />
  );
}
