import { useApi } from "@/contexts/ApiProvider";
import { Avatar } from "@mui/material";

export default function AvatarUser({ user, width = 40, height = 40 }) {
  const api = useApi();

  return (
    <Avatar
      alt={`${user?.lastname} ${user?.firstname}`}
      src={user?.upload ? api.image.thumbnail(user?.upload) : null}
      sx={{ width, height, fontSize: width / 2 }}
    >
      {user?.upload ? null : user?.lastname?.charAt(0).toUpperCase() + user?.firstname?.charAt(0).toUpperCase()}
    </Avatar>
  );
}
