import { Tab, Tabs as MuiTabs } from "@mui/material";
import styled from "@emotion/styled";

export const StyledTabs = styled(MuiTabs)(({ theme }) => ({
  "& .MuiTabs-list": {
    borderBottom: `1px solid ${theme.palette.border.main}`,
  },
  "& .MuiTabs-indicator": {
    display: "none",
  },
  "& .MuiTab-icon": {
    margin: "0px !important",
  },
}));

export const StyledTab = styled(Tab)(({ theme }) => ({
  height: "40px",
  padding: "0 15px",
  minHeight: "unset",
  fontWeight: "bold",
  color: "black",
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  gap: "7px",
  "& svg": {
    width: "20px",
    height: "20px",
    marginBottom: "0px !important",
  },
  border: `1px solid transparent`,
  borderBottom: `none`,
  "&.Mui-selected": {
    borderTopLeftRadius: theme.shape.borderRadius,
    borderTopRightRadius: theme.shape.borderRadius,
    backgroundColor: "#ffffff",
    border: `1px solid ${theme.palette.border.main}`,
    borderBottom: `none`,
    overflow: "visible",
    "& svg": {
      fill: theme.palette.primary.main,
    },
    "&::after": {
      content: "\"\"",
      width: "100%",
      height: "1px",
      backgroundColor: "#ffffff",
      position: "absolute",
      bottom: "-1px",
    },
  },
}));

export const StyledSubTabs = styled(MuiTabs)(({ theme }) => ({
  backgroundColor: "#ffffff",
  "& .MuiTabs-list": {
    border: `1px solid ${theme.palette.border.main}`,
    borderTop: "none",
  },
}));

export const StyledSubTab = styled(Tab)(({ theme }) => ({
  minHeight: "36px",
  color: "#000000",
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  gap: "5px",
  "& svg": {
    width: "20px",
    height: "20px",
    marginBottom: "0px !important",
  },
  "&.Mui-selected": {
    backgroundColor: "#ffffff",
    fontWeight: "normal",
    textShadow: "currentColor 0px 0px 1px",
    "& svg": {
      fill: theme.palette.primary.main,
    },
  },
}));
