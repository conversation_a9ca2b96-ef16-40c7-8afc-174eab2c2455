import { useEffect, useState } from "react";
import { Stack } from "@mui/material";
import theme from "@/theme/theme";
import { StyledSubTab, StyledSubTabs, StyledTab, StyledTabs } from "./Tabs.styled";

export default function Tabs({ tabs, value = null, onTabChange, onSubTabChange = null, defaultTab = 0, defaultSubTab = 0, children = null, sx = {} }) {
  const [tabValue, setTabValue] = useState(defaultTab);
  const [subTabValue, setSubTabValue] = useState(defaultSubTab);
  const [height, setHeight] = useState(0);
  const [heightSub, setHeightSub] = useState(0);

  if (0 === tabs.length) {
    return null;
  }

  const handleTabChange = (_, newValue) => {
    setTabValue(newValue);
    onTabChange ? onTabChange(tabs[newValue], newValue) : null;
  };

  const handleSubTabChange = (_, newValue) => {
    setSubTabValue(newValue);
    onSubTabChange ? onSubTabChange(tabs[tabValue].children[newValue]) : null;
  };

  useEffect(() => {
    if (value) {
      setTabValue(value);
    }
  }, [value]);

  return (
    <Stack gap={0} height="100%" overflow="hidden" sx={sx}>
      <StyledTabs value={tabValue} onChange={handleTabChange} ref={(ref) => setHeight(ref?.clientHeight)}>
        {tabs.map((tab, index) => (
          <StyledTab key={index} label={tab.label} icon={tab.icon} />
        ))}
      </StyledTabs>
      {tabs?.[tabValue]?.children?.length > 1 ? (
        <StyledSubTabs value={subTabValue} onChange={handleSubTabChange} ref={(ref) => setHeightSub(ref?.clientHeight)}>
          {tabs?.[tabValue]?.children?.map((tab, index) => (
            <StyledSubTab key={index} label={tab.label} icon={tab.icon} />
          ))}
        </StyledSubTabs>
      ) : null}
      <Stack bgcolor="background.paper" border={`1px solid ${theme.palette.border.main}`} borderTop="none" p={2} height={`calc(100% - ${height}px - ${heightSub}px)`} overflow="auto">
        {children}
        {tabs?.[tabValue]?.children?.length > 1 ? tabs?.[tabValue]?.children?.[subTabValue]?.component : tabs?.[tabValue]?.component}
      </Stack>
    </Stack>
  );
}
