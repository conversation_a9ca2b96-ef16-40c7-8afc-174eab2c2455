import { useLocales } from "@/contexts/LocalesProvider";
import LAYOUTS from "@/enums/LAYOUTS";
import { GridView, Menu } from "@mui/icons-material";
import { Box, InputLabel, MenuItem, Stack, ToggleButton, ToggleButtonGroup } from "@mui/material";
import Pagination from "@mui/material/Pagination";
import { useTranslation } from "@/hooks/useTranslation";
import { SelectInput } from "../inputs";
import LoaderPage from "../../loaders/LoaderPage";
import PaginatorEmpty from "@/components/ui/pagination/PaginatorEmpty";
import SelectLocalePaginator from "../inputs/SelectLocalePaginator";
import { useUser } from "@/contexts/UserProvider";
import SelectScopePaginator from "../inputs/SelectScopePaginator";

export default function Paginator({ paginator, children, onCreate, localizable = false, scopable = false, forceToolbar = false }) {
  const { t } = useTranslation();
  const { locales } = useLocales();
  const { preferences } = useUser();

  if (!paginator) {
    return null;
  }

  const handleChangeLimit = (e) => {
    paginator.setLimit(e.target.value);
    preferences.setLimit(e.target.value);
  };

  const handleChangeView = (e, newValue) => {
    if (newValue) {
      paginator.setView(newValue);
      preferences.setLayout(paginator.type, newValue);
    }
  };

  return (
    <Stack height="inherit" overflow="hidden" gap={!paginator.view && 1 <= paginator.pageCount && !localizable && !scopable ? 0 : 2}>
      <Box display="flex" alignItems="center" gap={2} flexWrap="wrap">
        {paginator.view ? (
          <ToggleButtonGroup exclusive value={paginator.view} onChange={handleChangeView} size="small">
            <ToggleButton value={LAYOUTS.LIST}>
              <Menu fontSize="small" />
            </ToggleButton>
            <ToggleButton value={LAYOUTS.CARD}>
              <GridView fontSize="small" />
            </ToggleButton>
          </ToggleButtonGroup>
        ) : null}
        {paginator.pagination && (paginator.view ? paginator.view !== LAYOUTS.LIST : forceToolbar ?? paginator.view) ? (
          <SelectInput value={paginator.limit} onChange={handleChangeLimit}>
            {[12, 24, 36, 48, 60, 72].map((option) => (
              <MenuItem value={option} key={`view-${option}`}>
                {option}
              </MenuItem>
            ))}
          </SelectInput>
        ) : null}
        {paginator.pagination && 0 < paginator.count && 1 < paginator.pageCount && (paginator.view ? paginator.view !== LAYOUTS.LIST : forceToolbar ?? paginator.view) ? (
          <Pagination showFirstButton showLastButton count={paginator.pageCount} page={paginator.page} onChange={(e, page) => paginator.goto(page)} />
        ) : null}
        {localizable || scopable ? (
          <Stack flexGrow={1} direction="row" alignItems="center" justifyContent="flex-end">
            {localizable && locales.length ? (
              <>
                <InputLabel>
                  {t("items.Locale._singular")}
                  {t("common.points")}
                </InputLabel>
                <SelectLocalePaginator />
              </>
            ) : null}
            {scopable ? (
              <>
                <InputLabel>
                  {t("items.Scope._singular")}
                  {t("common.points")}
                </InputLabel>
                <Stack width={250}>
                  <SelectScopePaginator getterKey="code" fullWidth={false} noLabel />
                </Stack>
              </>
            ) : null}
          </Stack>
        ) : null}
      </Box>
      {paginator.isStarting ? (
        <LoaderPage />
      ) : 0 < paginator.count ? (
        <Stack flexGrow={1} overflow="auto">
          {children}
        </Stack>
      ) : (
        <PaginatorEmpty type={paginator.type} onCreate={onCreate} />
      )}
    </Stack>
  );
}
