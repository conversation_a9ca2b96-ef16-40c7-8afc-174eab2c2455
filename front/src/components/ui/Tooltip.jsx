import { default as MuiTooltip, tooltipClasses } from "@mui/material/Tooltip";
import styled from "@emotion/styled";

const LightTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.white,
    color: "rgba(0, 0, 0, 0.87)",
    boxShadow: theme.shadows[1],
    fontSize: 11,
    border: `1px solid ${theme.palette.black}`,
    height: "100%",
  },
}));

export default function Tooltip({ children, light = false, ...props }) {
  if (light) {
    return (
      <LightTooltip arrow={false} {...props}>{children}</LightTooltip>
    );
  }

  return (
    <MuiTooltip arrow {...props}>{children}</MuiTooltip>
  );
}
