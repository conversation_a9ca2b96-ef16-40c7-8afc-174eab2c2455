import { TableCell, TableSortLabel } from "@mui/material";
import PaginationUtils from "@/utils/pagination.utils";
import { useTranslation } from "@/hooks/useTranslation";
import { DirectionEnum } from "@/enums";

export default function TableCellPaginatorSort({ paginator, field, align = "left", padding = "normal", sx = null, children }) {
  const { t } = useTranslation();

  return (
    <TableCell key={field} align={align} sortDirection={paginator.order[field] ?? false} sx={sx} padding={padding}>
      <TableSortLabel
        active={Object.prototype.hasOwnProperty.call(paginator.order, field)}
        direction={paginator.order[field] ?? DirectionEnum.ASC}
        onClick={() => paginator.orderBy(field, PaginationUtils.toggle(paginator.order[field] || DirectionEnum.DESC))}
      >
        <span
          style={{
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {children ? children : t(`items.${paginator.type}.${field}`)}
        </span>
      </TableSortLabel>
    </TableCell>
  );
}
