import { useUser } from "@/contexts/UserProvider";
import { NO_RESULT_LABEL } from "@/components/ui/NoResultValue";

export default function Price({ price, currency }) {
  const { preferences } = useUser();

  if (null === price) {
    return NO_RESULT_LABEL;
  }

  const lang = preferences.getLang().replace("_", "-");

  const params = {};

  if (currency) {
    params.style = "currency";
    params.currency = currency;
  }

  return new Intl.NumberFormat(lang, params).format(price);
}
