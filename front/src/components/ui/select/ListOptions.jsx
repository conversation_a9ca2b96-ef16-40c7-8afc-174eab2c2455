import Popover from "@mui/material/Popover";
import { Box, Checkbox, Divider, MenuItem, Pagination, PaginationItem, Stack, useTheme } from "@mui/material";
import { useEffect, useRef, useState } from "react";
import { Input } from "../inputs";
import { useTranslation } from "@/hooks/useTranslation";
import Tooltip from "@/components/ui/Tooltip";
import { Body1 } from "@/components/ui/Typography";

export default function ListOptions({ anchorEl, handleClose, buttonWidth, handleChange, multiple, renderOption, select, noSearch, optionDisabled, permOptions = [], reset, borderBetweenOptions = false }) {
  const { t } = useTranslation();
  const theme = useTheme();
  const { options, values, handleSearch, loading, page, setPage, pageCount, fetch, startLoading, setSearch } = select;

  const popoverRef = useRef(null);
  const optionsRef = useRef([]);

  const [isAttached, setIsAttached] = useState(true);
  // eslint-disable-next-line react/hook-use-state
  const [, setFocusedIndex] = useState(-1);

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  useEffect(() => {
    // Logic to determine if the popover is attached and needs a borderTop or not ex. Form Rule
    setTimeout(() => {
      if (anchorEl && popoverRef.current) {
        const popoverRect = popoverRef.current.getBoundingClientRect();
        const anchorRect = anchorEl.getBoundingClientRect();

        const isAttachedVertically = popoverRect.top - anchorRect.bottom > -150;

        setIsAttached(isAttachedVertically);
      } else {
        setSearch(null);
        reset();
      }
    }, 0);
  }, [open, anchorEl, popoverRef.current]);

  // Accessibilty
  const optionsLength = [...permOptions, ...options].length;

  const handleKeyDown = (e) => {
    if (optionsLength) {
      if (e.key === "ArrowDown") {
        setFocusedIndex((prev) => {
          const nextIndex = prev === optionsLength - 1 ? 0 : Math.min(prev + 1, optionsLength - 1);
          optionsRef.current[nextIndex]?.focus();
          return nextIndex;
        });
      } else if (e.key === "ArrowUp") {
        setFocusedIndex((prev) => {
          const previousIndex = prev === 0 ? optionsLength - 1 : Math.max(prev - 1, 0);
          optionsRef.current[previousIndex]?.focus();
          return previousIndex;
        });
      }
    }
  };

  const isSelected = (option) => {
    return values.some((value) => value["@id"] === option["@id"]);
  };

  return (
    <Popover
      id={id}
      ref={popoverRef}
      open={open}
      anchorEl={anchorEl}
      onClose={handleClose}
      onKeyDown={handleKeyDown}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "left",
      }}
      aria-hidden={false}
      slotProps={{
        paper: {
          // ref: popoverRef,
          square: true,
          sx: {
            borderTop: isAttached ? "none" : `1px solid ${theme.palette.border.main}`,
            minWidth: buttonWidth + 1, // Total width + 1 for border
            width: "min-content",
            maxWidth: 500,
            borderBottomRightRadius: theme.shape.borderRadius,
            borderBottomLeftRadius: theme.shape.borderRadius,
          },
        },
      }}
    >
      <Stack gap={1} p={1} alignItems="center">
        {noSearch ? null : (
          <Input
            autoFocus
            type="search"
            placeholder={t("actions.search")}
            name="search"
            onChange={(e) => {
              setSearch(e?.target?.value);
              handleSearch(e);
            }}
            isLoading={loading}
            fullWidth
          />
        )}
        <Stack gap={0} width="100%" alignItems="center" role="listbox" aria-multiselectable={multiple} aria-label="Options list">
          {options.length && !startLoading ? (
            <Box component="ul" sx={{ width: "100%", p: 0, m: 0 }}>
              {[...permOptions, ...options]?.map((option, index) => {
                const isDisabled = optionDisabled ? optionDisabled(option) : false;

                const menuItem = (
                  <MenuItem
                    ref={(el) => (optionsRef.current[index] = el)}
                    role="option"
                    aria-selected={values.some((v) => v["@id"] === option["@id"])}
                    aria-disabled={isDisabled}
                    tabIndex={index}
                    sx={{
                      px: 1,
                      display: "flex",
                      gap: 0,
                      width: "100%",
                      borderTop: borderBetweenOptions && index > 0 && index < options.length ? `1px solid ${theme.palette.border.main}` : "none",
                    }}
                    key={`${option["@id"]}-${index}`}
                    value={option}
                    selected={isSelected(option)}
                    onClick={() => {
                      handleChange(option);
                    }}
                    // To prevent console error expect boolean instead of string
                    disabled={optionDisabled ? !!optionDisabled(option) : false}
                  >
                    {multiple ? <Checkbox sx={{ p: 0 }} size="small" checked={values.some((value) => value["@id"] === option["@id"])} /> : null}
                    {renderOption(option, isSelected(option))}
                  </MenuItem>
                );

                return isDisabled ? (
                  <Tooltip title={optionDisabled(option)} key={`tooltip-${option["@id"]}-${index}`}>
                    <Box width="100%">{menuItem}</Box>
                  </Tooltip>
                ) : (
                  menuItem
                );
              })}
            </Box>
          ) : loading || startLoading ? (
            <Body1 color="text.secondary" py={2}>
              {`${t("actions.loading")}...`}
            </Body1>
          ) : (
            <Body1 color="text.secondary" py={2}>
              {t("common.no_result")}
            </Body1>
          )}
        </Stack>
        {pageCount > 1 && options?.length ? (
          <>
            {borderBetweenOptions ? (
              <Divider
                flexItem
                sx={{
                  width: "100%",
                  borderColor: theme.palette.border.main,
                }}
              />
            ) : null}
            <Pagination
              size="small"
              sx={{
                "& .MuiPagination-ul": {
                  flexWrap: "nowrap",
                },
              }}
              boundaryCount={1}
              siblingCount={1}
              count={pageCount}
              page={page}
              onChange={(_, page) => {
                setPage(page);
                fetch(page);
              }}
              renderItem={(item) => (
                <PaginationItem
                  {...item}
                  // accessibility
                  tabIndex={item.page + 20}
                />
              )}
            />
          </>
        ) : null}
      </Stack>
    </Popover>
  );
}
