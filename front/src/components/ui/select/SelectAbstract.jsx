import { Box, IconButton, Stack, useTheme } from "@mui/material";
import { Fragment, useEffect, useRef, useState } from "react";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { Body1 } from "@/components/ui/Typography";
import CloseIcon from "@mui/icons-material/Close";
import ListOptions from "./ListOptions";
import { Sync } from "@mui/icons-material";
import { useTranslation } from "@/hooks/useTranslation";
import Ellipsis from "@/components/ui/Ellipsis";
import Tooltip from "@/components/ui/Tooltip";

export default function SelectAbstract({
  required = false,
  disabled = false,
  onChange,
  onInit,
  placeholder,
  renderOption,
  renderSingleItem,
  renderMultipleItem,
  permOptions = [],
  select,
  multiple,
  noSearch = false,
  optionDisabled = false,
  isDragActive = false,
  endAdornment = null,
  limit = 4,
  borderBetweenOptions = false,
  sx = {},
}) {
  const theme = useTheme();
  const { t } = useTranslation();
  const { values, getterKey } = select;

  const [anchorEl, setAnchorEl] = useState(null);
  const [buttonWidth, setButtonWidth] = useState(0);

  const buttonRef = useRef(null);

  const handleClick = (event) => {
    if (select.options.length <= select.defaultOptions.length) {
      select.setStartLoading(true);
    }
    select.fetch();

    setAnchorEl(event.currentTarget);
    if (buttonRef.current) {
      setButtonWidth(buttonRef.current.offsetWidth);
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
    select.setPage(1);
  };

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  const comparison = (a, b) => {
    if (a[getterKey] && a[getterKey] === b[getterKey]) {
      return true;
    }

    if (a.code && a.code === b.code) {
      return true;
    }

    return a === b;
  };

  const different = (a, b) => {
    if (a[getterKey] && a[getterKey] !== b[getterKey]) {
      return true;
    }

    if (a.code && a.code !== b.code) {
      return true;
    }

    return a !== b;
  };

  const handleChange = (newValue) => {
    if (multiple) {
      const newValues = values.find((value) => value?.[getterKey] === newValue?.[getterKey]) ? values.filter((value) => value?.[getterKey] !== newValue?.[getterKey]) : [...new Set([...values, newValue])];
      select.setValues(newValues);
      select.setDefaultValues(newValues);
    } else {
      select.setValues([newValue]);
      select.setDefaultValues([newValue]);
      handleClose();
    }

    onChange(multiple ? (values.some((value) => comparison(value, newValue)) ? values.filter((value) => different(value, newValue)) : [...values, newValue]) : newValue);
  };

  const handleClear = (e) => {
    e.stopPropagation();
    select.setValues([]);
    select.handleSearch("");
    onChange(multiple ? [] : null);
  };

  useEffect(() => {
    if (values?.length) {
      onInit?.(multiple ? values : values?.[0]);
    }
  }, [values]);

  return (
    <Stack width="100%" position="relative" height={38}>
      <Stack
        gap={0.5}
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        ref={buttonRef}
        aria-describedby={id}
        variant="text"
        color="inherit"
        borderRadius={theme.shape.borderRadius}
        onClick={(e) => (disabled ? {} : handleClick(e))}
        sx={(theme) => ({
          pl: 1,
          pr: 1.25,
          py: 0.7,
          border: `1px ${isDragActive ? "dashed" : "solid"} ${theme.palette.border.main}`,
          backgroundColor: theme.palette.white,
          cursor: disabled ? "default" : "pointer",
          overflow: "hidden",
          maxHeight: 38,
          zIndex: 1,
          "&:hover": { backgroundColor: theme.palette.white },
          ...sx,
        })}
      >
        {select?.startLoading ? <Sync sx={(theme) => ({ color: theme.palette.text.disabled })} /> : null}
        {values.length ? (
          multiple ? (
            <Tooltip
              disableHoverListener={values.length <= limit}
              title={
                <ul style={{ paddingLeft: 15 }}>
                  {values.slice(limit, values.length).map((value, index) => (
                    <li key={index}>{value?.sku || t(value?.names) || value}</li>
                  ))}
                </ul>
              }
            >
              <Stack direction="row" whiteSpace="nowrap" width="100%" gap={1} height={38} alignItems="center" sx={{ overflowY: "hidden", overflowX: "auto" }}>
                {values?.map((value, index) => (index > limit - 1 ? null : <Fragment key={index}> {renderMultipleItem(value, () => handleChange(value))}</Fragment>))}
                {limit && values.length > limit ? `+${values.slice(limit, values.length).length} ${t("common.more")}...` : null}
              </Stack>
            </Tooltip>
          ) : (
            <Stack direction="row" width="100%" gap={1} overflow="hidden" height={38} alignItems="center">
              {values.map((value, index) => (
                <Stack key={`${value["@id"]}-${index}`} direction="row" justifyContent="space-between" alignItems="center" gap={1} width="100%" overflow="hidden">
                  <Body1
                    sx={{
                      color: disabled ? "text.disabled" : "inherit",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                      pl: 0.5,
                    }}
                  >
                    {renderSingleItem(value)}
                  </Body1>
                  <IconButton data-testid="clear-button" size="small" onClick={handleClear} disabled={disabled} sx={{ p: 0.5, color: disabled ? "text.disabled" : "" }}>
                    <CloseIcon sx={{ fontSize: 16 }} />
                  </IconButton>
                </Stack>
              ))}
            </Stack>
          )
        ) : (
          <Stack width="100%">
            <Box pl={0.5}>
              <span data-testid="select-placeholder">
                <Ellipsis text={placeholder ?? ""} color="text.disabled" pl={0.5} />
              </span>
            </Box>
          </Stack>
        )}
        <Stack direction="row" alignItems="center" gap={0.5}>
          <IconButton size="small" disableTouchRipple>
            <KeyboardArrowDownIcon sx={{ transform: open ? "rotate(180deg)" : "", color: disabled ? "text.disabled" : "inherit" }} />
          </IconButton>
          {endAdornment}
        </Stack>
      </Stack>
      <ListOptions
        anchorEl={anchorEl}
        handleClose={handleClose}
        buttonWidth={buttonWidth}
        handleChange={handleChange}
        multiple={multiple}
        renderOption={renderOption}
        permOptions={permOptions}
        select={select}
        noSearch={noSearch}
        optionDisabled={optionDisabled}
        reset={select.reset}
        borderBetweenOptions={borderBetweenOptions}
      />
      <input
        style={{
          position: "absolute",
          opacity: 0,
          height: "100%",
          width: "100%",
          zIndex: 0,
        }}
        type="text"
        value={values}
        required={required}
        onChange={() => null}
      />
    </Stack>
  );
}
