import { InputMultiSelect } from "@/components/filters/inputs/InputMultiSelect";
import type { InputMultiSelectProps } from "@/components/filters/inputs/type";
import { AttributeTypeEnum, OperatorEnum } from "@/enums";

export const INPUT_MAPPING_MULTISELECT = {
  [`${AttributeTypeEnum.MULTISELECT}-${OperatorEnum.CONTAINS}`]: ({ disabled, defaultValue, header, onChange, sx }: InputMultiSelectProps) => <InputMultiSelect disabled={disabled} defaultValue={defaultValue} onChange={onChange} header={header} sx={sx} />,
  [`${AttributeTypeEnum.MULTISELECT}-${OperatorEnum.EXCLUDES}`]: ({ disabled, defaultValue, header, onChange, sx }: InputMultiSelectProps) => <InputMultiSelect disabled={disabled} defaultValue={defaultValue} onChange={onChange} header={header} sx={sx} />,
  [`${AttributeTypeEnum.MULTISELECT}-${OperatorEnum.EXISTS}`]: () => null,
  [`${AttributeTypeEnum.MULTISELECT}-${OperatorEnum.INTERSECT_LEAST_ONE}`]: ({ disabled, defaultValue, header, onChange, sx }: InputMultiSelectProps) => <InputMultiSelect disabled={disabled} defaultValue={defaultValue} onChange={onChange} header={header} sx={sx} />,
};
