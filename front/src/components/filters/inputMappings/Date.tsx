import { InputDate } from "@/components/filters/inputs/InputDate";
import type { InputDateProps } from "@/components/filters/inputs/type";
import { AttributeTypeEnum, OperatorEnum } from "@/enums";

export const INPUT_MAPPING_DATE = {
  [`${AttributeTypeEnum.DATE}-${OperatorEnum.EQ}`]: ({ disabled, defaultValue, onChange, sx }: InputDateProps) => <InputDate disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.DATE}-${OperatorEnum.NEQ}`]: ({ disabled, defaultValue, onChange, sx }: InputDateProps) => <InputDate disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.DATE}-${OperatorEnum.GT}`]: ({ disabled, defaultValue, onChange, sx }: InputDateProps) => <InputDate disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.DATE}-${OperatorEnum.GTE}`]: ({ disabled, defaultValue, onChange, sx }: InputDateProps) => <InputDate disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.DATE}-${OperatorEnum.LT}`]: ({ disabled, defaultValue, onChange, sx }: InputDateProps) => <InputDate disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.DATE}-${OperatorEnum.LTE}`]: ({ disabled, defaultValue, onChange, sx }: InputDateProps) => <InputDate disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.TEXT}-${OperatorEnum.EXISTS}`]: () => null,
};
