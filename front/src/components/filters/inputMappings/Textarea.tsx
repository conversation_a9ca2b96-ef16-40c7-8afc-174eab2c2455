import { InputText } from "@/components/filters/inputs/InputText";
import { InputTextCollection } from "@/components/filters/inputs/InputTextCollection";
import type { InputTextCollectionProps, InputTextProps } from "@/components/filters/inputs/type";
import { AttributeTypeEnum, OperatorEnum } from "@/enums";

export const INPUT_MAPPING_TEXTAREA = {
  [`${AttributeTypeEnum.TEXTAREA}-${OperatorEnum.EQ}`]: ({ disabled, defaultValue, onChange, sx }: InputTextProps) => <InputText disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.TEXTAREA}-${OperatorEnum.NEQ}`]: ({ disabled, defaultValue, onChange, sx }: InputTextProps) => <InputText disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.TEXTAREA}-${OperatorEnum.IN}`]: ({ disabled, defaultValue, onChange, sx }: InputTextCollectionProps) => <InputTextCollection disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.TEXTAREA}-${OperatorEnum.LIKE}`]: ({ disabled, defaultValue, onChange, sx }: InputTextProps) => <InputText disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.TEXTAREA}-${OperatorEnum.ENDS_WITH}`]: ({ disabled, defaultValue, onChange, sx }: InputTextProps) => <InputText disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.TEXTAREA}-${OperatorEnum.STARTS_WITH}`]: ({ disabled, defaultValue, onChange, sx }: InputTextProps) => <InputText disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.TEXTAREA}-${OperatorEnum.EXISTS}`]: () => null,
};
