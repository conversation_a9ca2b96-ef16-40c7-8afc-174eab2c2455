import {InputText} from "@/components/filters/inputs/InputText";
import {InputTextCollection} from "@/components/filters/inputs/InputTextCollection";
import type {InputTextCollectionProps, InputTextProps} from "@/components/filters/inputs/type";
import {AttributeTypeEnum, OperatorEnum} from "@/enums";

export const INPUT_MAPPING_TEXT = {
  [`${AttributeTypeEnum.TEXT}-${OperatorEnum.EQ}`]: ({disabled, defaultValue, onChange, sx}: InputTextProps) => <InputText disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.TEXT}-${OperatorEnum.NEQ}`]: ({disabled, defaultValue, onChange, sx}: InputTextProps) => <InputText disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.TEXT}-${OperatorEnum.IN}`]: ({disabled, defaultValue, onChange, sx}: InputTextCollectionProps) => <InputTextCollection disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.TEXT}-${OperatorEnum.LIKE}`]: ({disabled, defaultValue, onChange, sx}: InputTextProps) => <InputText disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.TEXT}-${OperatorEnum.ENDS_WITH}`]: ({disabled, defaultValue, onChange, sx}: InputTextProps) => <InputText disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.TEXT}-${OperatorEnum.STARTS_WITH}`]: ({disabled, defaultValue, onChange, sx}: InputTextProps) => <InputText disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.TEXT}-${OperatorEnum.EXISTS}`]: () => null,
};
