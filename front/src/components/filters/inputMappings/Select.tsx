import { InputSelect } from "@/components/filters/inputs/InputSelect";
import type { InputSelectProps } from "@/components/filters/inputs/type";
import { AttributeTypeEnum, OperatorEnum } from "@/enums";

export const INPUT_MAPPING_SELECT = {
  [`${AttributeTypeEnum.SELECT}-${OperatorEnum.EQ}`]: ({ disabled, defaultValue, header, onChange, sx }: InputSelectProps) => <InputSelect disabled={disabled} defaultValue={defaultValue} onChange={onChange} header={header} sx={sx} />,
  [`${AttributeTypeEnum.SELECT}-${OperatorEnum.NEQ}`]: ({ disabled, defaultValue, header, onChange, sx }: InputSelectProps) => <InputSelect disabled={disabled} defaultValue={defaultValue} onChange={onChange} header={header} sx={sx} />,
  [`${AttributeTypeEnum.SELECT}-${OperatorEnum.IN}`]: ({ disabled, defaultValue, header, onChange, sx }: InputSelectProps) => <InputSelect disabled={disabled} defaultValue={defaultValue} onChange={onChange} header={header} multiple sx={sx} />,
  [`${AttributeTypeEnum.SELECT}-${OperatorEnum.EXISTS}`]: () => null,
};
