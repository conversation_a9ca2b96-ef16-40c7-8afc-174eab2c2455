import { InputSwitch } from "@/components/filters/inputs/InputSwitch";
import type { InputSwitchProps } from "@/components/filters/inputs/type";
import { AttributeTypeEnum, OperatorEnum } from "@/enums";

// TODO
export const INPUT_MAPPING_SWITCH = {
  [`${AttributeTypeEnum.SWITCH}-${OperatorEnum.EQ}`]: ({ disabled, defaultValue, onChange, sx }: InputSwitchProps) => <InputSwitch disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.SWITCH}-${OperatorEnum.NEQ}`]: ({ disabled, defaultValue, onChange, sx }: InputSwitchProps) => <InputSwitch disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.SWITCH}-${OperatorEnum.EXISTS}`]: () => null,
};
