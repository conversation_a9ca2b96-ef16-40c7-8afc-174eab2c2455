import { InputNumber } from "@/components/filters/inputs/InputNumber";
import type { InputNumberProps } from "@/components/filters/inputs/type";
import { AttributeTypeEnum, OperatorEnum } from "@/enums";

export const INPUT_MAPPING_NUMBER = {
  [`${AttributeTypeEnum.NUMBER}-${OperatorEnum.EQ}`]: ({ disabled, defaultValue, onChange, sx }: InputNumberProps) => <InputNumber disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.NUMBER}-${OperatorEnum.NEQ}`]: ({ disabled, defaultValue, onChange, sx }: InputNumberProps) => <InputNumber disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.NUMBER}-${OperatorEnum.GT}`]: ({ disabled, defaultValue, onChange, sx }: InputNumberProps) => <InputNumber disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.NUMBER}-${OperatorEnum.GTE}`]: ({ disabled, defaultValue, onChange, sx }: InputNumberProps) => <InputNumber disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.NUMBER}-${OperatorEnum.LT}`]: ({ disabled, defaultValue, onChange, sx }: InputNumberProps) => <InputNumber disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.NUMBER}-${OperatorEnum.LTE}`]: ({ disabled, defaultValue, onChange, sx }: InputNumberProps) => <InputNumber disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.NUMBER}-${OperatorEnum.EXISTS}`]: () => null,
};
