import { InputColor } from "@/components/filters/inputs/InputColor";
import type { InputColorProps } from "@/components/filters/inputs/type";
import { AttributeTypeEnum, OperatorEnum } from "@/enums";

export const INPUT_MAPPING_COLOR = {
  [`${AttributeTypeEnum.COLOR}-${OperatorEnum.EQ}`]: ({ disabled, defaultValue, onChange, sx }: InputColorProps) => <InputColor disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.COLOR}-${OperatorEnum.NEQ}`]: ({ disabled, defaultValue, onChange, sx }: InputColorProps) => <InputColor disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
};
