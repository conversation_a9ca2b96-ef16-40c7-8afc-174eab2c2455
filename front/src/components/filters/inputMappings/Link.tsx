import { InputLink } from "@/components/filters/inputs/InputLink";
import { InputTextCollection } from "@/components/filters/inputs/InputTextCollection";
import type { InputTextCollectionProps, InputTextProps } from "@/components/filters/inputs/type";
import { AttributeTypeEnum, OperatorEnum } from "@/enums";

export const INPUT_MAPPING_LINK = {
  [`${AttributeTypeEnum.LINK}-${OperatorEnum.EQ}`]: ({ disabled, defaultValue, onChange, sx }: InputTextProps) => <InputLink disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.LINK}-${OperatorEnum.NEQ}`]: ({ disabled, defaultValue, onChange, sx }: InputTextProps) => <InputLink disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.LINK}-${OperatorEnum.IN}`]: ({ disabled, defaultValue, onChange, sx }: InputTextCollectionProps) => <InputTextCollection disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.LINK}-${OperatorEnum.LIKE}`]: ({ disabled, defaultValue, onChange, sx }: InputTextProps) => <InputLink disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.LINK}-${OperatorEnum.ENDS_WITH}`]: ({ disabled, defaultValue, onChange, sx }: InputTextProps) => <InputLink disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.LINK}-${OperatorEnum.STARTS_WITH}`]: ({ disabled, defaultValue, onChange, sx }: InputTextProps) => <InputLink disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.LINK}-${OperatorEnum.EXISTS}`]: () => null,
};
