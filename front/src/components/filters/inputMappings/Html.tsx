import { InputText } from "@/components/filters/inputs/InputText";
import { InputTextCollection } from "@/components/filters/inputs/InputTextCollection";
import type { InputTextCollectionProps, InputTextProps } from "@/components/filters/inputs/type";
import { AttributeTypeEnum, OperatorEnum } from "@/enums";

export const INPUT_MAPPING_HTML = {
  [`${AttributeTypeEnum.HTML}-${OperatorEnum.EQ}`]: ({ disabled, defaultValue, onChange }: InputTextProps) => <InputText disabled={disabled} defaultValue={defaultValue} onChange={onChange} />,
  [`${AttributeTypeEnum.HTML}-${OperatorEnum.NEQ}`]: ({ disabled, defaultValue, onChange }: InputTextProps) => <InputText disabled={disabled} defaultValue={defaultValue} onChange={onChange} />,
  [`${AttributeTypeEnum.HTML}-${OperatorEnum.IN}`]: ({ disabled, defaultValue, onChange }: InputTextCollectionProps) => <InputTextCollection disabled={disabled} defaultValue={defaultValue} onChange={onChange} />,
  [`${AttributeTypeEnum.HTML}-${OperatorEnum.EXISTS}`]: () => null,
};
