import { InputTextCollection } from "@/components/filters/inputs/InputTextCollection";
import type { InputTextCollectionProps } from "@/components/filters/inputs/type";
import { AttributeTypeEnum, OperatorEnum } from "@/enums";

export const INPUT_MAPPING_TEXT_COLLECTION = {
  [`${AttributeTypeEnum.TEXT_COLLECTION}-${OperatorEnum.IN}`]: ({ disabled, defaultValue, onChange, sx }: InputTextCollectionProps) => <InputTextCollection disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
};
