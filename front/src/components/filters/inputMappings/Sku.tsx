import SelectProductFile from "@/components/selects/SelectProductFile";
import type {InputSkuProps} from "@/components/filters/inputs/type";
import {OperatorEnum} from "@/enums";

export const INPUT_MAPPING_SKU = {
  [`sku-${OperatorEnum.EQ}`]: ({disabled, defaultValue, onChange, sx}: InputSkuProps) => (
    <SelectProductFile required noLabel disabled={disabled} defaultValue={defaultValue} onChange={onChange} error={null} multiple={false} getterKey="sku" allowCreate={null} renderOptionKey={null} sx={sx} />
  ),
  [`sku-${OperatorEnum.NEQ}`]: ({disabled, defaultValue, onChange, sx}: InputSkuProps) => (
    <SelectProductFile required noLabel disabled={disabled} defaultValue={defaultValue} onChange={onChange} error={null} multiple={false} getterKey="sku" allowCreate={null} renderOptionKey={null} sx={sx} />
  ),
  [`sku-${OperatorEnum.IN}`]: ({disabled, defaultValue, onChange, sx}: InputSkuProps) => (
    <SelectProductFile required noLabel multiple disabled={disabled} defaultValue={defaultValue} onChange={onChange} error={null} getterKey="sku" allowCreate={null} renderOptionKey={null} sx={sx} />
  ),
  [`sku-${OperatorEnum.LIKE}`]: ({disabled, defaultValue, onChange, sx}: InputSkuProps) => (
    <SelectProductFile required noLabel disabled={disabled} defaultValue={defaultValue} onChange={onChange} error={null} multiple={false} getterKey="sku" allowCreate={null} renderOptionKey={null} sx={sx} />
  ),
  [`sku-${OperatorEnum.ENDS_WITH}`]: ({disabled, defaultValue, onChange, sx}: InputSkuProps) => (
    <SelectProductFile required noLabel disabled={disabled} defaultValue={defaultValue} onChange={onChange} error={null} multiple={false} getterKey="sku" allowCreate={null} renderOptionKey={null} sx={sx} />
  ),
  [`sku-${OperatorEnum.STARTS_WITH}`]: ({disabled, defaultValue, onChange, sx}: InputSkuProps) => (
    <SelectProductFile required noLabel disabled={disabled} defaultValue={defaultValue} onChange={onChange} error={null} multiple={false} getterKey="sku" allowCreate={null} renderOptionKey={null} sx={sx} />
  ),
  [`sku-${OperatorEnum.EXISTS}`]: () => null,
};
