import { InputMail } from "@/components/filters/inputs/InputMail";
import { InputTextCollection } from "@/components/filters/inputs/InputTextCollection";
import type { InputTextCollectionProps, InputTextProps } from "@/components/filters/inputs/type";
import { AttributeTypeEnum, OperatorEnum } from "@/enums";

export const INPUT_MAPPING_MAIL = {
  [`${AttributeTypeEnum.MAIL}-${OperatorEnum.EQ}`]: ({ disabled, defaultValue, onChange, sx }: InputTextProps) => <InputMail disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.MAIL}-${OperatorEnum.NEQ}`]: ({ disabled, defaultValue, onChange, sx }: InputTextProps) => <InputMail disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.MAIL}-${OperatorEnum.IN}`]: ({ disabled, defaultValue, onChange, sx }: InputTextCollectionProps) => <InputTextCollection disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.MAIL}-${OperatorEnum.LIKE}`]: ({ disabled, defaultValue, onChange, sx }: InputTextProps) => <InputMail disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.MAIL}-${OperatorEnum.ENDS_WITH}`]: ({ disabled, defaultValue, onChange, sx }: InputTextProps) => <InputMail disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.MAIL}-${OperatorEnum.STARTS_WITH}`]: ({ disabled, defaultValue, onChange, sx }: InputTextProps) => <InputMail disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.MAIL}-${OperatorEnum.EXISTS}`]: () => null,
};
