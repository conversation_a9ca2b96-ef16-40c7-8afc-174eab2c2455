import { InputDecimal } from "@/components/filters/inputs/InputDecimal";
import type { InputDecimalProps } from "@/components/filters/inputs/type";
import { AttributeTypeEnum, OperatorEnum } from "@/enums";

export const INPUT_MAPPING_DECIMAL = {
  [`${AttributeTypeEnum.DECIMAL}-${OperatorEnum.EQ}`]: ({ disabled, defaultValue, onChange, sx }: InputDecimalProps) => <InputDecimal disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.DECIMAL}-${OperatorEnum.NEQ}`]: ({ disabled, defaultValue, onChange, sx }: InputDecimalProps) => <InputDecimal disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.DECIMAL}-${OperatorEnum.GT}`]: ({ disabled, defaultValue, onChange, sx }: InputDecimalProps) => <InputDecimal disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.DECIMAL}-${OperatorEnum.GTE}`]: ({ disabled, defaultValue, onChange, sx }: InputDecimalProps) => <InputDecimal disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.DECIMAL}-${OperatorEnum.LT}`]: ({ disabled, defaultValue, onChange, sx }: InputDecimalProps) => <InputDecimal disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.DECIMAL}-${OperatorEnum.LTE}`]: ({ disabled, defaultValue, onChange, sx }: InputDecimalProps) => <InputDecimal disabled={disabled} defaultValue={defaultValue} onChange={onChange} sx={sx} />,
  [`${AttributeTypeEnum.DECIMAL}-${OperatorEnum.EXISTS}`]: () => null,
};
