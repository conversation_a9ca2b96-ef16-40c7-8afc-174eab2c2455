import Operator from "@/components/filters/Operator";
import Row from "@/components/filters/Row";
import {ButtonLink, ButtonNeutral} from "@/components/ui/Button";
import theme from "@/theme/theme";
import type {Filter, Filters} from "@/types/definition/SourceDefinition";
import {Stack} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import {useTranslation} from "@/hooks/useTranslation";
import {AttributeTypeEnum, FilterOperatorEnum} from "@/enums";
import {AlertPrimary} from "@/components/ui/Alert";

type FilterFormProps = {
  filters: Filters;
  onChange: (filters: Filters) => void;
  root?: boolean;
  type: AttributeTypeEnum;
};

const defaultRow: Partial<Filter> = {};

const defaultGroup = {
  operator: FilterOperatorEnum.AND,
  filters: [defaultRow],
};

export default function FilterForm({filters, onChange, root = true, type}: FilterFormProps) {
  const {t} = useTranslation();
  const operator = filters?.operator;
  const data = filters?.filters ?? [];

  const handleRowChange = (index: number, newRow: Filter) => {
    const updated = [...data];
    updated[index] = newRow;
    onChange({operator, filters: updated});
  };

  const handleGroupChange = (index: number, newGroup: Filters) => {
    const updated = [...data];
    updated[index] = newGroup;
    onChange({operator, filters: updated});
  };

  const handleOperatorChange = (newOperator: string) => {
    onChange({
      operator: newOperator,
      filters: data,
    });
  };

  const handleRemove = (index: number) => {
    const updated = [...data];
    updated.splice(index, 1);
    onChange({operator, filters: updated});
  };

  const addRow = () => {
    onChange({
      operator,
      filters: [...data, defaultRow],
    });
  };

  const addGroup = () => {
    onChange({
      operator,
      filters: [...data, defaultGroup],
    });
  };

  return (
    <Stack py={root ? 0 : 2} px={root ? 0 : 2} bgcolor={root ? "none" : theme.palette.info.light} border={root ? "none" : `1px solid ${theme.palette.grey[300]}`} borderRadius={1} gap={1} flexGrow={1}>
      {data.length === 0 ? <AlertPrimary>{t("filters.preferences.empty_list")}</AlertPrimary> : null}
      {data.map((filter, index: number) => {
        if ("filters" in filter) {
          return (
            <Stack direction="row" gap={1} key={index}>
              <Operator operator={operator} index={index} onChange={(value) => handleOperatorChange(value)} />
              <FilterForm filters={filter as Filters} onChange={(newGroup) => handleGroupChange(index, newGroup)} root={false} type={type} />
              <ButtonNeutral onClick={() => handleRemove(index)} size="small" isSelected={null}>
                <DeleteIcon color="error" />
              </ButtonNeutral>
            </Stack>
          );
        }

        return (
          <Row
            key={index + (filter.header || "default")}
            filter={filter}
            operator={operator}
            onOperatorChange={(value: string) => handleOperatorChange(value)}
            onChange={(newRow) => handleRowChange(index, newRow)}
            index={index}
            onDelete={() => handleRemove(index)}
            type={type}
          />
        );
      })}
      <Stack direction="row" gap={2}>
        <ButtonLink onClick={addRow}>+ {t("items.ChannelFilter.addRow")}</ButtonLink>
        <ButtonLink onClick={addGroup}>+ {t("items.ChannelFilter.addGroup")}</ButtonLink>
      </Stack>
    </Stack>
  );
}
