import {useTranslation} from "@/hooks/useTranslation";
import {MenuItem, Select} from "@mui/material";
import {FilterOperatorEnum} from "@/enums";

type SelectOperatorProps = {
  defaultValue: string;
  onChange: (value: string) => void;
};

export default function SelectOperator({defaultValue, onChange}: SelectOperatorProps) {
  const {t} = useTranslation();

  const defaultOptions = [
    {
      code: FilterOperatorEnum.AND,
      name: t("enums.OPERATORS.AND"),
    },
    {
      code: FilterOperatorEnum.OR,
      name: t("enums.OPERATORS.OR"),
    },
  ];
  return (
    <Select value={defaultValue} onChange={(e) => onChange(e.target.value)}>
      {defaultOptions.map((option) => (
        <MenuItem key={option.code} value={option.code}>
          {option.name}
        </MenuItem>
      ))}
    </Select>
  );
}
