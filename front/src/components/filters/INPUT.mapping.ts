import { INPUT_MAPPING_DATE } from "@/components/filters/inputMappings/Date";
import { INPUT_MAPPING_LINK } from "@/components/filters/inputMappings/Link";
import { INPUT_MAPPING_MAIL } from "@/components/filters/inputMappings/Mail";
import { INPUT_MAPPING_DECIMAL } from "@/components/filters/inputMappings/Decimal";
import { INPUT_MAPPING_NUMBER } from "@/components/filters/inputMappings/Number";
import { INPUT_MAPPING_SELECT } from "@/components/filters/inputMappings/Select";
import { INPUT_MAPPING_SKU } from "@/components/filters/inputMappings/Sku";
import { INPUT_MAPPING_SWITCH } from "@/components/filters/inputMappings/Switch";
import { INPUT_MAPPING_TEXT } from "@/components/filters/inputMappings/Text";
import { INPUT_MAPPING_TEXTAREA } from "@/components/filters/inputMappings/Textarea";
import { INPUT_MAPPING_TEXT_COLLECTION } from "@/components/filters/inputMappings/TextCollection";
import { INPUT_MAPPING_MULTISELECT } from "@/components/filters/inputMappings/Multiselect";
import { INPUT_MAPPING_COLOR } from "@/components/filters/inputMappings/Color";

export const INPUT_MAPPING = {
  ...INPUT_MAPPING_TEXT,
  ...INPUT_MAPPING_TEXT_COLLECTION,
  ...INPUT_MAPPING_SKU,
  ...INPUT_MAPPING_TEXTAREA,
  ...INPUT_MAPPING_LINK,
  ...INPUT_MAPPING_MAIL,
  ...INPUT_MAPPING_SELECT,
  ...INPUT_MAPPING_NUMBER,
  ...INPUT_MAPPING_DECIMAL,
  ...INPUT_MAPPING_DATE,
  ...INPUT_MAPPING_SWITCH,
  ...INPUT_MAPPING_MULTISELECT,
  ...INPUT_MAPPING_COLOR,
};
