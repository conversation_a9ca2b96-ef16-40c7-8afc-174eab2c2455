import { INPUT_MAPPING } from "@/components/filters/INPUT.mapping";
import { InputText } from "@/components/filters/inputs/InputText";
import type { ReactElement } from "react";
import { AttributeTypeEnum, OperatorEnum } from "@/enums";
import type { SxProps } from "@mui/material";

type DynamicInputProps = {
  type: AttributeTypeEnum | null;
  operator?: OperatorEnum | null;
  onChange: (value: unknown) => void;
  defaultValue: never;
  header?: string | null;
  sx?: SxProps;
};

export default function DynamicInput({ type, operator, defaultValue, onChange, header, sx }: DynamicInputProps): ReactElement {
  // @ts-expect-error - Dynamic input strange error
  if (!type || !operator || !INPUT_MAPPING[`${type}-${operator}`]) {
    return <InputText disabled={!type || !operator} defaultValue={defaultValue} onChange={onChange} sx={sx} />;
  }
  // @ts-expect-error - Dynamic input strange error
  return INPUT_MAPPING[`${type}-${operator}`]?.({ disabled: !type || !operator, defaultValue, onChange, header, sx });
}
