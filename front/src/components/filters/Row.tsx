import Operator from "@/components/filters/Operator";
import SelectHeader from "@/components/selects/SelectHeader";
import SelectOperator from "@/components/selects/SelectOperator";
import type { Filter } from "@/types/definition/SourceDefinition";
import { Stack } from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import { ButtonNeutral } from "@/components/ui/Button";
import { useState } from "react";
import DynamicInput from "@/components/filters/DynamicInput";
import { AttributeTypeEnum } from "@/enums";

type RowProps = {
  filter: Filter;
  operator: string | null;
  onChange: (filter: Filter) => void;
  index: number;
  onDelete: () => void;
  onOperatorChange: (value: string) => void;
  type: AttributeTypeEnum | null;
};

export default function Row({ filter, operator, onChange, index, onDelete, onOperatorChange, type }: RowProps) {
  const [headerType, setHeaderType] = useState<AttributeTypeEnum | null>(null);

  const handleChange = (field: string, value: string | string[] | null) => {
    if (field === "header") {
      onChange({ [field as string]: value, operator: null, value: null });
    } else if (field === "operator") {
      onChange({ [field as string]: value, header: filter.header, value: null });
    } else {
      onChange({ ...filter, [field]: value });
    }
  };

  return (
    <Stack direction="row" gap={1} alignItems="center">
      <Operator operator={operator as string} index={index} onChange={(value: string) => onOperatorChange(value)} />
      <Stack direction="row" gap={0} flexGrow={1}>
        <SelectHeader
          defaultValue={filter.header ?? null}
          required={true}
          error=""
          disabled={false}
          onChange={(value: Record<string, unknown> | null) => {
            handleChange("header", value?.header as string);
            const type = value?.header === "sku" ? "sku" : value?.type;
            setHeaderType(type as AttributeTypeEnum);
          }}
          onInit={(value: Record<string, unknown> | null) => {
            if (value && !headerType) {
              const type = value?.header === "sku" ? "sku" : value?.type;
              setHeaderType(type as AttributeTypeEnum);
            }
          }}
          multiple={false}
          getterKey="header"
          type={type}
          sx={{
            borderBottomRightRadius: 0,
            borderTopRightRadius: 0,
          }}
        />
        <Stack width="150px">
          <SelectOperator
            sx={{
              borderRadius: 0,
              borderLeft: "none",
              borderRight: "none",
            }}
            noLabel
            defaultValue={filter.operator}
            required={true}
            error=""
            disabled={!headerType}
            onChange={(value: string) => handleChange("operator", value)}
            multiple={false}
            getterKey="@id"
            type={headerType}
          />
        </Stack>
        <DynamicInput
          sx={{
            borderRadius: 0,
            borderRight: "none !important",
            "& .MuiOutlinedInput-root": {
              height: "100% !important",
              borderRadius: 0,
              borderRight: "none !important",
            },
            "& .MuiOutlinedInput-input": {
              height: "100% !important",
              borderRight: "none !important",
            },
            "& .MuiOutlinedInput-notchedOutline": {
              borderRight: "none !important",
            },
          }}
          type={headerType}
          defaultValue={filter.value as never}
          operator={filter.operator}
          onChange={(value: unknown) => handleChange("value", value as string)}
          header={filter.header}
        />
        <ButtonNeutral
          onClick={onDelete}
          size="small"
          sx={{
            borderBottomLeftRadius: 0,
            borderTopLeftRadius: 0,
            paddingX: 2,
            minWidth: 0,
          }}
        >
          <DeleteIcon color="error" />
        </ButtonNeutral>
      </Stack>
    </Stack>
  );
}
