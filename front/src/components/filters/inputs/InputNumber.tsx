import type { InputNumberProps } from "@/components/filters/inputs/type";
import { TextField } from "@mui/material";

export function InputNumber({ disabled, defaultValue, onChange, sx }: InputNumberProps) {
  return <TextField fullWidth placeholder="Value" disabled={disabled} defaultValue={defaultValue} onChange={(e) => onChange(e.target.value.length > 0 ? +e.target.value : null)} type="number" sx={sx} />;
}
