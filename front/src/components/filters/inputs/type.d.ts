import type {SxProps} from "@mui/material";

export type InputTextProps = {
  disabled: boolean;
  defaultValue: string;
  placeholder?: string;
  autoFocus?: boolean;
  required?: boolean;
  onChange?: (value: string | string[] | null) => void;
  sx?: SxProps;
};

export type InputTextCollectionProps = {
  disabled: boolean;
  defaultValue: string[];
  onChange: (value: string[] | null) => void;
  sx?: SxProps;
};

export type InputSkuProps = {
  disabled: boolean;
  defaultValue: string[];
  onChange: (value: string[] | null) => void;
  sx?: SxProps;
};

export type InputNumberProps = {
  disabled: boolean;
  defaultValue: number | null;
  onChange: (value: number | null) => void;
  sx?: SxProps;
};

export type InputDecimalProps = {
  disabled: boolean;
  defaultValue: number | null;
  onChange: (value: number | null) => void;
  sx?: SxProps;
};

export type InputDateProps = {
  disabled: boolean;
  defaultValue: string | null;
  onChange: (value: string | null) => void;
  sx?: SxProps;
};

export type InputSwitchProps = {
  disabled: boolean;
  defaultValue: string | null;
  onChange: (value: string | null) => void;
  sx?: SxProps;
};

export type InputSelectProps = {
  disabled: boolean;
  defaultValue: string | null;
  onChange: (value: string | string[] | null) => void;
  multiple?: boolean;
  header: string | null;
  sx?: SxProps;
};

export type InputMultiSelectProps = {
  disabled: boolean;
  defaultValue: string[];
  onChange: (value: string[] | null) => void;
  header: string | null;
  sx?: SxProps;
};

export type InputColorProps = {
  disabled: boolean;
  defaultValue: string;
  onChange: (value: string) => void;
  sx?: SxProps;
};
