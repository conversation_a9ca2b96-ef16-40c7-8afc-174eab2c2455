import type { InputSwitchProps } from "@/components/filters/inputs/type";
import { SelectInput } from "@/components/ui/inputs";
import { useTranslation } from "@/hooks/useTranslation";

export function InputSwitch({ disabled, defaultValue, onChange, sx }: InputSwitchProps) {
  const { t } = useTranslation();
  return (
    <SelectInput
      fullWidth
      label={null}
      error={null}
      required={false}
      helper={null}
      formControlProps={null}
      loading={false}
      options={[
        { value: true, label: t("common.yes") },
        { value: false, label: t("common.no") },
      ]}
      disabled={disabled}
      defaultValue={defaultValue}
      onChange={(e: { target: { value: string } }) => onChange(e.target.value)}
      sx={{
        ...sx,
        height: "100%",
        "& .MuiOutlinedInput-input": {
          height: "auto !important",
        },
      }}
    />
  );
}
