import type { InputSelectProps } from "@/components/filters/inputs/type";
import SelectAttributeOptionParams from "@/components/selects/SelectAttributeOptionParams";

export function InputSelect({ disabled, defaultValue, multiple, onChange, header, sx }: InputSelectProps) {
  return (
    <SelectAttributeOptionParams
      getterKey="code"
      noLabel
      defaultValue={defaultValue}
      onChange={(val: { code: string }[] | { code: string }) => onChange(multiple ? (val as { code: string }[]).map((v: { code: string }) => v.code) : (val as { code: string }).code)}
      multiple={multiple}
      required
      error={null}
      disabled={disabled}
      params={{ attribute: (header || "").replace("#", "") }}
      sx={sx}
    />
  );
}
