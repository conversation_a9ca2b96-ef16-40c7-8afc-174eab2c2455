import type { InputTextCollectionProps } from "@/components/filters/inputs/type";
import { ChipDefaultFilled } from "@/components/ui/Chip";
import { Autocomplete, TextField } from "@mui/material";

export function InputTextCollection({ disabled, defaultValue, onChange, sx }: InputTextCollectionProps) {
  return (
    <Autocomplete
      freeSolo
      multiple
      options={[]}
      open={false}
      disabled={disabled}
      defaultValue={defaultValue || []}
      fullWidth
      renderInput={(params) => <TextField {...params} placeholder="Value" />}
      sx={sx}
      onChange={(_, val) => onChange(val)}
      renderTags={(value, getTagProps) =>
        value.map((option, index) => {
          const handleDelete = (item: string) => {
            getTagProps({ index }).onDelete(item);
          };
          return (
            <ChipDefaultFilled key={index} onDelete={handleDelete}>
              {option}
            </ChipDefaultFilled>
          );
        })
      }
    />
  );
}
