import type { InputMultiSelectProps } from "@/components/filters/inputs/type";
import SelectAttributeOptionParams from "@/components/selects/SelectAttributeOptionParams";

export function InputMultiSelect({ disabled, defaultValue, onChange, header, sx }: InputMultiSelectProps) {
  return (
    <SelectAttributeOptionParams
      getterKey="code"
      noLabel
      defaultValue={defaultValue}
      onChange={(val: { code: string }[]) => onChange(val?.map((v: { code: string }) => v.code))}
      multiple={true}
      required
      error={null}
      disabled={disabled}
      params={{ attribute: (header || "").replace("#", "") }}
      sx={sx}
    />
  );
}
