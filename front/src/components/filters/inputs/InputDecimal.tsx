import type { InputDecimalProps } from "@/components/filters/inputs/type";
import { TextField } from "@mui/material";

export function InputDecimal({ disabled, defaultValue, onChange, sx }: InputDecimalProps) {
  return (
    <TextField
      fullWidth
      placeholder="Value"
      disabled={disabled}
      defaultValue={defaultValue}
      onChange={(e) => onChange(e.target.value.length > 0 ? parseFloat(e.target.value) : null)}
      type="number"
      slotProps={{
        htmlInput: {
          step: "any",
        },
      }}
      sx={sx}
    />
  );
}
