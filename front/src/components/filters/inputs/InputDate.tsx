import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider";
import {AdapterDayjs} from "@mui/x-date-pickers/AdapterDayjs";
import {DatePicker} from "@mui/x-date-pickers/DatePicker";
import {Stack} from "@mui/material";
import type {InputDateProps} from "@/components/filters/inputs/type";
import dayjs from "@/libs/dayjs";

// TODO : which format for date? ISO?
export function InputDate({disabled, defaultValue, onChange, sx}: InputDateProps) {
  const value = defaultValue ? dayjs(defaultValue).utc() : null;

  return (
    <Stack width="100%">
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker format="DD/MM/YYYY" disabled={disabled} defaultValue={value} onChange={(value) => onChange(value?.format("DD/MM/YYYY") ?? null)} sx={sx} />
      </LocalizationProvider>
    </Stack>
  );
}
