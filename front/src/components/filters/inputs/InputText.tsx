import type {InputTextProps} from "@/components/filters/inputs/type";
import {TextField} from "@mui/material";

export function InputText({defaultValue, onChange, placeholder = undefined, autoFocus = false, disabled = false, required = false, sx = {}}: InputTextProps) {
  return (
    <TextField
      required={required}
      fullWidth
      placeholder={placeholder}
      autoFocus={autoFocus}
      disabled={disabled}
      defaultValue={defaultValue}
      onChange={(e) => (onChange ? onChange(e.target.value) : null)}
      sx={sx}
    />
  );
}
