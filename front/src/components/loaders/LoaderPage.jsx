import styled from "@emotion/styled";
import { Grid } from "@mui/material";
import CircularProgress from "@mui/material/CircularProgress";
import { H5 } from "@/components/ui/Typography";
import { useTranslation } from "@/hooks/useTranslation";

const GridCustom = styled(Grid)`
  /* height: 300px; */
  ${({ style }) => style}
`;

const CircularLoader = styled(CircularProgress)`
  padding: 0;
`;

const H5Custom = styled(H5)`
  margin-top: 32px;
  text-align: center;
  width: 100%;
`;

export default function LoaderPage({ noAnimation = false }) {
  const { t } = useTranslation();

  return (
    <GridCustom container justifyContent="center" direction="column" alignItems="center" height="100%">
      {noAnimation ? (
        <img src="/logo512.png" width={100} alt="sinfin logo" />
      ) : (
        <>
          <CircularLoader color="inherit" size="5rem" />
          <H5Custom>{t("actions.loading")}</H5Custom>
        </>
      )}
    </GridCustom>
  );
}
