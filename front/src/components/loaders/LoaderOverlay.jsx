import Backdrop from "@mui/material/Backdrop";
import CircularProgress from "@mui/material/CircularProgress";
import styled from "@emotion/styled";
import { useUi } from "@/contexts/UiProvider";

const StyledBackdrop = styled(Backdrop)`
  z-index: 2000;
  color: #ffffff;
`;

export default function LoaderOverlay() {
  const { isLoading } = useUi();

  return (
    <StyledBackdrop open={isLoading}>
      <CircularProgress color="inherit" />
    </StyledBackdrop>
  );
};
