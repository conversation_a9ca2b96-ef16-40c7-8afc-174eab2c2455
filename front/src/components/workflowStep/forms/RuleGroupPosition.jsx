import Paginator from "@/components/ui/pagination/Paginator";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { usePaginator } from "@/hooks/usePaginator";
import { Box, Stack } from "@mui/material";
import { useTranslation } from "@/hooks/useTranslation";
import AddIcon from "@mui/icons-material/Add";
import { useEffect } from "react";
import LightTableDragRuleGroup from "@/components/lightTable/LightTableDragRuleGroup";
import { ButtonPrimary } from "@/components/ui/Button";
import { DirectionEnum } from "@/enums";

export default function RuleGroupPosition({ step, open, steps, onSuccess }) {
  const { t } = useTranslation();

  const paginator = usePaginator(ITEM_TYPES.WORKFLOW_STEP_RULE_GROUP, "/api/rule-groups", { step: step?.["@id"] }, { position: DirectionEnum.ASC }, false);

  useEffect(() => {
    const current = steps.find((s) => s["@id"] === step["@id"]);
    if (current?.ruleGroups?.length !== step?.ruleGroups?.length) {
      paginator.fetch();
    }
  }, [steps]);

  return (
    <Stack overflow="auto">
      <Paginator paginator={paginator}>
        <LightTableDragRuleGroup
          paginator={paginator}
          onSuccess={() => {
            onSuccess();
            paginator.fetch();
          }}
        />
      </Paginator>
      <Box mt={2} display="flex" justifyContent="center">
        <ButtonPrimary variant="text" startIcon={<AddIcon />} onClick={() => open()}>
          {t("items.WorkflowStep.add_ruleGroup")}
        </ButtonPrimary>
      </Box>
    </Stack>
  );
}
