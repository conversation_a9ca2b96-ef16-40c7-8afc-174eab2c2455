import OurTextField from "@/components/ui/inputs/OurTextField";
import Paginator from "@/components/ui/pagination/Paginator";
import { useUser } from "@/contexts/UserProvider";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { usePaginator } from "@/hooks/usePaginator";
import ListItemCstm from "@/components/ui/ListItemCstm";
import SearchIcon from "@mui/icons-material/Search";
import { Add, Sync } from "@mui/icons-material";
import { Box, InputAdornment, List, useTheme } from "@mui/material";
import { useState } from "react";
import { useTranslation } from "@/hooks/useTranslation";
import { useApi } from "@/contexts/ApiProvider";
import { ButtonPrimary } from "@/components/ui/Button";
import { DirectionEnum } from "@/enums";

export default function RuleGroupSelection({ workflowStep, onSuccess, openRuleGroupForm }) {
  const { t } = useTranslation();
  const { preferences } = useUser();
  const theme = useTheme();
  const api = useApi();

  const groupsInWorkflow = usePaginator(ITEM_TYPES.RULE_GROUP, "/api/rule-groups", {}, { code: DirectionEnum.ASC });

  const add = async (group) => {
    try {
      await api.post("/api/workflow-step-rule-groups", {
        ruleGroup: group,
        workflowStep: workflowStep["@id"],
      });
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.log(error);
    }
  };

  const [search, setSearch] = useState("");

  return (
    <Box
      display="flex"
      flexDirection="column"
      gap={2}
      p={2}
      sx={{
        backgroundColor: theme.palette.background.main,
        border: `0.5px solid ${theme.palette.border.main}`,
      }}
    >
      <OurTextField
        variant="outlined"
        color="secondary"
        name="search"
        fullWidth
        placeholder={t("actions.search")}
        value={search}
        onChange={(e) => setSearch(e.target.value)}
        onKeyPress={(e) => {
          if (e.code === "NumpadEnter" || e.code === "Enter") {
            e.preventDefault();
            const param = `names[${preferences.getLang()}]`;
            groupsInWorkflow.fetch({ [param]: search });
          }
        }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              {groupsInWorkflow.isLoading ? (
                <Sync />
              ) : (
                <SearchIcon
                  style={{
                    color: "#9e9e9e",
                  }}
                />
              )}
            </InputAdornment>
          ),
        }}
      />
      <Paginator paginator={groupsInWorkflow} open={openRuleGroupForm}>
        <List
          sx={{
            backgroundColor: "rgba(0,0,0,0.05)",
            border: groupsInWorkflow.items.length > 0 && `0.5px solid ${theme.palette.border.main}`,
            borderWidth: 1,
            overflowY: "scroll",
          }}
        >
          {groupsInWorkflow.items?.map((c, i) => (
            <ListItemCstm key={c["@id"]} label={t(c?.names)} isLast={i === groupsInWorkflow.items.length - 1} onClick={() => add(c["@id"])} />
          ))}
        </List>
      </Paginator>
      <ButtonPrimary variant="text" startIcon={<Add />} onClick={openRuleGroupForm}>
        {t("items.RuleGroup.create")}
      </ButtonPrimary>
    </Box>
  );
}
