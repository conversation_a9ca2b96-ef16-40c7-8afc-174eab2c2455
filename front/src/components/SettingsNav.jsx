import { useTranslation } from "@/hooks/useTranslation";
import Ellipsis from "@/components/ui/Ellipsis";
import { Stack, Link as MuiLink, Paper } from "@mui/material";
import { Link } from "react-router";
import { useUser } from "@/contexts/UserProvider";
import { useSettings } from "@/contexts/SettingsProvider";
import { H6 } from "@/components/ui/Typography";
import { ModuleEnum, RouteEnum } from "@/enums";

export default function SettingsNav() {
  const { t } = useTranslation();
  const { rights } = useUser();
  const { hasModule } = useSettings();

  const isActive = (route) => window.location.pathname === route || window.location.pathname.includes(`${route}/`);

  const settingsNav = [
    {
      title: t("items.User._"),
      children: [
        rights.hasAllRights() && {
          title: t("items.User._"),
          to: RouteEnum.USERS_LIST,
        },
        rights.hasAllRights() && {
          title: t("items.UserLogin._"),
          to: RouteEnum.USERS_STATS,
        },
        {
          title: t("items.UserGroupMembershipRequest._"),
          to: RouteEnum.USERS_REQUESTS,
        },
        {
          title: t("items.UserGroup._"),
          to: RouteEnum.USERS_GROUPS,
        },
      ],
    },
    rights.hasAllRights() && {
      title: t("drawer.centralisation"),
      children: [
        {
          title: t("items.Template._"),
          to: RouteEnum.CONFIGURATION_CENTRALISATION_TEMPLATES,
        },
        {
          title: t("items.AttributeGroup._"),
          to: RouteEnum.CONFIGURATION_CENTRALISATION_ATTRIBUTE_GROUPS,
        },
        {
          title: t("items.Attribute._"),
          to: RouteEnum.CONFIGURATION_CENTRALISATION_ATTRIBUTES,
        },
        {
          title: t("items.AttributeOption._"),
          to: RouteEnum.CONFIGURATION_CENTRALISATION_ATTRIBUTE_OPTIONS,
        },
      ],
    },
    rights.hasAllRights() &&
      hasModule(ModuleEnum.OPTIMIZATION) && {
        title: t("drawer.optimisation"),
        children: [
          {
            title: t("items.RuleGroup._"),
            to: RouteEnum.CONFIGURATION_OPTIMISATION_RULE_GROUPS,
          },
          {
            title: t("items.Rule._"),
            to: RouteEnum.CONFIGURATION_OPTIMISATION_RULES,
          },
          {
            title: t("items.Workflow._"),
            to: RouteEnum.CONFIGURATION_OPTIMISATION_WORKFLOWS,
          },
          {
            title: t("items.Completude._"),
            to: RouteEnum.CONFIGURATION_CENTRALISATION_COMPLENESS,
          },
        ],
      },
    rights.hasAllRights() && {
      title: t("drawer.layout"),
      children: [
        {
          title: t("items.Product._"),
          to: RouteEnum.PREFERENCES_AFFICHAGE_PRODUCTS,
        },
        hasModule(ModuleEnum.DAM) && {
          title: t("items.Media._"),
          to: RouteEnum.PREFERENCES_AFFICHAGE_MEDIAS,
        },
        hasModule(ModuleEnum.CONTENTS) && {
          title: t("items.Content._"),
          to: RouteEnum.PREFERENCES_AFFICHAGE_CONTENTS,
        },
      ].filter(Boolean),
    },
    rights.hasAllRights() && {
      title: t("drawer.preferences"),
      children: [
        {
          title: t("items.Locale._"),
          to: RouteEnum.PREFERENCES_LOCALES,
        },
        {
          title: t("items.Currency._"),
          to: RouteEnum.PREFERENCES_CURRENCIES,
        },
        {
          title: t("items.Unit._"),
          to: RouteEnum.PREFERENCES_UNITS,
        },
        {
          title: t("items.Config._"),
          to: RouteEnum.CONFIG,
        },
      ],
    },
    rights.hasAllRights() && {
      title: t("drawer.other"),
      children: [
        {
          title: t("items.UploadImport._"),
          to: RouteEnum.UPLOAD_IMPORTS,
        },
        {
          title: t("items.Dictionary._"),
          to: RouteEnum.DICTIONARIES,
        },
        {
          title: t('items.DictionaryMapping._'),
          to: RouteEnum.DICTIONARY_MAPPINGS,
        }
      ],
    },
  ].filter(Boolean);

  return (
    <Stack direction="row" py={2} pl={2}>
      <Stack component={Paper} p={2}>
        {settingsNav.map((s) => (
          <Stack key={s.title} gap={1}>
            <H6 fontWeight={600} color="primary">
              {s.title}
            </H6>
            {/* <DividerLeft label={s.title} /> */}
            <Stack gap={0.5}>
              {s.children?.map((c) => (
                <MuiLink
                  key={c.title}
                  component={Link}
                  to={c.to}
                  sx={{
                    px: 2,
                    py: isActive(c.to) ? 0.5 : 0,
                    color: isActive(c.to) ? "white" : "inherit",
                    backgroundColor: isActive(c.to) ? "primary.main" : "inherit",
                    textDecoration: "none",
                    "&:hover": { textDecoration: "underline" },
                  }}
                >
                  <Ellipsis text={<span style={{ fontWeight: isActive(c.to) ? 700 : 400 }}>{c.title}</span>} />
                </MuiLink>
              ))}
            </Stack>
          </Stack>
        ))}
      </Stack>
    </Stack>
  );
}
