import {useTranslation} from "@/hooks/useTranslation";
import type {ReactElement} from "react";
import {Alert, AlertTitle} from "@mui/material";
import type {Translations} from "@/types/model/Translations";

type Alert = {
  type: string;
  titles: Translations;
  messages: Translations;
}

type AlertProps = {
  alert: Alert;
}

export default function AlertFromBackend({alert}: AlertProps): null|ReactElement {
  const {t} = useTranslation();

  return (
    <Alert severity={alert.type}>
      <AlertTitle>{t(alert.titles)}</AlertTitle>
      {t(alert.messages)}
    </Alert>
  )
}
