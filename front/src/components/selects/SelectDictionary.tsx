import { useTranslation } from "@/hooks/useTranslation";
import InputsSkeleton from "../ui/inputs/InputsSkeleton";
import SelectAbstract from "../ui/select/SelectAbstract";
import StackedLabelCode from "../ui/StackedLabelCode";
import Translations from "../ui/translation/Translations";
import useSelect from "@/hooks/useSelect";
import { ChipDefaultFilled } from "../ui/Chip";
import type { Dictionary } from "@/resources";

type Props = {
  required?: boolean;
  defaultValue?: string | string[] | null;
  error?: string | null;
  onChange: (value: unknown) => void;
  noLabel?: boolean;
  multiple?: boolean;
  disabled?: boolean;
  getterKey: string;
};

export default function SelectDictionary({ required, defaultValue, error, onChange, noLabel, multiple, disabled, getterKey }: Props) {
  const { pluralize } = useTranslation();

  const select = useSelect({ defaultValue, identifier: "dictionaries", getterKey });

  return (
    <InputsSkeleton label={!noLabel ? pluralize("items.Dictionary", multiple) : null} required={required} error={error} fullWidth disabled={disabled}>
      <SelectAbstract
        placeholder={noLabel ? pluralize("items.Dictionary", multiple) : null}
        required={required}
        disabled={disabled}
        onChange={(val) => onChange(val ? (multiple ? val.map((v) => v[getterKey]) : val[getterKey]) : null)}
        multiple={multiple}
        select={select}
        renderOption={(option: Dictionary) => <StackedLabelCode item={option} />}
        renderSingleItem={(item: Dictionary) => <Translations translations={item.names} />}
        renderMultipleItem={(item: Dictionary, onDeleteItem: () => void) => <ChipDefaultFilled label={<Translations translations={item.names} />} onDelete={onDeleteItem} />}
      />
    </InputsSkeleton>
  );
}
