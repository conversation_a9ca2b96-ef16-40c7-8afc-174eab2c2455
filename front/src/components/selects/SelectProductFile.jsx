import { useTranslation } from "@/hooks/useTranslation";
import InputsSkeleton from "../ui/inputs/InputsSkeleton";
import SelectAbstract from "../ui/select/SelectAbstract";
import { CircularProgress, Stack } from "@mui/material";
import useSelect from "@/hooks/useSelect";
import { useCallback, useRef, useState } from "react";
import { useDropzone } from "react-dropzone";
import { FileUpload } from "@mui/icons-material";
import CloseIcon from "@mui/icons-material/Close";
import { useApi } from "@/contexts/ApiProvider";
import { ChipDefaultFilled } from "../ui/Chip";
import IconButton from "../ui/IconButton";
import Tooltip from "../ui/Tooltip";
import { DirectionEnum } from "@/enums";

export default function SelectProductFile({ required, defaultValue, error, onChange, noLabel, multiple, disabled, getterKey, allowCreate = null, renderOptionKey = null, sx }) {
  const { pluralize, t } = useTranslation();
  const select = useSelect({ defaultValue, identifier: "autocomplete/products", identifierKey: "sku", allowCreate: allowCreate, getterKey, params: { order: { sku: DirectionEnum.ASC } } });
  const api = useApi();
  const inputRef = useRef(null);
  const [loading, setLoading] = useState(false);

  const uploadFileToExtract = async (file) => {
    return new Promise((resolve, reject) => {
      if (file) {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onloadend = () => {
          const blob = new Blob([file], { name: file.name });

          const formData = new FormData();
          formData.append("file", blob);
          formData.append("type", file.type);
          formData.append("name", file.name);

          api
            .postFormData("/api/file-extractors", formData)
            .then((result) => {
              resolve(result);
            })
            .catch((e) => {
              console.log(e);
              reject(e);
            });
        };
      }
    });
  };

  const handleFile = async (file) => {
    setLoading(true);
    if (!file) {
      setLoading(false);
      return;
    }
    try {
      const result = await uploadFileToExtract(file);
      if (result.skus.length > 0) {
        onChange(result.skus);
        select.setDefaultValues(result.skus);
      }
      setLoading(false);
      if (inputRef.current) {
        inputRef.current.value = null;
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onDrop = useCallback((acceptedFiles) => {
    handleFile(acceptedFiles[0]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
  });

  return (
    <Stack gap={0} width="100%">
      <input {...getInputProps()} />
      <div
        {...getRootProps({
          onClick: (event) => event.stopPropagation(),
        })}
      >
        <InputsSkeleton label={!noLabel ? pluralize("items.Product", multiple) : null} required={required} error={error} fullWidth disabled={disabled}>
          <SelectAbstract
            sx={sx}
            placeholder={noLabel ? pluralize("items.Product", multiple) : null}
            required={required}
            disabled={disabled}
            onChange={(val) => onChange(val ? (multiple ? val.map((v) => v[getterKey] ?? v?.sku) : val?.[getterKey] ?? val.sku) : multiple ? [] : null)}
            multiple={multiple}
            select={select}
            renderOption={(option) => option[renderOptionKey ?? getterKey]}
            renderSingleItem={(item) => item[renderOptionKey ?? getterKey]}
            renderMultipleItem={(item, onDeleteItem) => <ChipDefaultFilled label={item[renderOptionKey ?? getterKey] || item} onDelete={onDeleteItem} />}
            isDragActive={isDragActive}
            limit={1}
            endAdornment={
              <>
                {multiple && select.values.length > 0 ? (
                  <IconButton
                    style={{ padding: "2px" }}
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      select.setDefaultValues([]);
                      select.setValues([]);
                      onChange([]);
                    }}
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                ) : null}
                <label onClick={(e) => e.stopPropagation()}>
                  <input ref={inputRef} style={{ display: "none" }} type="file" onChange={(evt) => handleFile(evt.target.files[0])} accept=".csv,.xls,.xlsx" />
                  <Tooltip title={t("actions.upload_file")}>
                    <IconButton onClick={() => inputRef.current.click()} size="small" sx={{ padding: "2px" }}>
                      {loading ? <CircularProgress size={20} color="black" /> : <FileUpload fontSize="small" />}
                    </IconButton>
                  </Tooltip>
                </label>
              </>
            }
          />
        </InputsSkeleton>
      </div>
    </Stack>
  );
}
