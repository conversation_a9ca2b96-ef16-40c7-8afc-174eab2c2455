import { useTranslation } from "@/hooks/useTranslation";
import InputsSkeleton from "../ui/inputs/InputsSkeleton";
import SelectAbstract from "../ui/select/SelectAbstract";
import StrUtils from "@/utils/str.utils";
import useSelect from "@/hooks/useSelect";
import DOCUMENT_TYPES from "@/enums/DOCUMENT_TYPES";
import { ChipDefaultFilled } from "../ui/Chip";

export default function SelectDocumentType({ required, defaultValue, error, onChange, noLabel, multiple, disabled, getterKey }) {
  const { t, pluralize } = useTranslation();

  const defaultOptions = Object.values(DOCUMENT_TYPES)
    .map((e) => ({
      [getterKey]: e,
      name: t(`enums.DOCUMENT_TYPES.${e}`),
    }))
    .sort((a, b) => StrUtils.spaceship(a.name, b.name));

  const select = useSelect({ defaultValue, defaultOptions, getterKey });

  return (
    <InputsSkeleton
      label={!noLabel ? pluralize("enums.DOCUMENT_TYPES", multiple) : null}
      required={required}
      error={error}
      fullWidth
      disabled={disabled}
    >
      <SelectAbstract
        placeholder={noLabel ? pluralize("enums.DOCUMENT_TYPES", multiple) : null}
        required={required}
        disabled={disabled}
        onChange={(val) => onChange(val ? (multiple ? val.map((v) => v[getterKey]) : val[getterKey]) : null)}
        multiple={multiple}
        renderOption={(option) => option.name}
        renderSingleItem={(item) => item.name}
        renderMultipleItem={(item, onDeleteItem) => <ChipDefaultFilled label={item.name} onDelete={onDeleteItem} />}
        select={select}
      />
    </InputsSkeleton>
  );
}
