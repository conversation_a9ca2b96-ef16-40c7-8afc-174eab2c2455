import { ChipDefaultFilled, ChipSecondary } from "@/components/ui/Chip";
import InputsSkeleton from "@/components/ui/inputs/InputsSkeleton";
import SelectAbstract from "@/components/ui/select/SelectAbstract";
import Translations from "@/components/ui/translation/Translations";
import useSelect from "@/hooks/useSelect";
import Header from "@/utils/models/Header";
import { Stack, Typography } from "@mui/material";
import { HeaderTypeIconEnum } from "@/enums/HeaderTypeIconEnum";
import type { HeaderTypeEnum } from "@/enums/HeaderTypeEnum";
import type { SvgIconComponent } from "@mui/icons-material";
import LocaleIcon from "@/assets/icons/LocaleIcon";
import ScopeIcon from "@/assets/icons/ScopeIcon";

type SelectHeaderProps = {
  defaultValue: string | null;
  required: boolean;
  error: string;
  disabled: boolean;
  onChange: (value: Record<string, unknown> | null) => void;
  onInit: (value: Record<string, unknown> | null) => void;
  multiple: boolean;
  getterKey: string;
  type: string | null;
  sx?: {};
};

type OptionType = {
  header: string;
  names: Record<string, string>;
  locale: string | null;
  scope: string | null;
  type: HeaderTypeEnum;
};

type FullOptionType = OptionType & {
  getterKey: string;
  locale: string | null;
  scope: string | null;
  icon: SvgIconComponent;
};

export default function SelectHeader({ defaultValue, required, error, disabled, onChange, onInit, multiple, getterKey, type, sx }: SelectHeaderProps) {
  const select = useSelect({
    defaultValue,
    identifier: "header-definitions",
    params: { documentType: type },
    getterKey,
    identifierKey: "header",
  });

  const renderOption = (option: OptionType) => {
    const fullOption = {
      ...option,
      ...Header.create(option.header),
      getterKey,
    } as FullOptionType;

    const HeaderIcon = HeaderTypeIconEnum[fullOption.type];

    return (
      <Stack gap={3} width={1} direction="row" alignItems="center" justifyContent="space-between">
        <Stack direction="row" gap={0} alignItems="center" overflow="hidden">
          {HeaderIcon ? <HeaderIcon sx={{ fontSize: 16 }} /> : null}
          <Typography
            sx={{
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              fontWeight: "bold",
            }}
          >
            <Translations translations={fullOption.names} />
          </Typography>
        </Stack>
        <Stack direction="row" gap={1} alignItems="center">
          {fullOption.locale ? <ChipSecondary icon={<LocaleIcon />}>{fullOption.locale}</ChipSecondary> : null}
          {fullOption.scope ? <ChipSecondary icon={<ScopeIcon />}>{fullOption.scope}</ChipSecondary> : null}
        </Stack>
      </Stack>
    );
  };

  return (
    <InputsSkeleton required={required} error={error} fullWidth disabled={disabled}>
      <SelectAbstract
        borderBetweenOptions
        placeholder="Header"
        required={required}
        disabled={disabled}
        onChange={(val: Record<string, unknown>) => {
          onChange(val ?? null);
        }}
        onInit={(val: Record<string, unknown>) => onInit(val ?? null)}
        multiple={multiple}
        select={select}
        renderOption={renderOption}
        renderSingleItem={(item: OptionType) => {
          return <Translations translations={item?.names} />;
        }}
        renderMultipleItem={(item: Record<string, unknown>, onDeleteItem: () => void) => (
          <ChipDefaultFilled onDelete={onDeleteItem}>
            <Translations translations={item.names} />
          </ChipDefaultFilled>
        )}
        sx={sx}
      />
    </InputsSkeleton>
  );
}
