import { useTranslation } from "@/hooks/useTranslation";
import InputsSkeleton from "../ui/inputs/InputsSkeleton";
import SelectAbstract from "../ui/select/SelectAbstract";
import StrUtils from "@/utils/str.utils";
import useSelect from "@/hooks/useSelect";
import { ChipDefaultFilled } from "../ui/Chip";
import { AttributeTypeEnum } from "@/enums";
import { AttributeTypeIconEnum } from "@/enums/AttributeTypeIconEnum";
import { Stack } from "@mui/material";
import { H6 } from "@/components/ui/Typography";

export default function SelectAttributeType({ required, defaultValue, error, onChange, noLabel, multiple, disabled, getterKey }) {
  const { t, pluralize } = useTranslation();

  const defaultOptions = Object.values(AttributeTypeEnum)
    .map((e) => ({
      [getterKey]: e,
      name: t(`enums.ATTRIBUTE_TYPES.${e}`),
      icon: AttributeTypeIconEnum[e],
    }))
    .sort((a, b) => StrUtils.spaceship(a.name, b.name));

  const select = useSelect({ defaultValue, defaultOptions, getterKey });

  return (
    <InputsSkeleton label={!noLabel ? pluralize("enums.ATTRIBUTE_TYPES", multiple) : null} required={required} error={error} fullWidth disabled={disabled}>
      <SelectAbstract
        borderBetweenOptions
        placeholder={noLabel ? pluralize("enums.ATTRIBUTE_TYPES", multiple) : null}
        required={required}
        disabled={disabled}
        onChange={(val) => onChange(val ? (multiple ? val.map((v) => v[getterKey]) : val[getterKey]) : null)}
        multiple={multiple}
        renderOption={(option) => (
          <Stack direction="row" alignItems="center" gap={1}>
            <option.icon />
            <H6>{option.name}</H6>
          </Stack>
        )}
        renderSingleItem={(item) => item.name}
        renderMultipleItem={(item, onDeleteItem) => <ChipDefaultFilled label={item.name} onDelete={onDeleteItem} />}
        select={select}
      />
    </InputsSkeleton>
  );
}
