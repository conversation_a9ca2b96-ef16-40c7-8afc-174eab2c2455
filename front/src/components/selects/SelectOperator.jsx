import { useTranslation } from "@/hooks/useTranslation";
import InputsSkeleton from "../ui/inputs/InputsSkeleton";
import SelectAbstract from "../ui/select/SelectAbstract";
import useSelect from "@/hooks/useSelect";
import { ChipDefaultFilled } from "../ui/Chip";
import { OPERATORS_MAPPING } from "@/components/filters/OPERATORS.mapping";
import { useEffect } from "react";

export default function SelectOperator({ required, defaultValue, error, onChange, noLabel, multiple, disabled, type, getterKey,sx }) {
  const { t, pluralize } = useTranslation();

  const options = (OPERATORS_MAPPING[type] || []).map((operator) => ({
    name: t(`enums.OPERATORS.${operator}`),
    [getterKey]: operator,
  }));

  const select = useSelect({
    defaultValue,
    defaultOptions: options,
    getterKey,
  });

  useEffect(() => {
    if (type && select.options.length === 0) {
      select.setOptions(options);
    } else if (defaultValue) {
      select.setStartLoading(true);
    }
  }, [type]);

  return (
    <InputsSkeleton label={!noLabel ? pluralize("enums.OPERATORS", multiple) : null} required={required} error={error} fullWidth disabled={disabled}>
      <SelectAbstract
        placeholder={noLabel ? pluralize("enums.OPERATORS", multiple) : null}
        required={required}
        disabled={disabled}
        onChange={(val) => onChange(val ? (multiple ? val.map((v) => v[getterKey]) : val[getterKey]) : null)}
        multiple={multiple}
        renderOption={(option) => option.name}
        renderSingleItem={(item) => item.name}
        renderMultipleItem={(item, onDeleteItem) => <ChipDefaultFilled label={item.name} onDelete={onDeleteItem} />}
        select={select}
        sx={sx}
        noSearch
      />
    </InputsSkeleton>
  );
}
