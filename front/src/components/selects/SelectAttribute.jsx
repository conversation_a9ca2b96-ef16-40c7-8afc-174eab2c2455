import { useTranslation } from "@/hooks/useTranslation";
import InputsSkeleton from "../ui/inputs/InputsSkeleton";
import SelectAbstract from "../ui/select/SelectAbstract";
import StackedLabelCode from "../ui/StackedLabelCode";
import Translations from "../ui/translation/Translations";
import useSelect from "@/hooks/useSelect";
import { ChipDefaultFilled } from "../ui/Chip";

export default function SelectAttribute({ required, defaultValue, error, onChange, noLabel, multiple, disabled, getterKey, params = {} }) {
  const { pluralize } = useTranslation();

  const select = useSelect({ defaultValue, identifier: "attributes", params, getterKey });

  return (
    <InputsSkeleton label={!noLabel ? pluralize("items.Attribute", multiple) : null} required={required} error={error} fullWidth disabled={disabled}>
      <SelectAbstract
        placeholder={noLabel ? pluralize("items.Attribute", multiple) : null}
        required={required}
        disabled={disabled}
        onChange={(val) => onChange(val ? (multiple ? val.map((v) => v[getterKey]) : val[getterKey]) : null)}
        multiple={multiple}
        select={select}
        renderOption={(option) => <StackedLabelCode item={option} />}
        renderSingleItem={(item) => <Translations translations={item.names} />}
        renderMultipleItem={(item, onDeleteItem) => <ChipDefaultFilled label={<Translations translations={item.names} />} onDelete={onDeleteItem} />}
      />
    </InputsSkeleton>
  );
}
