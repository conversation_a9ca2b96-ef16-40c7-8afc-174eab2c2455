import { useTranslation } from "@/hooks/useTranslation";
import InputsSkeleton from "../ui/inputs/InputsSkeleton";
import SelectAbstract from "../ui/select/SelectAbstract";
import StackedLabelCode from "../ui/StackedLabelCode";
import Translations from "../ui/translation/Translations";
import useSelect from "@/hooks/useSelect";
import { ChipDefaultFilled } from "../ui/Chip";

export default function SelectAttributeOptionParams({ required, defaultValue, error, onChange, noLabel, multiple, disabled, params, getterKey, sx }) {
  const { pluralize } = useTranslation();

  const select = useSelect({ defaultValue, identifier: "attribute-options", params, getterKey });

  console.log(sx);

  return (
    <InputsSkeleton label={!noLabel ? pluralize("items.AttributeOption", multiple) : null} required={required} error={error} fullWidth disabled={disabled}>
      <SelectAbstract
        placeholder={noLabel ? pluralize("items.AttributeOption", multiple) : null}
        required={required}
        disabled={disabled}
        onChange={(val) => onChange(val ?? null)}
        multiple={multiple}
        select={select}
        limit={1}
        renderOption={(option) => <StackedLabelCode item={option} />}
        renderSingleItem={(item) => <Translations translations={item.names} />}
        renderMultipleItem={(item, onDeleteItem) => <ChipDefaultFilled label={<Translations translations={item.names} />} onDelete={onDeleteItem} />}
        sx={sx}
      />
    </InputsSkeleton>
  );
}
