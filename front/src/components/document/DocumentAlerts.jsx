import AlertAttributeErrors from "@/components/alerts/AlertAttributeErrors";
import { useAttributes } from "@/contexts/AttributesProvider";
import { useTranslation } from "@/hooks/useTranslation";
import { Stack } from "@mui/material";
import { useSearchParams } from "react-router";

export default function DocumentAlerts({ scopes, combinedGroups, fetchRule }) {
  const { t } = useTranslation();
  const { attributes } = useAttributes();

  const [searchParams] = useSearchParams();
  const searchTerms = searchParams.get("search");

  // Filter out empty strings and codify the search terms
  const attributesSearched = searchTerms?.split(",").map((term) => term.trim().toLowerCase()).filter(Boolean);

  const uniqueAttributesFiltered = [...new Set((combinedGroups ?? []).flatMap((group) => group.attributes))];
  const attributesFiltered = uniqueAttributesFiltered.map((attr) => attr.code);

  const attributesNotEqualProvider = attributesSearched
    ?.filter((attr) => attributes.every((attribute) => attribute.code !== attr))
    .map((f) => ({
      code: scopes.errors.find((e) => e.rule.errorTargets.includes(f))?.rule?.code,
      value: f,
    }));

  const attributesNotEqualFiltered = attributesSearched
    ?.filter((attr) => !attributesFiltered.includes(attr) && !attributesNotEqualProvider.find((e) => e.value === attr))
    .map((f) => ({
      code: scopes.errors.find((e) => e.rule.errorTargets.includes(f))?.rule?.code,
      value: f,
    }));

  return (
    <Stack gap={1} py={1}>
      <AlertAttributeErrors
        title={t("items.Completude.missing_attributes")}
        errors={attributesNotEqualFiltered ?? []}
        onClick={(e) => fetchRule(e.code)}
      />
      <AlertAttributeErrors
        title={t("items.Completude.not_exist_attributes")}
        errors={attributesNotEqualProvider ?? []}
        onClick={(e) => fetchRule(e.code)}
      />
    </Stack>
  );
}
