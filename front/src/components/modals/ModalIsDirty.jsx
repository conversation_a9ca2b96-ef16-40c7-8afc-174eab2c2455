import ModalPrimary from "./ModalPrimary";
import { ButtonPrimary } from "../ui/Button";
import { useTranslation } from "@/hooks/useTranslation";

export default function ModalIsDirty({ open, onClose, onSuccess }) {
  const { t } = useTranslation();
  return (
    <ModalPrimary open={open} onClose={onClose} title={t("common.warning")} buttonSuccess={<ButtonPrimary onClick={onSuccess}>{t("actions.confirm")}</ButtonPrimary>}>
      {t("common.warning_close_form")}
    </ModalPrimary>
  );
}
