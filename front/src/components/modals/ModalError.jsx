import Modal from "@/components/ui/Modal";
import theme from "@/theme/theme";
import { Warning } from "@mui/icons-material";

export default function ModalError({ open, onClose, title, buttonSuccess, children, ...props }) {
  return (
    <Modal
      open={open}
      onClose={onClose}
      title={title}
      buttonSuccess={buttonSuccess}
      icon={<Warning color="error" fontSize="large" />}
      sx={{
        "& .MuiDialog-paper": {
          borderLeft: `solid 12px ${theme.palette.error.main}`,
        },
      }}
      {...props}
    >
      {children}
    </Modal>
  );
}
