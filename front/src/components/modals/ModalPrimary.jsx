import Modal from "@/components/ui/Modal";
import theme from "@/theme/theme";
import { Info } from "@mui/icons-material";

export default function ModalPrimary({ open, onClose, title, buttonSuccess, children, ...props }) {
  return (
    <Modal
      open={open}
      onClose={onClose}
      title={title}
      buttonSuccess={buttonSuccess}
      icon={<Info color="primary" fontSize="large" />}
      sx={{
        "& .MuiDialog-paper": {
          borderLeft: `solid 12px ${theme.palette.primary.main}`,
        },
      }}
      {...props}
    >
      {children}
    </Modal>
  );
}
