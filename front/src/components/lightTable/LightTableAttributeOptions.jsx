import { Table, TableBody, TableCell, TableHead, TableRow } from "@mui/material";
import Translations from "../ui/translation/Translations";
import { useAttributes } from "@/contexts/AttributesProvider";
import { useTranslation } from "react-i18next";

export default function LightTableAttributeOptions({ paginator, open }) {
  const { getAttribute } = useAttributes();
  const { t } = useTranslation();
  return (
    <Table>
      <TableHead>
        <TableRow>
          <TableCell>{t("items.AttributeOption.code")}</TableCell>
          <TableCell>{t("items.AttributeOption.names")}</TableCell>
          <TableCell>{t("items.AttributeOption.attribute")}</TableCell>
        </TableRow>
      </TableHead>
      <TableBody>
        {paginator.items.map((item) => (
          <TableRow key={item["@id"]} onClick={() => open(item)}>
            <TableCell>{item.code}</TableCell>
            <TableCell>
              <Translations translations={item.names} />
            </TableCell>
            <TableCell>
              <Translations translations={getAttribute(item.attribute)?.names} />
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
