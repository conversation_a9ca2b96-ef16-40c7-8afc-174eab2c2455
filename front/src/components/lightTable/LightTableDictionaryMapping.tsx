import { Table, TableBody, TableCell, TableHead, TableRow } from "@mui/material";
import { useTranslation } from "react-i18next";
import Paginator from "../ui/pagination/Paginator";
import type { DictionaryMapping } from "@/resources";

type Props = {
  open: (item: DictionaryMapping) => void;
  paginator: {
    items: DictionaryMapping[];
    goto: (page: number) => void;
  };
};

export default function LightTableDictionaryMapping({ paginator, open }: Props) {
  const { t } = useTranslation();

  const items: DictionaryMapping[] = paginator?.items || [];

  return (
    <Paginator paginator={paginator} forceToolbar>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>{t("items.DictionaryMapping.from")}</TableCell>
            <TableCell>{t("items.DictionaryMapping.to")}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {items.map((item) => (
            <TableRow key={item["@id"]} onClick={() => open(item)}>
              <TableCell>{item.from}</TableCell>
              <TableCell>{item.to}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Paginator>
  );
}
