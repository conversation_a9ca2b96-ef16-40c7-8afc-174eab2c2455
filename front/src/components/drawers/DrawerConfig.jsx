import { useState } from "react";
import DrawerForm from "./DrawerForm";
import FormHeaderItem from "../ui/item/forms/ui/FormHeaderItem";
import FormConfigPdf from "../forms/FormConfigPdf";
import { useTranslation } from "@/hooks/useTranslation";
import FormConfigPdfShipping from "@/components/forms/FormConfigPdfShipping";
import FormConfigEnvironment from "@/components/forms/FormConfigEnvironment";
import { ConfigurationTypeEnum } from "@/enums";

const CONFIGURATION_FORMS = {
  [ConfigurationTypeEnum.PDF]: FormConfigPdf,
  [ConfigurationTypeEnum.PDF_SHIPPING]: FormConfigPdfShipping,
  [ConfigurationTypeEnum.ENVIRONMENT]: FormConfigEnvironment,
  // csv: FormConfigurationCsv,
};

export default function DrawerConfig({ configuration, onClose, onChange, onRefresh }) {
  const { t } = useTranslation();

  const [isDirty, setIsDirty] = useState(false);

  const hookOnChange = (saved) => {
    onChange(saved);
    onRefresh();
  };

  const configurationWithoutId = { ...configuration };
  delete configurationWithoutId["@id"];

  const ConfigurationFormComponent = CONFIGURATION_FORMS[configuration?.key];

  return (
    <DrawerForm item={configuration} isDirty={isDirty} onClose={onClose} onChange={hookOnChange}>
      <FormHeaderItem item={configuration} label={t(`items.Config._types.${configuration?.key}._`)} />
      {ConfigurationFormComponent ? (
        <ConfigurationFormComponent
          item={configurationWithoutId}
          setIsDirty={setIsDirty}
          onSave={(saved) => {
            onClose();
            hookOnChange(saved);
          }}
        />
      ) : (
        <p>{t("items.Config._notFound")}{t("common.points")} {configuration?.key}</p>
      )}
    </DrawerForm>
  );
}
