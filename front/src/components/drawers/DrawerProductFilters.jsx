import { useEffect, useState } from "react";
import { Stack } from "@mui/material";
import { Body1, H3 } from "@/components/ui/Typography";
import { useTranslation } from "@/hooks/useTranslation";
import { useView } from "@/contexts/ViewProvider";
import { FormLayoutTab } from "../ui/form/FormLayouts";
import { ButtonPrimary, ButtonSecondary } from "../ui/Button";
import ModalPrimary from "../modals/ModalPrimary";
import Drawer from "../ui/Drawer";
import FilterForm from "../filters/FilterForm";
import { ApiTypeEnum, FilterOperatorEnum } from "@/enums";

export default function DrawerProductFilters({ open, onClose, onChange, resetColumns, paginator }) {
  const { view } = useView();
  const { t } = useTranslation();

  const productView = view?.[paginator.type];

  const [filtersForm, setFiltersForm] = useState(
    productView?.attributeFilters || {
      operator: FilterOperatorEnum.AND,
      filters: [],
    },
  );

  const [openModal, setOpenModal] = useState(false);

  function isFilterValid(filter) {
    const { operator, header, value } = filter;

    if (operator === "EXISTS") {
      return !!header && !!operator;
    }

    const isValueValid = value !== undefined && value !== null && (typeof value === "boolean" || typeof value === "number" || (typeof value === "string" && value.trim() !== "") || (Array.isArray(value) && value.length > 0));

    return !!header && !!operator && isValueValid;
  }

  function cleanFilters(group) {
    const cleanedFilters = [];

    for (const filter of group?.filters || []) {
      if ("filters" in filter && Array.isArray(filter.filters)) {
        const nestedClean = cleanFilters({ operator: filter.operator, filters: filter.filters });
        if (nestedClean && nestedClean.filters.length > 0) {
          cleanedFilters.push(nestedClean);
        }
      } else if (isFilterValid(filter)) {
        cleanedFilters.push(filter);
      }
    }

    if (0 === cleanedFilters.length) {
      return {};
    }

    return {
      operator: group.operator,
      filters: cleanedFilters,
    };
  }

  useEffect(() => {
    if (!open) {
      resetColumns();
    }
    if (open) {
      setFiltersForm(
        productView?.attributeFilters || {
          operator: FilterOperatorEnum.AND,
          filters: [],
        },
      );
    }
  }, [open]);

  const handleClose = () => {
    setFiltersForm(cleanFilters(filtersForm));
    onChange(cleanFilters(filtersForm));
    setOpenModal(false);
    onClose();
  };

  const submit = () => {
    if (JSON.stringify(cleanFilters(filtersForm)) !== JSON.stringify(filtersForm)) {
      setOpenModal(true);
    } else {
      handleClose();
    }
  };

  const reset = () => {
    setFiltersForm({
      operator: FilterOperatorEnum.AND,
      filters: [],
    });
    onChange({
      operator: FilterOperatorEnum.AND,
      filters: [],
    });
    setOpenModal(false);
    onClose();
  };

  const close = () => {
    setFiltersForm(
      productView?.attributeFilters || {
        operator: FilterOperatorEnum.AND,
        filters: [],
      },
    );
    setOpenModal(false);
    onClose();
  };

  return (
    <Drawer open={open} onClose={close} width="60%">
      <Stack display="grid" gridTemplateRows="max-content 1fr max-content" height="100%">
        <H3>{t("common.filters")}</H3>
        <FormLayoutTab>
          <Stack p={2}>
            <FilterForm type={ApiTypeEnum.PRODUCT} filters={filtersForm} onChange={setFiltersForm} />
          </Stack>
        </FormLayoutTab>
        <Stack direction="row" justifyContent="space-between">
          <ButtonSecondary type="button" onClick={reset}>
            {t("common.reset")}
          </ButtonSecondary>
          <ButtonPrimary type="button" onClick={submit}>
            {t("actions.save")}
          </ButtonPrimary>
        </Stack>
      </Stack>
      <ModalPrimary open={openModal} title={t("common.warning")} onClose={() => setOpenModal(false)} buttonSuccess={<ButtonPrimary onClick={handleClose}>{t("actions.confirm")}</ButtonPrimary>}>
        <Body1>{t("common.warning_close_form_filter")}</Body1>
      </ModalPrimary>
    </Drawer>
  );
}
