import {useState} from "react";
import AccordionDangerZone from "../accordions/AccordionDangerZone";
import FormDictionary from "../forms/formDictionary/FormDictionary";
import FormHeaderItem from "../ui/item/forms/ui/FormHeaderItem";
import DrawerForm from "./DrawerForm";
import type {Dictionary} from "@/resources";

type Props = {
  dictionary: Dictionary | null;
  onClose: () => void;
  onSuccess?: () => void;
  onDelete?: () => void;
};

export default function DrawerDictionary({dictionary, onClose, onSuccess, onDelete}: Props) {
  const hookOnChange = async () => {
    onSuccess?.();
  };

  const [formIsDirty, setFormIsDirty] = useState(false);

  return (
    <DrawerForm item={dictionary} isDirty={formIsDirty} onClose={onClose}>
      <FormHeaderItem item={dictionary} />
      <FormDictionary dictionary={dictionary} onSuccess={hookOnChange} onDirty={(isDirty) => setFormIsDirty(isDirty)} />
      <AccordionDangerZone item={dictionary} onDelete={onDelete} />
    </DrawerForm>
  );
}
