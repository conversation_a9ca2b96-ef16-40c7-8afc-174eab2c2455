import type {DictionaryMapping} from "@/resources";
import AccordionDangerZone from "../accordions/AccordionDangerZone";
import FormDictionaryMapping from "../forms/formDictionaryMapping/FormDictionaryMapping";
import FormHeaderItem from "../ui/item/forms/ui/FormHeaderItem";
import DrawerForm from "./DrawerForm";
import {useState} from "react";

type Props = {
  dictionaryMapping: DictionaryMapping | Partial<DictionaryMapping>;
  onClose: () => void;
  onSuccess?: () => void;
  onDelete?: () => void;
};

export default function DrawerDictionaryMapping({dictionaryMapping, onClose, onSuccess, onDelete}: Props) {
  const hookOnChange = () => {
    onSuccess?.();
  };
  const [formIsDirty, setFormIsDirty] = useState(false);

  return (
    <DrawerForm item={dictionaryMapping} isDirty={formIsDirty} onClose={onClose}>
      <FormHeaderItem item={dictionaryMapping} />
      <FormDictionaryMapping item={dictionaryMapping} onSuccess={hookOnChange} onDirty={(isDirty) => setFormIsDirty(isDirty)} />
      <AccordionDangerZone item={dictionaryMapping} onDelete={onDelete} />
    </DrawerForm>
  );
}
