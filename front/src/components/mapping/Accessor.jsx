import { Box, ButtonGroup, MenuItem, Stack } from "@mui/material";
import CHANNEL_ACCESSOR_TYPES from "@/enums/CHANNEL_ACCESSOR_TYPES";
import CHANNEL_ACCESSOR_VARIANTS from "@/enums/CHANNEL_ACCESSOR_VARIANTS";
import SelectMapping from "@/components/mapping/SelectMapping";
import { Input } from "@/components/ui/inputs/index";
import RuleEditor from "@/components/rules/ruleEditor/RuleEditor";
import { CHANNEL_ACCESSORS } from "@/components/mapping/FormMappingColumn";
import { useState } from "react";
import { MAPPING_PROPERTIES } from "@/enums/MAPPING_PROPERTIES";
import { useTranslation } from "@/hooks/useTranslation";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import SelectAttribute from "../selects/SelectAttribute";
import { ButtonPrimary } from "../ui/Button";
import { Body1, H3 } from "@/components/ui/Typography";
import AlertFromBackend from "@/components/alerts/AlertFromBackend.js";

const ALLOWED_ACCESSORS = {
  [ITEM_TYPES.IMPORT]: {
    [CHANNEL_ACCESSOR_VARIANTS.IN]: [CHANNEL_ACCESSOR_TYPES.FORMULA],
    [CHANNEL_ACCESSOR_VARIANTS.OUT]: [CHANNEL_ACCESSOR_TYPES.PROPERTY, CHANNEL_ACCESSOR_TYPES.ATTRIBUTE],
  },
  [ITEM_TYPES.EXPORT]: {
    [CHANNEL_ACCESSOR_VARIANTS.IN]: [CHANNEL_ACCESSOR_TYPES.PROPERTY, CHANNEL_ACCESSOR_TYPES.ATTRIBUTE, CHANNEL_ACCESSOR_TYPES.FORMULA, CHANNEL_ACCESSOR_TYPES.RAW],
    [CHANNEL_ACCESSOR_VARIANTS.OUT]: [CHANNEL_ACCESSOR_TYPES.PROPERTY],
  },
};

export default function Accessor({ variant, source, accessorVariant, accessor, onChange, item, disabled, helper }) {
  const { t } = useTranslation();

  const getAllowedType = () => {
    if (item?.in?.definition?.types.length > 0) {
      return CHANNEL_ACCESSORS.filter((a) => item?.in?.definition?.types.includes(a.type));
    }

    return CHANNEL_ACCESSORS.filter((a) => ALLOWED_ACCESSORS[variant][accessorVariant].includes(a.type));
  }

  const allowedTypes = getAllowedType()

  const [type, setType] = useState(accessor?.type ?? allowedTypes[0]?.type);

  const handleType = (type) => {
    setType(type);
    onChange({ type, definition: accessor.definition ? accessor.definition : null });
  };

  const handleChange = (key, value) => {
    onChange({ ...accessor, type, [key]: value });
  };

  const isSinfin = (ITEM_TYPES.IMPORT === variant && CHANNEL_ACCESSOR_VARIANTS.OUT === accessorVariant) || (ITEM_TYPES.EXPORT === variant && CHANNEL_ACCESSOR_VARIANTS.IN === accessorVariant);

  return (
    <>
      {item?.in?.definition?.ui?.alert ? <AlertFromBackend alert={item?.in?.definition?.ui?.alert} />: null}
      <H3>{t(`items.${variant}.Mapping.${accessorVariant}`)}</H3>
      {1 <= allowedTypes.length ? (
        <ButtonGroup>
          {allowedTypes.map((a) => (
            <ButtonPrimary key={a.type} variant={a.type === type ? "contained" : "outlined"} onClick={() => handleType(a.type)}>
              {a.icon} {t(`enums.CHANNEL_ACCESSOR_TYPES.${a.type}`)}
            </ButtonPrimary>
          ))}
        </ButtonGroup>
      ) : null}
      {CHANNEL_ACCESSOR_TYPES.PROPERTY === type && !isSinfin ? (
        <Input defaultValue={accessor?.property} label={t(`enums.CHANNEL_ACCESSOR_TYPES.${type}`)} placeholder={t(`enums.CHANNEL_ACCESSOR_TYPES.${type}`)} onChange={(e) => handleChange("property", e.target.value)} disabled={disabled} required helper={helper} />
      ) : CHANNEL_ACCESSOR_TYPES.PROPERTY === type ? (
        <SelectMapping defaultValue={accessor?.property} onChange={(e) => handleChange("property", e.target.value)} label={t(`enums.CHANNEL_ACCESSOR_TYPES.${type}`)} disabled={disabled} required>
          {(MAPPING_PROPERTIES[source.type] ?? []).map((property) => (
            <MenuItem key={property.code} value={property.code}>
              {t(property.label)}
            </MenuItem>
          ))}
        </SelectMapping>
      ) : CHANNEL_ACCESSOR_TYPES.ATTRIBUTE === type ? (
        <SelectAttribute defaultValue={accessor?.code} onChange={(e) => handleChange("code", e)} label={t(`enums.CHANNEL_ACCESSOR_TYPES.${type}`)} getterKey="code" disabled={disabled} required />
      ) : CHANNEL_ACCESSOR_TYPES.RAW === type ? (
        <Input placeholder={t(`enums.CHANNEL_ACCESSOR_TYPES.${type}`)} defaultValue={accessor?.raw} onChange={(e) => handleChange("raw", e.target.value)} label={t(`enums.CHANNEL_ACCESSOR_TYPES.${type}`)} disabled={disabled} required />
      ) : CHANNEL_ACCESSOR_TYPES.FORMULA === type ? (
        <Stack gap={0.5} position="relative">
          <Box position="absolute" right={5} top={-5}>
            <RuleEditor onChange={(e) => handleChange("formula", e)} defaultValue={item.in.formula} />
          </Box>
          <Body1>{t(`enums.CHANNEL_ACCESSOR_TYPES.${type}`) + "*"}</Body1>
          <Input
            multiline
            rows={4}
            placeholder={t(`enums.CHANNEL_ACCESSOR_TYPES.${type}`)}
            helper={ITEM_TYPES.IMPORT === variant ? t(`enums.CHANNEL_ACCESSOR_TYPES.${type}_import_help`) : null}
            value={item.in.formula}
            onChange={(e) => handleChange("formula", e.target.value)}
            required
            disabled={disabled}
          />
        </Stack>
      ) : null}
    </>
  );
}
