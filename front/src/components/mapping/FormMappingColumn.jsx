import { useTranslation } from "@/hooks/useTranslation";
import { Form } from "../ui/form/FormWrapper";
import { FormLayout } from "../ui/form/FormLayouts";
import { FormHelperText, Stack } from "@mui/material";
import ButtonSave from "../buttons/ButtonSave";
import { useState } from "react";
import CHANNEL_ACCESSOR_TYPES from "@/enums/CHANNEL_ACCESSOR_TYPES";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import RawOnIcon from "@mui/icons-material/RawOn";
import FunctionsIcon from "@mui/icons-material/Functions";
import ReceiptLongIcon from "@mui/icons-material/ReceiptLong";
import ManageSearchIcon from "@mui/icons-material/ManageSearch";
import { Add, Delete } from "@mui/icons-material";
import Accessor from "@/components/mapping/Accessor";
import CHANNEL_ACCESSOR_VARIANTS from "@/enums/CHANNEL_ACCESSOR_VARIANTS";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import DividerLeft from "@/components/ui/dividers/DividerLeft";
import { ButtonError, ButtonPrimary } from "../ui/Button";
import { AlertWarning } from "../ui/Alert";
import MappingOptions from "@/components/mapping/MappingOptions";

export const CHANNEL_ACCESSORS = [
  {
    type: CHANNEL_ACCESSOR_TYPES.PROPERTY,
    icon: <ReceiptLongIcon sx={{ fontSize: 16 }} />,
    variants: [ITEM_TYPES.EXPORT, ITEM_TYPES.IMPORT],
  },
  {
    type: CHANNEL_ACCESSOR_TYPES.ATTRIBUTE,
    icon: <ManageSearchIcon sx={{ fontSize: 16 }} />,
    variants: [ITEM_TYPES.EXPORT],
  },
  {
    type: CHANNEL_ACCESSOR_TYPES.FORMULA,
    icon: <FunctionsIcon sx={{ fontSize: 16 }} />,
    variants: [ITEM_TYPES.EXPORT, ITEM_TYPES.IMPORT],
  },
  {
    type: CHANNEL_ACCESSOR_TYPES.RAW,
    icon: <RawOnIcon sx={{ fontSize: 16 }} />,
    variants: [ITEM_TYPES.EXPORT, ITEM_TYPES.IMPORT],
  },
];

export default function FormMappingColumn({ variant, source, column, onSave, setIsDirty, onDelete, suggestMapping = [] }) {
  const { t } = useTranslation();

  const [item, setItem] = useState(() => column);

  const handleChange = (key, value) => {
    setItem((p) => ({ ...p, [key]: value }));
    setIsDirty(true);
  };

  return (
    <Form
      component="form"
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        onSave(item);
      }}
    >
      <FormLayout>
        {suggestMapping.length ? (
          <AlertWarning
            action={
              <ButtonPrimary color="inherit" size="small" startIcon={<Add />} onClick={() => handleChange("code", suggestMapping[0])}>
                {suggestMapping[0]}
              </ButtonPrimary>
            }
          >
            {t(`items.${variant}.MappingColumn.requiresColumns`)}
          </AlertWarning>
        ) : null}
        <Accessor
          variant={variant}
          source={source}
          accessorVariant={CHANNEL_ACCESSOR_VARIANTS.IN}
          accessor={column?.in}
          onChange={(accessor) => handleChange("in", accessor)} item={item}
          disabled={item.in.definition?.ui?.isReadonly}
        />
        {item?.helper ? <FormHelperText style={{ whiteSpace: "pre-line" }}>{t(item.helper)}</FormHelperText> : null}
        {item?.definition?.descriptions ? <FormHelperText style={{ whiteSpace: "pre-line" }}>{t(item.definition.descriptions)}</FormHelperText> : null}
        <DividerLeft icon={<ArrowDownwardIcon />} label={t(`items.${variant}.MappingColumn.willGoTo`)} />
        <Accessor
          variant={variant}
          source={source}
          accessorVariant={CHANNEL_ACCESSOR_VARIANTS.OUT}
          accessor={column?.out}
          onChange={(accessor) => handleChange("out", accessor)}
          disabled={item.out.disabled || item.out.definition?.ui?.isReadonly}
          helper={column?.descriptions ? t(column.descriptions) : null}
        />
        {column?.options ? <MappingOptions options={column?.options} /> : null}
        {column?.definition?.options ? <MappingOptions options={column.definition.options} /> : null}
      </FormLayout>
      <Stack direction="row" justifyContent="space-between">
        <ButtonError variant="outlined" onClick={onDelete} startIcon={<Delete />}>
          {t("common.delete")}
        </ButtonError>
        <ButtonSave />
      </Stack>
    </Form>
  );
}
