import { useRef } from "react";
import { useDrag, useDrop } from "react-dnd";
import { TableCell, TableRow } from "@mui/material";
import DragIndicatorIcon from "@mui/icons-material/DragIndicator";
import LabelAccessor from "@/components/mapping/LabelAccessor";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";

export default function MappingColumn({ column, index, moveItem, setItem, canUpdatePosition }) {
  const ref = useRef(null);

  const [{ handlerId }, drop] = useDrop({
    accept: "DraggableRow",
    collect: (monitor) => ({
      handlerId: monitor.getHandlerId(),
    }),
    hover(item, monitor) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) {
        return;
      }

      const hoverBoundingRect = ref.current?.getBoundingClientRect();
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }
      moveItem(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });

  const [, drag, preview] = useDrag({
    type: "DraggableRow",
    item: column,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  preview(drop(ref));

  return (
    <TableRow ref={ref} data-handler-id={handlerId} onClick={() => setItem(column)}>
      {canUpdatePosition ? (
        <TableCell>
          <div ref={drag} style={{ cursor: "move", display: "flex", alignItems: "center" }}>
            <DragIndicatorIcon sx={{ cursor: "move" }} />
          </div>
        </TableCell>
      ) : null}
      <TableCell>
        <LabelAccessor accessor={{ ...column.in, isRequired: column.isRequired || column.definition?.ui?.isRequired, isRecommended: column.isRecommended || column.definition?.isRecommended }} />
      </TableCell>
      <TableCell>
        <ArrowForwardIosIcon />
      </TableCell>
      <TableCell>
        <LabelAccessor accessor={{ ...column.out, names: column.names }} />
      </TableCell>
    </TableRow>
  );
}
