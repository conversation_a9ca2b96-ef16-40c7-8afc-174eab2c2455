import ChipAccessorType from "@/components/chips/ChipAccessorType.jsx";
import MappingUtils from "@/utils/mapping.utils.js";
import { Box, Stack } from "@mui/material";
import styled from "@emotion/styled";
import StackedInfos from "@/components/ui/StackedInfos";
import Ellipsis from "@/components/ui/Ellipsis";
import { useTranslation } from "@/hooks/useTranslation";

const AccessorLabel = styled(Box)(() => ({
  overflow: "hidden",
  whiteSpace: "nowrap",
  textOverflow: "ellipsis",
}));

export default function LabelAccessor({ accessor }) {
  const { t } = useTranslation();

  if (!accessor) {
    return null;
  }

  return (
    <Box display="grid" gridTemplateColumns="max-content 1fr" gap={1}>
      {accessor.names ? (
        <Stack gap={1} direction="row" alignItems="center">
          <ChipAccessorType type={accessor.type} />
          <StackedInfos firstLine={<Ellipsis text={t(accessor.names)} />} secondLine={MappingUtils.getAccessorView(accessor)} />
        </Stack>
      ) : (
        <>
          <ChipAccessorType type={accessor.type} />
          <AccessorLabel>{MappingUtils.getAccessorView(accessor)}</AccessorLabel>
        </>
      )}
    </Box>
  );
}
