import { useState } from "react";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import Paginator from "@/components/ui/pagination/Paginator";
import { usePaginator } from "@/hooks/usePaginator";
import { Box, Stack } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import { useTranslation } from "@/hooks/useTranslation";
import ItemUtils from "@/utils/item.utils";
import DrawerRule from "../drawers/DrawerRule";
import LightTableDragRule from "../lightTable/LightTableDragRule";
import { ButtonPrimary } from "../ui/Button";
import { DirectionEnum } from "@/enums";

export default function RulesSelection({ ruleGroup, onSuccess }) {
  const { t } = useTranslation();
  const paginator = usePaginator(ITEM_TYPES.RULE, "/api/rules", { ruleGroup: ruleGroup?.code }, { position: DirectionEnum.ASC }, false);
  const [rule, setRule] = useState(null);

  return (
    <Stack overflow="auto">
      <Paginator paginator={paginator}>
        <LightTableDragRule paginator={paginator} open={setRule} onSuccess={onSuccess} />
      </Paginator>

      <Box mt={2} display="flex" justifyContent="center">
        <ButtonPrimary variant="text" startIcon={<AddIcon />} onClick={() => setRule({ ...ItemUtils.getDefault(paginator.type), ruleGroup: ruleGroup["@id"], position: paginator.items.length })}>
          {t("items.Rule.create")}
        </ButtonPrimary>
      </Box>

      <DrawerRule
        rule={rule}
        ruleGroup={ruleGroup}
        onClose={() => setRule(null)}
        onChange={(rule) => void paginator.replaceItem(rule)}
        onRefresh={() => {
          void paginator.goto(1);
        }}
      />
    </Stack>
  );
}
