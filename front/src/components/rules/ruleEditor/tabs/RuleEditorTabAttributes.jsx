import FormFilterAttribute from "@/components/formFilters/FormFilterAttribute";
import TableAttributeRuleEditor from "@/components/rules/ruleEditor/tables/TableAttributeRuleEditor";
import Paginator from "@/components/ui/pagination/Paginator";
import { DirectionEnum } from "@/enums";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { usePaginator } from "@/hooks/usePaginator";
import { Stack } from "@mui/material";

export default function RuleEditorTabAttributes({ onSeeOptions, onChange }) {
  const paginator = usePaginator(ITEM_TYPES.ATTRIBUTE, "/api/attributes", {}, { code: DirectionEnum.ASC });

  return (
    <Stack flex={1} height="100%">
      <FormFilterAttribute paginator={paginator} />
      <Paginator paginator={paginator}>
        <TableAttributeRuleEditor paginator={paginator} onSeeOptions={onSeeOptions} onChange={onChange} />
      </Paginator>
    </Stack>
  );
}
