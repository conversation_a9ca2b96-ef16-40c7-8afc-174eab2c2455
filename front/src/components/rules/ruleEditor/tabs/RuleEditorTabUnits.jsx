import Paginator from "@/components/ui/pagination/Paginator";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { usePaginator } from "@/hooks/usePaginator";
import { Stack, useTheme } from "@mui/material";
import TableUnit from "@/components/tables/TableUnit";
import FormFilterUnit from "@/components/formFilters/FormFilterUnit";
import { DirectionEnum } from "@/enums";

export default function RuleEditorTabUnits({ onChange }) {
  const paginator = usePaginator(ITEM_TYPES.UNIT, "/api/units", {}, { code: DirectionEnum.ASC });
  const theme = useTheme();

  return (
    <Stack height="100%">
      <FormFilterUnit paginator={paginator} />
      <Paginator paginator={paginator}>
        <TableUnit paginator={paginator} bgcolor={theme.palette.white} onChange={onChange} />
      </Paginator>
    </Stack>
  );
}
