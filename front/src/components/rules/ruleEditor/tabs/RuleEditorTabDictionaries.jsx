import Paginator from "@/components/ui/pagination/Paginator";
import { usePaginator } from "@/hooks/usePaginator";
import { Stack, useTheme } from "@mui/material";
import { ApiTypeEnum, DirectionEnum } from "@/enums";
import FormFilterDictionaryMapping from "@/components/formFilters/FormFilterDictionaryMapping";
import TableDictionaryMapping from "@/components/tables/TableDictionaryMapping";

export default function RuleEditorTabDictionaries({ onChange }) {
  const paginator = usePaginator(ApiTypeEnum.DICTIONARY_MAPPING, "/api/dictionary-mappings", {}, { from: DirectionEnum.ASC });
  const theme = useTheme();

  return (
    <Stack height="100%">
      <FormFilterDictionaryMapping paginator={paginator} />
      <Paginator paginator={paginator}>
        <TableDictionaryMapping paginator={paginator} bgcolor={theme.palette.white} onChange={onChange} />
      </Paginator>
    </Stack>
  );
}
