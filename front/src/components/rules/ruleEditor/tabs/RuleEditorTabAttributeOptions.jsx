import FormFilterAttributeOption from "@/components/formFilters/FormFilterAttributeOption";
import Paginator from "@/components/ui/pagination/Paginator";
import TableAttributeOptionsRuleEditor from "@/components/rules/ruleEditor/tables/TableAttributeOptionsRuleEditor";
import ITEM_TYPES from "@/enums/ITEM_TYPES";
import { usePaginator } from "@/hooks/usePaginator";
import { Stack } from "@mui/material";
import { DirectionEnum } from "@/enums";

export default function RuleEditorTabAttributeOptions({ paginatorFilter = {}, onChange }) {
  const paginator = usePaginator(ITEM_TYPES.ATTRIBUTE_OPTION, "/api/attribute-options", {}, { code: DirectionEnum.ASC }, true, paginatorFilter);

  return (
    <Stack flex={1} height="100%">
      <FormFilterAttributeOption paginator={paginator} />
      <Paginator paginator={paginator}>
        <TableAttributeOptionsRuleEditor paginator={paginator} onChange={onChange} />
      </Paginator>
    </Stack>
  );
}
