import { Box, debounce, Dialog, DialogContent, Divider, Stack, TextField } from "@mui/material";
import { useCallback, useEffect, useRef, useState } from "react";
import RuleEditorTabAttributes from "./tabs/RuleEditorTabAttributes";
import RuleEditorTabAttributeOptions from "./tabs/RuleEditorTabAttributeOptions";
import RuleEditorTabDocumentation from "./tabs/RuleEditorTabDocumentation";
import { useTranslation } from "@/hooks/useTranslation";
import RuleEditorTest from "./widgets/RuleEditorTest";
import { DialogHeader } from "@/theme/styled";
import RuleEditorTabUnits from "./tabs/RuleEditorTabUnits";
import { ButtonPrimary } from "@/components/ui/Button";
import Tabs from "@/components/ui/tabs/Tabs";
import RuleEditorTabDictionaries from "@/components/rules/ruleEditor/tabs/RuleEditorTabDictionaries";

export default function RuleEditor({ onChange, defaultValue, title = "" }) {
  const { t } = useTranslation();

  const inputRef = useRef(null);

  const [open, setOpen] = useState(false);
  const [value, setValue] = useState(defaultValue);
  const [tabValue, setTabValue] = useState(0);
  const [optionsAttribute, setOptionsAttribute] = useState(null);

  useEffect(() => {
    setValue(defaultValue);
  }, [defaultValue]);

  useEffect(() => {
    if (!open) {
      setValue(defaultValue);
      setTabValue(0);
    }
  }, [open]);

  const setFocus = () => inputRef.current.focus();

  const onSeeOptions = (attribute) => {
    setTabValue(2);
    setOptionsAttribute(attribute);
  };

  useEffect(() => {
    if (optionsAttribute) {
      setOptionsAttribute(null);
    }
  }, [tabValue]);

  const save = () => {
    onChange(value);
    setOpen(false);
  };

  const handleChangeDebounce = useCallback(
    debounce((f) => {
      f();
    }, 100),
    [],
  );

  const handleChangeValue = (value) => {
    inputRef.current.value = inputRef.current.value + value;
    setValue((prev) => prev + value);
    setFocus();
  };

  const COMPONENTS = {
    [0]: <RuleEditorTabDocumentation onChange={handleChangeValue} />,
    [1]: <RuleEditorTabAttributes onSeeOptions={onSeeOptions} onChange={handleChangeValue} />,
    [2]: <RuleEditorTabAttributeOptions paginatorFilter={optionsAttribute ? { attribute: optionsAttribute?.["@id"] } : {}} onChange={handleChangeValue} />,
    [3]: <RuleEditorTabUnits onChange={handleChangeValue} />,
    [4]: <RuleEditorTabDictionaries onChange={handleChangeValue} />,
  };

  const tabs = [{
    label: "Documentation",
    component: COMPONENTS[0],
  }, {
    label: t("items.Attribute._"),
    component: COMPONENTS[1],
  }, {
    label: t("items.AttributeOption._"),
    component: COMPONENTS[2],
  }, {
    label: t("items.Unit._"),
    component: COMPONENTS[3],
  }, {
    label: t("items.Dictionary._"),
    component: COMPONENTS[4],
  }];

  return (
    <>
      <Box display="flex" justifyContent="flex-end">
        <ButtonPrimary variant="text" onClick={() => setOpen(true)} sx={{ fontSize: "90%" }}>
          {t("rules.form.open_rule_editor")}
        </ButtonPrimary>
      </Box>
      <Dialog maxWidth={false} fullWidth open={open} onClose={() => setOpen(false)} PaperProps={{ style: { height: "90vh" } }}>
        <DialogHeader onClose={() => setOpen(false)}>
          <span>{t("rule_editor._")}</span>
          {title ? <span>{title}</span> : null}
        </DialogHeader>
        <DialogContent dividers>
          <Box height="100%" display="flex" gap={2} p={2} overflow="hidden">
            <Stack height="100%" flex={1}>
              <Stack>
                <TextField multiline fullWidth autoFocus minRows={5} maxRows={25} placeholder={t("rules.form.rule_placeholder")} inputRef={inputRef} defaultValue={value} onChange={(e) => handleChangeDebounce(() => setValue(e.target.value))} />
              </Stack>
              <Divider />
              <RuleEditorTest value={value} />
            </Stack>
            <Stack flex={1} overflow="hidden">
              <Stack overflow="hidden" flex={1}>
                <Tabs tabs={tabs} value={tabValue} onTabChange={(_, value) => setTabValue(value)} />
              </Stack>
              <Stack direction="row" justifyContent="flex-end">
                <ButtonPrimary onClick={save}>{t("actions.apply")}</ButtonPrimary>
              </Stack>
            </Stack>
          </Box>
        </DialogContent>
      </Dialog>
    </>
  );
}
