import { useState } from "react";
import FormFilter from "@/components/ui/form/FormFilter";
import { Grid2 } from "@mui/material";
import { useView } from "@/contexts/ViewProvider";
import ProductFilters from "../productFilters/ProductFilters";
import InputQ from "@/components/ui/filters/InputQ";
import SelectStatus from "../selects/SelectStatus";
import SelectCatalogExist from "../selects/SelectCatalogExist";
import SelectProductFile from "../selects/SelectProductFile";
import useDidMountEffect from "@/hooks/useDidMountEffect";

export default function FormFilterProduct({ paginator }) {
  const { view, saveView } = useView();

  const productView = view?.[paginator.type];

  const [columns, setColumns] = useState(productView?.columns || []);
  const [filters, setFilters] = useState(() => ({
    sku: productView?.filters?.sku ?? [],
    status: productView?.filters?.status || "",
    catalog: productView?.filters?.catalog ?? "",
    q: productView?.filters?.q ?? "",
    filters: productView?.attributeFilters,
  }));

  const resetColumns = () => {
    setColumns(productView?.columns || []);
  };

  const submit = (o) => {
    const params = { ...filters };
    delete params.filters;
    const attributeFilters = { ...filters.filters };
    const old = { ...productView };
    if (JSON.stringify(Object.fromEntries(Object.entries(params).filter(([, value]) => value !== "" && !(Array.isArray(value) && value.length === 0)))) !== JSON.stringify(productView?.filters)) {
      old.filters = Object.fromEntries(Object.entries(params).filter(([, value]) => !(Array.isArray(value) && value.length === 0) && value !== ""));
    }
    if (JSON.stringify(attributeFilters) !== JSON.stringify(productView?.attributeFilters)) {
      old.attributeFilters = attributeFilters;
    }
    saveView(paginator.type, old);
    void paginator.submit(o);
  };

  useDidMountEffect(() => {
    submit(filters);
  }, [JSON.stringify(filters)]);

  const onChange = (value) => {
    setFilters({ ...filters, filters: value });
  };

  const changeFilter = (value) => {
    if (value === "false") {
      setFilters((o) => ({ ...o, catalog: "", "exists[catalog]": false }));
    } else {
      setFilters((o) => ({ ...o, catalog: value ? value?.replace("/api/catalogs/", "") : "", "exists[catalog]": "" }));
    }
  };

  return (
    <FormFilter onSubmit={() => submit(filters)}>
      <Grid2 container spacing={2} sx={{ width: "100%" }}>
        <Grid2 size={2}>
          <SelectStatus getterKey="@id" noLabel defaultValue={filters?.status || ""} onChange={(e) => setFilters((o) => ({ ...o, status: e ?? "" }))} />
        </Grid2>
        <Grid2 size={2}>
          <SelectCatalogExist noLabel defaultValue={filters?.catalog || ""} onChange={changeFilter} getterKey="code" />
        </Grid2>
        <Grid2 size={4}>
          <InputQ isLoading={paginator.isLoading} value={filters.q} onChange={(q) => setFilters((o) => ({ ...o, q }))} disabled={paginator.isReadonly("q")} />
        </Grid2>
        <Grid2 size={4}>
          <SelectProductFile getterKey="sku" noLabel multiple defaultValue={filters?.sku} onChange={(value) => setFilters({ ...filters, sku: value })} />
        </Grid2>
      </Grid2>
      <ProductFilters resetColumns={resetColumns} columns={columns} setColumns={setColumns} paginator={paginator} onChange={onChange} />
    </FormFilter>
  );
}
