import { useEffect, useState } from "react";
import FormFilter from "@/components/ui/form/FormFilter";
import { Grid2 } from "@mui/material";
import { useView } from "@/contexts/ViewProvider";
import ProductFilters from "../productFilters/ProductFilters";
import SelectErrorType from "../selects/SelectErrorType";
import { useSearchParams } from "react-router";
import SelectRuleGroup from "../selects/SelectRuleGroup";
import SelectLocale from "../selects/SelectLocale";
import SelectChannel from "../selects/SelectChannel";
import SelectLevel from "../selects/SelectLevel";
import SelectProductFile from "../selects/SelectProductFile";
import useDidMountEffect from "@/hooks/useDidMountEffect";
import { FilterOperatorEnum } from "@/enums";

export default function FormFilterError({ paginator }) {
  const { view, saveView } = useView();
  const documentErrorSummaryView = view?.[paginator.type];

  const [columns, setColumns] = useState(documentErrorSummaryView?.columns || []);

  const [filters, setFilters] = useState(() => ({
    sku: typeof documentErrorSummaryView?.filters?.sku === "string" ? [documentErrorSummaryView?.filters?.sku] : documentErrorSummaryView?.filters?.sku ?? [],
    level: documentErrorSummaryView?.filters?.level ?? paginator.filters?.level ?? "",
    type: documentErrorSummaryView?.filters?.type ?? paginator.filters?.type ?? "",
    ruleGroup: documentErrorSummaryView?.filters?.ruleGroup ?? paginator.filters?.ruleGroup ?? "",
    catalogScope: documentErrorSummaryView?.filters?.catalogScope ?? paginator.filters?.catalogScope ?? "",
    locale: documentErrorSummaryView?.filters?.locale ?? paginator.filters?.locale ?? "",
    filters: { filters: documentErrorSummaryView?.attributeFilters?.filters || paginator.filters?.filters || [], operator: FilterOperatorEnum.AND },
  }));

  const [searchParams] = useSearchParams();
  const type = searchParams.get("type");

  const resetColumns = () => {
    setColumns(documentErrorSummaryView?.columns || []);
  };

  const submit = (o) => {
    const params = { ...filters };
    delete params.filters;
    const attributeFilters = { ...filters.filters };
    const old = { ...documentErrorSummaryView };
    if (JSON.stringify(Object.fromEntries(Object.entries(params).filter(([, value]) => value !== "" && !(Array.isArray(value) && value.length === 0)))) !== JSON.stringify(documentErrorSummaryView?.filters)) {
      old.filters = Object.fromEntries(Object.entries(params).filter(([, value]) => !(Array.isArray(value) && value.length === 0) && value !== ""));
    }
    if (JSON.stringify(attributeFilters) !== JSON.stringify(documentErrorSummaryView?.attributeFilters)) {
      old.attributeFilters = attributeFilters;
    }
    saveView(paginator.type, old);
    void paginator.submit(o);
  };

  useDidMountEffect(() => {
    submit(filters);
  }, [filters]);

  useEffect(() => {
    if (type && type !== filters.type) {
      setFilters({ ...filters, type: type });
    } else if (!type && filters.type) {
      setFilters({ ...filters, type: "" });
    }
  }, [type]);

  const onChange = (value) => {
    setFilters({ ...filters, filters: { filters: value, operator: FilterOperatorEnum.AND } });
  };

  return (
    <FormFilter onSubmit={() => submit(filters)}>
      <Grid2 container spacing={2} sx={{ width: "100%" }}>
        <Grid2 size="grow">
          <SelectLevel getterKey="@id" noLabel defaultValue={filters?.level || ""} onChange={(evt) => setFilters({ ...filters, level: evt ?? "" })} />
        </Grid2>
        <Grid2 size="grow">
          <SelectChannel noLabel defaultValue={filters.catalogScope || ""} onChange={(val) => setFilters({ ...filters, catalogScope: val ? val.replace("/api/catalog-scopes/", "") : "" })} getterKey="code" />
        </Grid2>
        <Grid2 size="grow">
          <SelectRuleGroup getterKey="@id" noLabel onChange={(ruleGroup) => setFilters((o) => ({ ...o, ruleGroup: ruleGroup ?? "" }))} defaultValue={filters.ruleGroup} />
        </Grid2>
        <Grid2 size="grow">
          <SelectErrorType getterKey="@id" noLabel defaultValue={type || ""} onChange={(val) => setFilters({ ...filters, type: val ?? "" })} />
        </Grid2>
        <Grid2 size="grow">
          <SelectLocale getterKey="code" noLabel onChange={(evt) => setFilters((o) => ({ ...o, locale: evt ?? "" }))} defaultValue={filters?.locale} />
        </Grid2>
        <Grid2 size="grow">
          <SelectProductFile getterKey="sku" noLabel multiple defaultValue={filters.sku} onChange={(value) => setFilters({ ...filters, sku: value })} />
        </Grid2>
      </Grid2>
      <ProductFilters resetColumns={resetColumns} columns={columns} setColumns={setColumns} paginator={paginator} onChange={onChange} disableColumns />
    </FormFilter>
  );
}
