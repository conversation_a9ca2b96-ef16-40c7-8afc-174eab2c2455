import { useState } from "react";
import InputQ from "@/components/ui/filters/InputQ";
import FormFilter from "@/components/ui/form/FormFilter";
import useDidMountEffect from "@/hooks/useDidMountEffect";

export default function FormFilterDictionary({ paginator }) {
  const [filters, setFilters] = useState(() => ({
    q: paginator.filters.q ?? "",
  }));

  const submit = (o) => void paginator.submit(o);

  useDidMountEffect(() => {
    submit(filters);
  }, [filters]);

  return (
    <FormFilter onSubmit={() => submit(filters)}>
      <InputQ isLoading={paginator.isLoading} value={filters.q} onChange={(q) => setFilters((o) => ({ ...o, q }))} disabled={paginator.isReadonly("q")} />
    </FormFilter>
  );
}
