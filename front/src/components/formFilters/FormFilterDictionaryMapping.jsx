import { useState } from "react";
import InputQ from "@/components/ui/filters/InputQ";
import FormFilter from "@/components/ui/form/FormFilter";
import useDidMountEffect from "@/hooks/useDidMountEffect";
import SelectDictionary from "@/components/selects/SelectDictionary";
import { Grid2 } from "@mui/material";

export default function FormFilterDictionaryMapping({ paginator }) {
  const [filters, setFilters] = useState(() => ({
    q: paginator.filters.q ?? "",
  }));

  const submit = (o) => void paginator.submit(o);

  useDidMountEffect(() => {
    submit(filters);
  }, [filters]);

  return (
    <FormFilter onSubmit={() => submit(filters)}>
      <Grid2 container spacing={2} sx={{ width: "100%" }}>
        <Grid2 size="grow">
          <SelectDictionary getterKey="@id" noLabel defaultValue={filters?.dictionary || ""} onChange={(evt) => setFilters({ ...filters, dictionary: evt ?? "" })} />
        </Grid2>
        <Grid2 size="grow">
          <InputQ isLoading={paginator.isLoading} value={filters.q} onChange={(q) => setFilters((o) => ({ ...o, q }))} disabled={paginator.isReadonly("q")} />
        </Grid2>
      </Grid2>
    </FormFilter>
  );
}
