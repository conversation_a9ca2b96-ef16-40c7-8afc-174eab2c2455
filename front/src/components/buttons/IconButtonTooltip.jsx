import Tooltip from "@/components/ui/Tooltip";
import { IconButton } from "@mui/material";

export default function IconButtonTooltip({ onClick, tooltip, sx, size = "medium", placement = "bottom", disabled = false, children }) {
  // Prevents error in console if btn disabled
  if (disabled) {
    return (
      <IconButton onClick={onClick} size={size} disabled={disabled} sx={sx}>
        {children}
      </IconButton>
    );
  }

  return (
    <Tooltip title={tooltip} placement={placement}>
      <IconButton onClick={onClick} size={size} disabled={disabled} sx={sx}>
        {children}
      </IconButton>
    </Tooltip>
  );
}
