import { useSnack } from "@/contexts/SnackProvider";
import { useTranslation } from "@/hooks/useTranslation";
import { useState } from "react";
import DeleteIcon from "@mui/icons-material/Delete";
import { useUser } from "@/contexts/UserProvider";
import { useApi } from "@/contexts/ApiProvider";
import { ButtonError } from "../ui/Button";
import ModalError from "../modals/ModalError";

export default function ButtonDelete({ item, handleDelete = null, disabled = false, onDeleted = null }) {
  const { t } = useTranslation();
  const snack = useSnack();
  const { rights } = useUser();
  const api = useApi();

  const [isLoading, setIsLoading] = useState(false);

  const [isOpen, setIsOpen] = useState(false);

  handleDelete ??= async () => {
    try {
      setIsLoading(true);
      await api.delete(item?.["@id"]);
      onDeleted && onDeleted(item?.["@id"]);
      snack.success(t(`items.${item["@type"]}._deleted`, { label: item?.code }));
    } catch (err) {
      console.log(err);
      snack.error("errors.default");
    } finally {
      setIsLoading(false);
      setIsOpen(false);
    }
  };

  if (!rights.isEditable(item)) {
    return <span />;
  }

  return (
    <>
      <ButtonError variant="outlined" disabled={disabled} onClick={() => setIsOpen(true)} startIcon={<DeleteIcon />}>
        {t("actions.delete")}
      </ButtonError>
      <ModalError
        open={isOpen}
        onClose={() => setIsOpen(false)}
        title={t("actions.delete_permanently")}
        buttonSuccess={
          <ButtonError disabled={isLoading} onClick={handleDelete}>
            {t("actions.delete")}
          </ButtonError>
        }
      >
        {t("actions.delete_permanently_text", { name: item?.code || item?.name || t(item?.names) })}
      </ModalError>
    </>
  );
}
