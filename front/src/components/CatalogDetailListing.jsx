import GridProduct from "@/components/grid/GridProduct";
import TableProduct from "@/components/tables/TableProduct";
import Paginator from "@/components/ui/pagination/Paginator";
import LAYOUTS from "@/enums/LAYOUTS";
import { usePaginator } from "@/hooks/usePaginator";
import ItemUtils from "@/utils/item.utils";
import { generatePath, useNavigate } from "react-router";
import { useState } from "react";
import { useView } from "@/contexts/ViewProvider";
import { Stack } from "@mui/material";
import Tabs from "@/components/ui/tabs/Tabs";
import { useTranslation } from "@/hooks/useTranslation";
import FormFilterProductCatalog from "@/components/formFilters/FormFilterProductCatalog";
import useDidMountEffect from "@/hooks/useDidMountEffect";
import ContentListing from "@/router/pages/contents/ContentListing";
import MediaListing from "./MediaListing";
import { ApiTypeEnum, FilterOperatorEnum, RouteEnum } from "@/enums";

export default function CatalogDetailListing({ catalogDatas, code, tree }) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { view, saveView } = useView();

  const [order, setOrder] = useState(view?.orders?.orders?.[0]);
  const [item, setItem] = useState(null);

  const gotoProductDetail = (product) => navigate(generatePath(RouteEnum.PRODUCTS_DETAIL, { sku: product.sku }));

  const paginator = usePaginator(
    catalogDatas?.type || ApiTypeEnum.PRODUCT,
    ItemUtils.getIri(catalogDatas?.type) || "/api/products",
    tree.urlParams
      ? {
        catalog: code,
        category: tree?.current?.uuid || tree.urlParams,
      }
      : { catalog: code },
    {},
    true,
    {
      filters: { filters: view?.filters || [], operator: FilterOperatorEnum.AND },
    },
    LAYOUTS.CARD,
  );

  const saveOrder = (order) => {
    setOrder(order);
    saveView(view?.filters, view?.columns?.columns, [order]);
    paginator.submit({ ...paginator.filters, orders: [order] });
  };

  useDidMountEffect(() => {
    if (tree.current) {
      paginator.fetch({ catalog: code, category: tree.current.uuid });
    } else {
      paginator.fetch({ catalog: code });
    }
  }, [tree.current]);

  const COMPONENTS = {
    [ApiTypeEnum.PRODUCT]: (
      <>
        <Stack py={1}>
          <FormFilterProductCatalog paginator={paginator} />
        </Stack>
        <Paginator paginator={paginator} localizable>
          {paginator.view === LAYOUTS.LIST ? <TableProduct paginator={paginator} order={order} saveOrder={saveOrder} open={gotoProductDetail} /> : <GridProduct paginator={paginator} open={gotoProductDetail} />}
        </Paginator>
      </>
    ),
    [ApiTypeEnum.CONTENT]: <ContentListing paginator={paginator} content={item} setContent={(item) => setItem(item)} />,
    [ApiTypeEnum.MEDIA]: <MediaListing paginator={paginator} />,
  };

  const tabs = [
    {
      label: `${t(`items.${catalogDatas?.type}._`)} ${paginator ? `(${paginator?.count})` : ""}`,
      component: COMPONENTS[catalogDatas?.type],
    },
  ];

  return <Tabs tabs={tabs} />;
}
