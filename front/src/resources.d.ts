import type {CodeTrait, Id<PERSON><PERSON><PERSON>, <PERSON><PERSON>, JsonLdTrait, OwnerTrait, PositionTrait, StatusTrait, TimestampableTrait, TreeTrait, UploadTrait} from "@/traits";
import {ApiTypeDocumentEnum, ApiTypeDocumentWithCategoryEnum, ApiTypeEnum, AttributeTypeEnum, MembershipRoleEnum, RoleEnum, RuleLevelEnum} from "@/enums";
import type {Prettify} from "@/helpers";
import type {Translations} from "@/types/model/Translations";
import type {Filters} from "@/types/definition/SourceDefinition";
import type {Formula} from "@/types/model/Formula";
import type {Code} from "@/types/model/Code";
import type {Color} from "@/types/model/Color";

// @todo until the real one is created
export type User = Prettify<
  {
    role: RoleEnum;
    groups: {
      id: number;
      name: string;
      role: MembershipRoleEnum;
    }[];
    readonly isSso?: boolean;
  }
  & JsonLdTrait<ApiTypeEnum.USER>
  & IdTrait
  & TimestampableTrait
  & StatusTrait
>

export type UserGroup = Prettify<
  {
    catalogs: Iri<ApiTypeEnum.CATALOG>[];
    readonly attributeGroups: {
      id: number;
      scope: "read" | "write"; // @todo enum
    }[];
    readonly users: {
      id: number;
      email: string;
      firstname: string;
      lastname: string;
      role: "admin" | "admin_inherited" | "member"; // @todo enum
    }[];
    parent: Iri<ApiTypeEnum.USER_GROUP> | null;
    readonly counters: {
      readonly users: number;
      readonly groups: number;
      readonly catalogs: number;
      readonly scopes: number;
    };
  }
  & JsonLdTrait<ApiTypeEnum.USER_GROUP>
  & IdTrait
  & TimestampableTrait
  & TreeTrait
  & UploadTrait
>

export type Locale = Prettify<
  {
    names: Translations;
  }
  & JsonLdTrait<ApiTypeEnum.LOCALE>
  & CodeTrait
  & OwnerTrait
>

export type AttributeGroup = Prettify<
  {
    names: Translations;
    type: ApiTypeDocumentWithCategoryEnum;
    readonly counters: {
      readonly attribute: number;
    };
  }
  & JsonLdTrait<ApiTypeEnum.ATTRIBUTE_GROUP>
  & CodeTrait
  & StatusTrait
  & OwnerTrait
  & TimestampableTrait
>

// @todo
export type AttributeParameters = {};

export type Attribute = Prettify<
  {
    names: Translations;
    attributeGroup: Iri<ApiTypeEnum.ATTRIBUTE_GROUP>;
    type: AttributeTypeEnum;
    isRequired: boolean;
    isSearchable: boolean;
    isLocalizable: boolean;
    isScopable: boolean;
    readonly counters: {
      readonly uses: number;
      readonly products: number;
      readonly categories: number;
      readonly contents: number;
      readonly medias: number;
      readonly options: number;
    };
    parameters: AttributeParameters;
  }
  & JsonLdTrait<ApiTypeEnum.ATTRIBUTE>
  & CodeTrait
  & StatusTrait
  & OwnerTrait
  & PositionTrait
  & TimestampableTrait
>

export type Catalog = Prettify<
  {
    names: Translations;
    filters: Filters;
    type: ApiTypeDocumentEnum;
    readonly counters: {
      readonly uses: number;
      readonly products: number;
      readonly categories: number;
      readonly contents: number;
      readonly medias: number;
    }
  }
  & JsonLdTrait<ApiTypeEnum.CATALOG>
  & CodeTrait
  & UploadTrait
  & TimestampableTrait
  & StatusTrait
>

export type Currency = Prettify<
  {
    names: Translations;
    symbol: string;
  }
  & JsonLdTrait<ApiTypeEnum.CURRENCY>
  & CodeTrait
  & OwnerTrait
>

export type Scope = Prettify<
  {
    names: Translations;
  }
  & JsonLdTrait<ApiTypeEnum.SCOPE>
  & CodeTrait
  & TimestampableTrait
  & UploadTrait
  & OwnerTrait
>

export type ContentFolder = Prettify<
  {
    parent: Iri<ApiTypeEnum.CONTENT_FOLDER> | null;
    readonly counters: {
      readonly contents: number;
      readonly folders: number;
    };
  }
  & JsonLdTrait<ApiTypeEnum.CONTENT_FOLDER>
  & IdTrait
  & TimestampableTrait
  & TreeTrait
>

export type MediaFolder = Prettify<
  {
    parent: Iri<ApiTypeEnum.MEDIA_FOLDER> | null;
    readonly counters: {
      readonly contents: number;
      readonly folders: number;
    };
  }
  & JsonLdTrait<ApiTypeEnum.MEDIA_FOLDER>
  & IdTrait
  & TimestampableTrait
  & TreeTrait
>

export type RuleGroup = Prettify<
  {
    names: Translations;
    triggerCondition: Formula | null;
    readonly counters: {
      readonly rules: number;
      readonly steps: number;
    };
  }
  & JsonLdTrait<ApiTypeEnum.RULE_GROUP>
  & CodeTrait
  & TimestampableTrait
>

export type Rule = Prettify<
  {
    ruleGroup: Iri<ApiTypeEnum.RULE_GROUP>;
    names: Translations;
    descriptions: Translations;
    isBlocking: boolean;
    triggerCondition: Formula | null;
    rule: Formula;
    errorTargets: Code[];
    level: RuleLevelEnum;
  }
  & JsonLdTrait<ApiTypeEnum.RULE>
  & CodeTrait
  & TimestampableTrait
  & PositionTrait
>

export type Workflow = Prettify<
  {
    names: Translations;
    descriptions: Translations;
    statuses: Translations;
    color: Color;
    readonly counters: {
      readonly steps: number;
      readonly rules: number;
    };
  }
  & JsonLdTrait<ApiTypeEnum.WORKFLOW>
  & IdTrait
  & TimestampableTrait
>

export type WorkflowStep = Prettify<
  {
    names: Translations;
    workflow: Iri<ApiTypeEnum.WORKFLOW>;
    isBlocking: boolean;
    statuses: Translations;
    color: Color;
    readonly counters: {
      readonly rules: number;
    };
  }
  & JsonLdTrait<ApiTypeEnum.WORKFLOW_STEP>
  & IdTrait
  & TimestampableTrait
  & PositionTrait
>

export type Dictionary = Prettify<
  {
    names: Translations;
  }
  & JsonLdTrait<ApiTypeEnum.DICTIONARY>
  & CodeTrait
  & TimestampableTrait
>

export type DictionaryMapping = Prettify<
  {
    dictionary: Dictionary
    from: string;
    to: string;
  }
  & JsonLdTrait<ApiTypeEnum.DICTIONARY_MAPPING>
  & TimestampableTrait
  & UuidTrait
>