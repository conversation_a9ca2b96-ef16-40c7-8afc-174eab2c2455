import js from "@eslint/js";
import globals from "globals";
// import reactHooks from "eslint-plugin-react-hooks";
// import reactRefresh from "eslint-plugin-react-refresh";
import tseslint from "typescript-eslint";
import react from "eslint-plugin-react";

export default tseslint.config(
  { ignores: ["**/stories/**"] },
  {
    extends: [js.configs.recommended, ...tseslint.configs.recommended],
    files: ["**/*.{ts,tsx,js,jsx}"],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      react,
      // "react-hooks": reactHooks,
      // "react-refresh": reactRefresh,
    },
    rules: {
      // ...reactHooks.configs.recommended.rules,
      // "react-refresh/only-export-components": ["warn", { allowConstantExport: true }],
      "@typescript-eslint/no-empty-object-type": "off",
      "@typescript-eslint/no-duplicate-enum-values": "off",
      "prefer-const": "off",
      "@typescript-eslint/no-unused-expressions": "off",
      "react/jsx-uses-react": "error",
      "react/boolean-prop-naming": "error",
      // "react/destructuring-assignment": ["error", "never"],
      "react/function-component-definition": ["error", { namedComponents: "function-declaration" }],
      "react/hook-use-state": ["error", { allowDestructuredState: false }],
      // "react/jsx-boolean-value": ["error", "always"],
      // "react/jsx-closing-bracket-location": "error",
      "react/jsx-closing-tag-location": "error",
      "react/jsx-curly-brace-presence": "error",
      // "react/jsx-curly-newline": "error",
      "react/jsx-curly-spacing": "error",
      "react/jsx-equals-spacing": ["error", "never"],
      "react/jsx-filename-extension": [1, { extensions: [".js", ".jsx", ".ts", ".tsx"] }],
      "react/jsx-fragments": "error",
      "react/jsx-handler-names": "error",
      "react/jsx-indent": ["error", 2],
      "react/jsx-indent-props": ["error", 2],
      "react/jsx-key": "error",
      // "react/jsx-max-depth": ["error", { max: 6 }],
      // "react/jsx-max-props-per-line": ["error", { maximum: 4 }],
      "react/jsx-no-comment-textnodes": "error",
      // @todo https://github.com/jsx-eslint/eslint-plugin-react/blob/master/docs/rules/jsx-no-constructed-context-values.md
      "react/jsx-no-duplicate-props": "error",
      "react/jsx-no-leaked-render": "error",
      "react/jsx-no-literals": ["error", { allowedStrings: ["/", "(", ")", "%", "+", "):", ","] }],
      "react/jsx-no-script-url": "error",
      "react/jsx-no-target-blank": "error",
      "react/jsx-no-undef": "error",
      "react/jsx-no-useless-fragment": "error",
      // "react/jsx-one-expression-per-line": "error",
      "react/jsx-pascal-case": "error",
      "react/jsx-props-no-multi-spaces": "error",
      "react/jsx-props-no-spread-multi": "error",
      // "react/jsx-props-no-spreading": "error", // haha, ça va pleurer
      "react/jsx-tag-spacing": [
        "error",
        {
          closingSlash: "never",
          beforeSelfClosing: "always",
          afterOpening: "never",
          beforeClosing: "never",
        },
      ],
      "react/jsx-uses-vars": "error",
      "react/jsx-wrap-multilines": [
        "error",
        {
          declaration: "parens-new-line",
          assignment: "parens-new-line",
          return: "parens-new-line",
          arrow: "parens-new-line",
          condition: "parens-new-line",
          logical: "parens-new-line",
        },
      ],
      "react/no-access-state-in-setstate": "error",
      "react/no-children-prop": "error",
      "react/no-deprecated": "error",
      "react/no-invalid-html-attribute": "error",
    },
    settings: {
      react: {
        version: "detect",
      },
    },
  },
);
