const AccessorsArgs = [{
  name: "code",
  type: "string",
  required: true,
}, {
  name: "locale",
  type: "string",
}, {
  name: "scope",
  type: "string",
}];

const MultipleAccessorsArgs = [{
  name: "codes",
  type: "string[]",
  required: true,
}, {
  name: "locale",
  type: "string",
}, {
  name: "scope",
  type: "string",
}];

const AccessorsNumber = [{
  name: "value",
  type: "number",
  required: true,
}];

const functions = [{
  name: "value",
  args: AccessorsArgs,
  return: "mixed",
  type: "Accessors",
  descriptions: {
    fr_FR: "Récupère une valeur textuelle d'un attribut.",
    en_GB: "Get a value from the current product based on its attribute code.",
  },
}, {
  name: "value_text",
  args: AccessorsArgs,
  return: "string",
  type: "Accessors",
  descriptions: {
    en_GB: "Get a text value from the current product based on its attribute code.",
    fr_FR: "Obtenir une valeur textuelle du produit actuel en fonction de son code d'attribut.",
  },
  advanced: true,
}, {
  name: "value_number",
  args: AccessorsArgs,
  return: "string<number>",
  type: "Accessors",
  descriptions: {
    en_GB: "Get a number value from the current product based on its attribute code.",
    fr_FR: "Obtenir une valeur numérique du produit actuel en fonction de son code d'attribut.",
  },
  advanced: true,
}, {
  name: "dictionary_find",
  args: [
    {
      name: "dictionaryCode",
      type: "string",
      required: true,
    }, {
      name: "value",
      type: "string",
      required: true,
    }
  ],
  return: "string",
  type: "Accessors",
  descriptions: {
    en_GB: "Apply the dictionary definition for this value on a dictionary.",
    fr_FR: "Applique la définition du dictionnaire pour cette valeur en fonction de son code de dictionnaire.",
  },
  advanced: true,
}, {
  name: "dictionary_exists",
  args: [
    {
      name: "dictionaryCode",
      type: "string",
      required: true,
    }
  ],
  return: "bool",
  type: "Accessors",
  descriptions: {
    en_GB: "Check if dictionary exists on a dictionary.",
    fr_FR: "Vérifie si le dictionnaire existe en fonction de son code.",
  },
  advanced: true,
}, {
  name: "dictionary_contains",
  args: [
    {
      name: "dictionaryCode",
      type: "string",
      required: true,
    }, {
      name: "from",
      type: "string",
      required: true,
    }
  ],
  return: "bool",
  type: "Accessors",
  descriptions: {
    en_GB: "Check if definition exists on a dictionary.",
    fr_FR: "Vérifie si le dictionnaire contient une définition en particulier.",
  },
  advanced: true,
}, {
  name: "value_bool",
  args: AccessorsArgs,
  return: "bool",
  type: "Accessors",
  descriptions: {
    en_GB: "Get a bool value from the current product based on its attribute code.",
    fr_FR: "Obtenir une valeur booléenne du produit actuel en fonction de son code d'attribut.",
  },
  advanced: true,
}, {
  name: "value_date",
  args: AccessorsArgs,
  return: "date",
  type: "Accessors",
  descriptions: {
    en_GB: "Get a date value from the current product based on its attribute code.",
    fr_FR: "Obtenir une valeur de date du produit actuel en fonction de son code d'attribut.",
  },
  advanced: true,
}, {
  name: "value_metric_unit",
  args: AccessorsArgs,
  return: "string<unit>",
  type: "Accessors",
  descriptions: {
    en_GB: "Get a metric unit value from the current product based on its attribute code.",
    fr_FR: "Obtenir une unité de mesure du produit actuel en fonction de son code d'attribut.",
  },
}, {
  name: "value_metric_amount",
  args: AccessorsArgs,
  return: "string<number>",
  type: "Accessors",
  descriptions: {
    en_GB: "Get a metric amount value from the current product based on its attribute code.",
    fr_FR: "Obtenir une quantité métrique du produit actuel en fonction de son code d'attribut.",
  },
}, {
  name: "value_metric",
  args: [{
    name: "code",
    type: "string",
    required: true,
  }, {
    name: "unit",
    type: "string",
    required: true,
  }, {
    name: "locale",
    type: "string",
  }, {
    name: "scope",
    type: "string",
  }],
  return: "metric",
  type: "Accessors",
  descriptions: {
    en_GB: "Get a metric value from the current product based on its attribute code.",
    fr_FR: "Obtenir une valeur métrique du produit actuel en fonction de son code d'attribut.",
  },
  advanced: true,
}, {
  name: "values",
  args: MultipleAccessorsArgs,
  return: "mixed[]",
  type: "Accessors",
  descriptions: {
    en_GB: "Get a list of values from the current product based on its attribute codes.",
    fr_FR: "Obtenir une liste de valeurs du produit actuel en fonction de ses codes d'attributs.",
  },
}, {
  name: "values_text",
  args: MultipleAccessorsArgs,
  return: "string[]",
  type: "Accessors",
  descriptions: {
    en_GB: "Get a list of text values from the current product based on its attribute codes.",
    fr_FR: "Obtenir une liste de valeurs textuelles du produit actuel en fonction de ses codes d'attributs.",
  },
  advanced: true,
}, {
  name: "values_number",
  args: MultipleAccessorsArgs,
  return: "string<number>[]",
  type: "Accessors",
  descriptions: {
    en_GB: "Get a list of number values from the current product based on its attribute codes.",
    fr_FR: "Obtenir une liste de valeurs numériques du produit actuel en fonction de ses codes d'attributs.",
  },
  advanced: true,
}, {
  name: "values_bool",
  args: MultipleAccessorsArgs,
  return: "bool[]",
  type: "Accessors",
  descriptions: {
    en_GB: "Get a list of bool values from the current product based on its attribute codes.",
    fr_FR: "Obtenir une liste de valeurs booléennes du produit actuel en fonction de ses codes d'attributs.",
  },
  advanced: true,
}, {
  name: "values_date",
  args: MultipleAccessorsArgs,
  return: "date[]",
  type: "Accessors",
  descriptions: {
    en_GB: "Get a list of date values from the current product based on its attribute codes.",
    fr_FR: "Obtenir une liste de valeurs de dates du produit actuel en fonction de ses codes d'attributs.",
  },
  advanced: true,
}, {
  name: "values_metric_unit",
  args: MultipleAccessorsArgs,
  return: "string<unit>[]",
  type: "Accessors",
  descriptions: {
    en_GB: "Get a list of metric unit values from the current product based on its attribute codes.",
    fr_FR: "Obtenir une liste d'unités de mesure du produit actuel en fonction de ses codes d'attributs.",
  },
  advanced: true,
}, {
  name: "values_metric_amount",
  args: MultipleAccessorsArgs,
  return: "string<number>[]",
  type: "Accessors",
  descriptions: {
    en_GB: "Get a list of metric amount values from the current product based on its attribute codes.",
    fr_FR: "Obtenir une liste de quantités métriques du produit actuel en fonction de ses codes d'attributs.",
  },
  advanced: true,
}, {
  name: "has_value",
  args: AccessorsArgs,
  return: "bool",
  type: "Accessors",
  descriptions: {
    en_GB: "Test if a value exists.",
    fr_FR: "Tester si une valeur existe.",
  },
}, {
  name: "has_value_text",
  args: AccessorsArgs,
  return: "bool",
  type: "Accessors",
  advanced: true,
}, {
  name: "has_value_number",
  args: AccessorsArgs,
  return: "bool",
  type: "Accessors",
  advanced: true,
}, {
  name: "has_value_bool",
  args: AccessorsArgs,
  return: "bool",
  type: "Accessors",
  advanced: true,
}, {
  name: "has_value_date",
  args: AccessorsArgs,
  return: "bool",
  type: "Accessors",
  advanced: true,
}, {
  name: "has_value_metric_unit",
  args: AccessorsArgs,
  return: "bool",
  type: "Accessors",
  advanced: true,
}, {
  name: "has_value_metric_amount",
  args: AccessorsArgs,
  return: "bool",
  type: "Accessors",
  advanced: true,
}, {
  name: "is_locale",
  return: "bool",
  args: [
    {
      name: "locale",
      type: "string",
      required: true,
    },
  ],
  type: "bool",
  descriptions: {
    en_GB: "Check if the workflow is executed in specific language",
    fr_FR: "Vérifie si le worklow est joué dans une langue spécifique",
  },
}, {
  name: "is_channel",
  return: "bool",
  args: [
    {
      name: "channel",
      type: "string",
      required: true,
    },
  ],
  type: "bool",
  descriptions: {
    en_GB: "Check if the workflow is executed in specific channel",
    fr_FR: "Vérifie si le worklow est joué dans un channel spécifique",
  },
}, {
  name: "count_existing_values",
  args: [{
    name: "codes",
    type: "string",
    required: true,
  }, {
    name: "locale",
    type: "string",
  }, {
    name: "scope",
    type: "string",
  }],
  return: "int",
  type: "Accessors",
}, ...["string", "numeric", "integer", "float", "bool", "array"].map(type => ({
  name: `is_${type}`,
  args: [{
    name: "value",
    required: true,
  }],
  return: "bool",
  type: "Format tester",
  description: `Test if a value is of type "${type}".`,
})),{
  name: "option_name",
  args: [
    {
      name: "code",
      type: "string",
      required: true,
    }, {
      name: "optionLocaleCode",
      type: "string",
      required: true,
    }, {
      name: "locale",
      type: "string",
    }, {
      name: "scope",
      type: "string",
    }
  ],
  return: "?string",
  type: "String",
  descriptions: {
    en_GB: "Get a localized value for a select.",
    fr_FR: "Retourne la valeur localisée d'une liste déroulante.",
  },
}, {
  name: "string_length",
  args: [{
    name: "value",
    type: "string",
    required: true,
  }],
  return: "int",
  type: "String",
  descriptions: {
    en_GB: "Get string length.",
  },
}, {
  name: "string_lowercase",
  args: [{
    name: "value",
    type: "string",
    required: true,
  }],
  return: "string",
  type: "String",
  descriptions: {
    en_GB: "Make a string lowercase.",
  },
}, {
  name: "string_uppercase",
  args: [{
    name: "value",
    type: "string",
    required: true,
  }],
  return: "string",
  type: "String",
  descriptions: {
    en_GB: "Make a string uppercase.",
  },
}, {
  name: "string_starts_with",
  args: [{
    name: "value",
    type: "string",
    required: true,
  }, {
    name: "pattern",
    type: "string",
    required: true,
  }],
  return: "bool",
  type: "String",
  descriptions: {
    en_GB: "Checks if a string starts with a given substring.",
  },
}, {
  name: "string_ends_with",
  args: [{
    name: "value",
    type: "string",
    required: true,
  }, {
    name: "pattern",
    type: "string",
    required: true,
  }],
  return: "bool",
  type: "String",
  descriptions: {
    en_GB: "Checks if a string ends with a given substring.",
  },
}, {
  name: "string_to_number",
  args: [{
    name: "value",
    type: "string<number>",
    required: true,
  }],
  return: "number",
  type: "String",
}, {
  name: "string_slugify",
  args: [{
    name: "value",
    type: "string",
    required: true,
  }],
  return: "String",
  type: "String",
}, {
  name: "min",
  args: [{
    name: "value1",
    type: "number",
    required: true,
  }, {
    name: "value2",
    type: "string",
    required: true,
  }],
  return: "number",
  type: "Numeric",
  descriptions: {
    fr_FR: "Trouver la valeur la plus basse.",
    en_GB: "Find lowest value.",
  },
}, {
  name: "max",
  args: [{
    name: "value1",
    type: "number",
    required: true,
  }, {
    name: "value2",
    type: "string",
    required: true,
  }],
  return: "number",
  type: "Numeric",
  descriptions: {
    fr_FR: "Trouver la valeur la plus élevée.",
    en_GB: "Find highest value.",
  },
}, {
  name: "abs",
  args: AccessorsNumber,
  return: "number",
  type: "Numeric",
  descriptions: {
    en_GB: "Get absolute value.",
    fr_FR: "Obtenez la valeur absolue.",
  },
}, {
  name: "round",
  args: AccessorsNumber,
  return: "number",
  type: "Numeric",
  descriptions: {
    fr_FR: "Arrondit un float.",
    en_GB: "Rounds a float.",
  },
}, {
  name: "ceil",
  args: AccessorsNumber,
  return: "number",
  type: "Numeric",
  descriptions: {
    en_GB: "Round fractions up.",
    fr_FR: "Arrondit à l'entier supérieur.",
  },
}, {
  name: "floor",
  args: AccessorsNumber,
  return: "number",
  type: "Numeric",
  descriptions: {
    fr_FR: "Arrondit à l'entier inférieur.",
    en_GB: "Round fractions down.",
  },
}, {
  name: "string_number_precision",
  args: [{
    name: "value",
    type: "string<number>",
    required: true,
  }],
  return: "int",
  type: "Numeric",
}, {
  name: "string_number_thresholds_are",
  type: "Numeric",
  args: [{
    name: "value",
    type: "string<number>",
    required: true,
  }, {
    name: "min",
    type: "int",
    required: false,
  }, {
    name: "max",
    type: "int",
    required: false,
  }, {
    name: "precision",
    type: "int",
    required: false,
  }],
  return: "bool",
  descriptions: {
    en_GB: "Check if a number is between min and max and has a valid precision.",
    fr_FR: "Vérifier si un nombre est compris entre min et max et possède une précision valide.",
  },
}, {
  name: "string_numbers_add",
  args: [{
    name: "value1",
    type: "string<number>",
    required: true,
  }, {
    name: "value2",
    type: "string<number>",
    required: true,
  }],
  return: "string<number>",
  type: "Numeric",
  descriptions: {
    en_GB: "Add two arbitrary precision numbers (stored in strings).",
    fr_FR: "Ajoutez deux nombres à précision arbitraire (stockés sous forme de chaînes).",
  },
}, {
  name: "string_numbers_comp",
  args: [{
    name: "value1",
    type: "string<number>",
    required: true,
  }, {
    name: "value2",
    type: "string<number>",
    required: true,
  }],
  return: "string<number>",
  type: "Numeric",
  descriptions: {
    fr_FR: "Comparer deux nombres à précision arbitraire (stockés sous forme de chaînes).",
    en_GB: "Compare two arbitrary precision numbers (stored in strings).",
  },
}, {
  name: "string_numbers_div",
  args: [{
    name: "value1",
    type: "string<number>",
    required: true,
  }, {
    name: "value2",
    type: "string<number>",
    required: true,
  }],
  return: "string<number>",
  type: "Numeric",
  descriptions: {
    en_GB: "Divide two arbitrary precision numbers (stored in strings).",
    fr_FR: "Divisez deux nombres à précision arbitraire (stockés sous forme de chaînes).",
  },
}, {
  name: "string_numbers_mul",
  args: [{
    name: "value1",
    type: "string<number>",
    required: true,
  }, {
    name: "value2",
    type: "string<number>",
    required: true,
  }],
  return: "string<number>",
  type: "Numeric",
  descriptions: {
    fr_FR: "Multipliez deux nombres à précision arbitraire (stockés sous forme de chaînes).",
    en_GB: "Multiply two arbitrary precision numbers (stored in strings).",
  },
}, {
  name: "string_numbers_sub",
  args: [{
    name: "value1",
    type: "string<number>",
    required: true,
  }, {
    name: "value2",
    type: "string<number>",
    required: true,
  }],
  return: "string<number>",
  type: "Numeric",
  descriptions: {
    en_GB: "Subtract one arbitrary precision number from another (stored in strings).",
    fr_FR: "Soustrayez un nombre à précision arbitraire d'un autre (stockés sous forme de chaînes).",
  },
}, {
  name: "array_contains",
  args: [{
    name: "value",
    required: true,
  }, {
    name: "array",
    type: "array",
    required: true,
  }],
  return: "bool",
  type: "Array",
  descriptions: {
    en_GB: "Checks if a value exists in an array.",
    fr_FR: "Vérifiez si une valeur existe dans un tableau.",
  },
}, {
  name: "array_unique",
  args: [{
    name: "value",
    type: "array",
    required: true,
  }],
  return: "array",
  type: "Array",
  descriptions: {
    fr_FR: "Supprimez les valeurs en double d'un tableau.",
    en_GB: "Removes duplicate values from an array.",
  },
}, {
  name: "array_count",
  args: [{
    name: "value",
    type: "array",
    required: true,
  }],
  return: "int",
  type: "Numeric",
  descriptions: {
    en_GB: "Counts all elements in an array.",
    fr_FR: "Comptez tous les éléments d'un tableau.",
  },
}, {
  name: "date_now",
  args: [],
  return: "DateTime",
  type: "Date",
  descriptions: {
    en_GB: "Generate a date (when the workflow is executed).",
    fr_FR: "Générer une date (lors de l'exécution du workflow).",
  },
}, {
  name: "in_days",
  args: [{
    name: "value",
    type: "string<datetime>",
    required: "true",
  }],
  return: "int",
  type: "Date",
  descriptions: {
    en_GB: "Get the number of days until a date (when the workflow is executed).",
    fr_FR: "Obtenez le nombre de jours jusqu'à une date (lors de l'exécution du workflow).",
  },
}, {
  name: "map",
  args: [{
    name: "entry_value",
    type: "string<number>",
    required: true,
  }, {
    name: "mapping",
    type: "array",
    required: true
  }, {
    name: "default",
    type: "string<any>",
    required: false
  }],
  return: "string",
  type: "String",
  descriptions: {
    en_GB: "Get the value contained in the array for the specified key.",
    fr_FR: "Obtenez la valeur contenue dans le tableau pour la clé spécifié.",
  }
}, {
  name: "array_intersect",
  args: [{
    name: "array1",
    type: "array",
    required: true,
  }, {
    name: "array2",
    type: "array",
    required: true
  }, {
    name: "...array",
    type: "array",
    required: false
  }],
  return: "array",
  descriptions: {
    en_GB: "Returns an array with values that appear in all given arrays.",
    fr_FR: "Retourne un tableau avec les valeurs présentes dans tous les tableaux donnés."
  }
}];

export default functions;
