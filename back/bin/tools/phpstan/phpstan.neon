parameters:
    # @todo goto 8?
    level: 7
    paths:
        - ../../../src
    excludePaths:
        - ../../../src/Connector/Shopify/Exporter/ProductExporter.php
    doctrine:
        objectManagerLoader: object-manager-loader.php
    symfony:
        containerXmlPath: ../../../var/cache/dev/App_KernelDevDebugContainer.xml
    ignoreErrors:
        # avoid Symfony mapping errors
        - '#but database expects#'
        # avoid elastic errors
        # - Call to an undefined method Elastic\Elasticsearch\Response\Elasticsearch|Http\Promise\Promise::asBool().
        # - Call to an undefined method Elastic\Elasticsearch\Response\Elasticsearch|Http\Promise\Promise::asArray().
        - '#Call to an undefined method Elastic#'
        - identifier: missingType.generics
        - identifier: missingType.iterableValue
        - '#\Configs#' # for now
    parallel:
        maximumNumberOfProcesses: 4
