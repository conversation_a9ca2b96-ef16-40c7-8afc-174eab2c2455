<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250603120822 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            CREATE TABLE dictionary (code VARCHAR(255) NOT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, names_fr VARCHAR(1000) DEFAULT NULL, names_en VARCHAR(1000) DEFAULT NULL, PRIMARY KEY(code)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE dictionary_mapping (uuid BINARY(16) NOT NULL COMMENT '(DC2Type:uuid)', dictionary_code VARCHAR(255) NOT NULL, mapping_from VARCHAR(255) NOT NULL, mapping_to VARCHAR(255) NOT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, UNIQUE INDEX UNIQ_A8833E2B79593180 (mapping_from), INDEX IDX_A8833E2B913278D3 (dictionary_code), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE dictionary_mapping ADD CONSTRAINT FK_A8833E2B913278D3 FOREIGN KEY (dictionary_code) REFERENCES dictionary (code) ON DELETE CASCADE
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE dictionary_mapping DROP FOREIGN KEY FK_A8833E2B913278D3
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE dictionary
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE dictionary_mapping
        SQL);
    }
}
