<?php declare(strict_types=1);

namespace DoctrineMigrations;

use App\Bridge\Filter\Model\Filter;
use App\Bridge\Filter\Model\Filters;
use App\Contracts\Enum\TableEnum;
use App\Contracts\Model\Header;
use App\Utils\JsonUtils;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250604100201 extends AbstractMigration
{
    /**
     * Migrations to update Filter to their new version
     */
    public function up(Schema $schema): void
    {
        $tables = [TableEnum::CATALOG => 'code', TableEnum::CATEGORY => 'uuid', TableEnum::FLAT_CATEGORY => 'uuid', TableEnum::VIEW => 'id'];
        foreach ($tables as $table => $identifier) {
            $res = $this->connection->executeQuery("SELECT {$identifier} as identifier, filters FROM {$table}");

            while ($row = $res->fetchAssociative()) {
                $this->connection->executeStatement(
                    "UPDATE {$table} SET filters = ? WHERE {$identifier} = ?",
                    [JsonUtils::encode($this->fixFilters(JsonUtils::decode($row['filters']))), $row['identifier']]
                );
            }
        }
    }

    private function fixFilters(array $data): array
    {
        $filters = [];

        if (isset($data['filters'])) {
            foreach ($data['filters'] as $filter) {
                if (isset($filter['code'])) {
                    if ($filter['type'] === 'property') {
                        $header = Header::property($filter['code']);
                    } else {
                        // scope is not forgotten, it was not functional
                        $header = Header::attribute($filter['code'], $filter['locale'] ?? null);
                    }

                    $filters[] = [
                        '#type' => Filter::TYPE,
                        'header' => [
                            '#type' => Header::TYPE,
                            "#scalar" => $header->toString(),
                        ],
                        'operator' => $filter['operator'],
                        'value' => $filter['value'],
                    ];
                } elseif (isset($filter['filters'])) {
                    $filters[] = $this->fixFilters($filter);
                }
            }
        }

        return [
            '#type' => Filters::TYPE,
            'filters' => $filters,
            'operator' => $data['operator'],
        ];
    }
}
