.PHONY: container-database-create

container-database-create-4f0e9aa5be818be6660b8b486f48d731:
	./bin/console cache:pool:clear --all
	./bin/console cache:clear
	./bin/console do:da:dr --force
	./bin/console do:da:cr
	curl -s -L -o dev.zip "https://drive.usercontent.google.com/u/0/uc?id=1rN5xCftFAM1tAHT_fhBpZCEwlFJLBUuX&export=download"
	unzip -o dev.zip
	rm dev.zip
	unzip -o api.sql.zip
	rm api.sql.zip
	mysql --init-command="SET SESSION FOREIGN_KEY_CHECKS=0;" -h "${MYSQL_HOST}" --port "${MYSQL_PORT}" -u "${MYSQL_USER}" -p"${MYSQL_PASSWORD}" "${MYSQL_DATABASE}" < api.sql
	rm api.sql
	rm -rf private/files/cloud/
	mkdir -p private/files/cloud/Media/
	unzip -o medias.zip
	mv medias/* private/files/cloud/Media/
	rm -rf medias/ medias.zip
	rm -rf public/uploads/
	mkdir -p private/files/cloud/Upload/
	unzip -o uploads.zip
	mv uploads/* private/files/cloud/Upload/
	rm -rf uploads/ uploads.zip
	./bin/console doctrine:migrations:migrate -n
	./bin/console app:contracts:refresh

container-fixtures-load-4f0e9aa5be818be6660b8b486f48d731:
	./bin/console hautelook:fixtures:load -n

export-4f0e9aa5be818be6660b8b486f48d731:
	rm -rf api.sql.zip medias.zip uploads.zip private/files/
	./bin/console hautelook:fixtures:load -n -vv
	./bin/console app:contracts:refresh
	mysqldump -h "${MYSQL_HOST}" --port "${MYSQL_PORT}" -u "${MYSQL_USER}" -p"${MYSQL_PASSWORD}" "${MYSQL_DATABASE}" > api.sql
	zip -9q api.sql.zip api.sql
	rm api.sql
	cd public/ && zip -9qr medias.zip medias/ && mv medias.zip ..
	cd public/ && zip -9qr uploads.zip uploads/ && mv uploads.zip ..
	zip -9q dev.zip api.sql.zip medias.zip uploads.zip
	rm -f api.sql.zip medias.zip uploads.zip

reset-4f0e9aa5be818be6660b8b486f48d731:
	rm -rf private/*
	./bin/console doctrine:database:drop --if-exists --force
	./bin/console doctrine:database:create
	./bin/console doctrine:migrations:migrate -n
	./bin/console cache:pool:clear --all
	./bin/console cache:clear
	./bin/console app:bridge:elastic:reindex
	./bin/console app:contracts:refresh

test-4f0e9aa5be818be6660b8b486f48d731:
	./bin/console doctrine:database:drop --force --if-exists -e test
	./bin/console doctrine:database:create -e test
	./bin/console doctrine:schema:update --complete --force -e test
	XDEBUG_MODE=coverage ./bin/phpunit --coverage-html=tests/coverage tests/
