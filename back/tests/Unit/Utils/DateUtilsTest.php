<?php declare(strict_types=1);

namespace Tests\Unit\Utils;

use App\Utils\DateUtils;
use DateTime;
use PHPUnit\Framework\TestCase;

class DateUtilsTest extends TestCase
{
    public function testNow(): void
    {
        self::assertInstanceOf(DateTime::class, DateUtils::now());
    }

    public function testToDateTime(): void
    {
        self::assertNull(DateUtils::new(null));
        self::assertNull(DateUtils::new('wrong'));
        self::assertInstanceOf(DateTime::class, DateUtils::new('now'));
        self::assertEquals('1988-01-16', DateUtils::new('1988-01-16')?->format('Y-m-d'));
    }
}
