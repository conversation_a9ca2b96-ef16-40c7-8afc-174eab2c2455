<?php declare(strict_types=1);

namespace Tests\Bridge\Workflow;

use App\Bridge\Flat\Model\Values;
use App\Bridge\Workflow\WorkflowsElFactory;
use App\Contracts\Model\Document;
use App\Contracts\Model\Metric;
use App\Contracts\Type\Enum\AttributeTypeEnum;
use App\Module\Cache\DoctrineCache;
use DateTime;
use Factory\DictionaryMappingFactory;
use Generator;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\ExpressionLanguage\ExpressionLanguage;
use Symfony\Component\Uid\Uuid;
use Tests\Factory\AttributeFactory;
use Tests\Factory\Model\ValueDataFactory;
use Tests\Factory\Model\ValueFactory;
use Tests\Factory\UnitFactory;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;
use function is_bool;
use function is_callable;

class WorkflowValueRuleTest extends KernelTestCase
{
    use ResetDatabase;
    use Factories;

    private ExpressionLanguage $el;
    private Document $document;

    protected function setUp(): void
    {
        parent::setUp();
        $container = static::getContainer();
        $doctrineCache = $container->get(DoctrineCache::class);
        $workflowsElFactory = $container->get(WorkflowsElFactory::class);
        $this->el = $workflowsElFactory->createEl('FR', 'FR');

        UnitFactory::new(['code' => 'DAY', 'symbol' => 'd'])->create();
        $this->document = new Document(Uuid::v4());
        $values = [];
        foreach (self::factoryProvider() as $factory) {
            [$attributeCode, $value, $type] = $factory;

            $attribute = AttributeFactory::new([
                'code' => $attributeCode,
                'type' => $type,
                'isLocalizable' => true,
                'isScopable' => true,
            ])->create();

            $values[] = ValueFactory::new([
                'code' => $attribute->code,
                'data' => [
                    ValueDataFactory::new([
                        'locale' => 'FR',
                        'scope' => 'FR',
                        'data' => $this->fakeSerializer($value),
                    ])->create(),
                ],
            ])->create();

            if ('attr_dico' === $attributeCode) {
                DictionaryMappingFactory::new([
                    'from' => 'value',
                    'to' => 'expected',
                ])->create();

                $doctrineCache->dictionaries->warmup();
            }
        }

        $this->document->sku = 'SKU_0001';
        $this->document->values = new Values($values);

        $doctrineCache->attributes->warmup();
        $doctrineCache->units->warmup();
    }

    /**
     * @dataProvider expressionProvider
     */
    public function testValueRule(mixed $expected, string $expression): void
    {
        $result = $this->el->evaluate($expression, ['document' => $this->document]);

        if (is_bool($expected)) {
            $expected ? self::assertTrue($result) : self::assertFalse($result);
        } elseif (is_callable($expected)) {
            self::assertTrue($expected($result));
        } else {
            self::assertEquals($expected, $result);
        }
    }

    public static function expressionProvider(): Generator
    {
        yield [2, 'count_existing_values(["attr_bool", "attr_text"])'];
        // has_value
        yield [true, 'has_value("attr_bool")'];
        yield [false, 'has_value("fake")'];
        yield [true, 'has_value_bool("attr_bool")'];
        yield [false, 'has_value_bool("attr_text")'];
        yield [true, 'has_value_date("attr_date")'];
        yield [false, 'has_value_date("attr_text")'];
        yield [false, 'has_value_metric_amount("attr_text")'];
        yield [false, 'has_value_metric_unit("attr_text")'];
        yield [true, 'has_value_number("attr_number")'];
        yield [false, 'has_value_number("attr_text")'];
        yield [true, 'has_value_text("attr_text")'];
        // value
        yield [true, 'value_bool("attr_bool")'];
        yield [new DateTime('2025-04-04'), 'value_date("attr_date")'];
        yield [12.3, 'value_number("attr_number")'];
        yield [__CLASS__, 'value_text("attr_text")'];
        // values
        yield [[__CLASS__, 12.3], 'values(["attr_text", "attr_number"])'];
        yield [[true], 'values_bool(["attr_bool"])'];
        yield [[true], 'values_bool(["attr_bool", "attr_text"])'];
        yield [[new DateTime('2025-04-04')], 'values_date(["attr_date"])'];
        yield [[new DateTime('2025-04-04')], 'values_date(["attr_date", "attr_text"])'];
        yield [[12.3], 'values_number(["attr_number"])'];
        yield [[12.3], 'values_number(["attr_number", "attr_text"])'];
        yield [[__CLASS__], 'values_text(["attr_text"])'];
        // metrics
        yield [true, 'has_value_metric_amount("attr_metric")'];
        yield [true, 'has_value_metric_unit("attr_metric")'];
        yield [12, 'value_metric("attr_metric", "DAY")'];
        yield [12, 'value_metric_amount("attr_metric")'];
        yield ['DAY', 'value_metric_unit("attr_metric")'];
        yield [[12], 'values_metric_amount(["attr_metric"])'];
        yield [['DAY'], 'values_metric_unit(["attr_metric"])'];
        // dictionary
        yield ['expected', 'dictionary_find("dico", value_text("attr_dico"))'];
        yield [true, 'dictionary_exists("dico")'];
        yield [true, 'dictionary_contains("dico", value_text("attr_dico"))'];
    }

    public static function factoryProvider(): Generator
    {
        yield ['attr_dico', 'value', AttributeTypeEnum::TEXT];
        yield ['attr_bool', true, AttributeTypeEnum::SWITCH];
        yield ['attr_text', __CLASS__, AttributeTypeEnum::TEXT];
        yield ['attr_date', '2025-04-04', AttributeTypeEnum::DATE];
        yield ['attr_metric', new Metric(amount: '12', unit: 'DAY', symbol: 'd'), AttributeTypeEnum::METRIC];
        yield ['attr_number', 12.3, AttributeTypeEnum::NUMBER];
    }

    private function fakeSerializer(mixed $value): mixed
    {
        if ($value instanceof Metric) {
            return [
                Metric::KEY_SYMBOL => $value->symbol,
                Metric::KEY_AMOUNT => $value->amount,
                Metric::KEY_UNIT => $value->unit,
            ];
        }

        return $value;
    }
}
