<?php declare(strict_types=1);

namespace Tests\Bridge\Counter;

use App\Bridge\Counter\Counter\AttributeOptionCounter;
use App\Bridge\Counter\Entity\AttributeOptionCounters;
use DateTime;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Tests\Factory\AttributeOptionFactory;
use Tests\Factory\CategoryDataFactory;
use Tests\Factory\ContentDataFactory;
use Tests\Factory\MediaDataFactory;
use Tests\Factory\ProductDataFactory;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;

class SyncAttributeOptionTest extends KernelTestCase
{
    private const array COUNTERS_PROPERTIES = ['uses', 'categories', 'contents', 'medias', 'updatedAt'];
    use ResetDatabase;
    use Factories;

    public function testValidSync(): void
    {
        /** @var AttributeOptionCounter $counter */
        $counter = self::getContainer()->get(AttributeOptionCounter::class);

        $attributeOpt = AttributeOptionFactory::new()->create();
        ProductDataFactory::new(['attributeOption' => $attributeOpt])->many(1)->create();
        CategoryDataFactory::new(['attributeOption' => $attributeOpt])->many(2)->create();
        ContentDataFactory::new(['attributeOption' => $attributeOpt])->many(3)->create();
        MediaDataFactory::new(['attributeOption' => $attributeOpt])->many(4)->create();

        $counter->executeSync([$attributeOpt->id]);

        $attributeOpt->_real();

        self::assertEquals(10, $attributeOpt->counters->uses);
        self::assertEquals(1, $attributeOpt->counters->products);
        self::assertEquals(2, $attributeOpt->counters->categories);
        self::assertEquals(3, $attributeOpt->counters->contents);
        self::assertEquals(4, $attributeOpt->counters->medias);
        self::assertEquals(DateTime::class, $attributeOpt->counters->updatedAt::class);
        self::assertCount(0, array_diff(self::COUNTERS_PROPERTIES, array_keys(get_class_vars(AttributeOptionCounters::class))));
    }
}
