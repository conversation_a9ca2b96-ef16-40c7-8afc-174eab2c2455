<?php declare(strict_types=1);

namespace Tests\Bridge\Counter;

use App\Bridge\Counter\Counter\CategoryCounter;
use App\Bridge\Counter\Entity\CategoryCounters;
use DateTime;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Tests\Factory\CategoryContentFactory;
use Tests\Factory\CategoryFactory;
use Tests\Factory\CategoryMediaFactory;
use Tests\Factory\CategoryProductFactory;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;

class SyncCategoryTest extends KernelTestCase
{
    private const array COUNTERS_PROPERTIES = ['uses', 'products', 'contents', 'medias', 'updatedAt'];
    use ResetDatabase;
    use Factories;

    public function testValidSync(): void
    {
        /** @var CategoryCounter $counter */
        $counter = self::getContainer()->get(CategoryCounter::class);

        $category = CategoryFactory::new()->create();
        CategoryProductFactory::new(['category' => $category])->many(1)->create();
        CategoryContentFactory::new(['category' => $category])->many(2)->create();
        CategoryMediaFactory::new(['category' => $category])->many(3)->create();

        $counter->executeSync([$category->uuid->toBinary()]);

        $category->_real();

        self::assertEquals(6, $category->counters->uses);
        self::assertEquals(1, $category->counters->products);
        self::assertEquals(2, $category->counters->contents);
        self::assertEquals(3, $category->counters->medias);
        self::assertEquals(DateTime::class, $category->counters->updatedAt::class);
        self::assertCount(0, array_diff(self::COUNTERS_PROPERTIES, array_keys(get_class_vars(CategoryCounters::class))));
    }
}
