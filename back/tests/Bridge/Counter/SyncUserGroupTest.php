<?php declare(strict_types=1);

namespace Tests\Bridge\Counter;

use App\Bridge\Counter\Counter\UserGroupCounter;
use App\Bridge\Counter\Entity\UserGroupCounters;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Tests\Factory\CatalogFactory;
use Tests\Factory\UserGroupFactory;
use Tests\Factory\UserGroupMembershipFactory;
use Tests\Factory\UserGroupScopeFactory;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;

class SyncUserGroupTest extends KernelTestCase
{
    private const array COUNTERS_PROPERTIES = ['users', 'groups', 'catalogs', 'scopes', 'updatedAt'];
    use ResetDatabase;
    use Factories;

    public function testValidSync(): void
    {
        /** @var UserGroupCounter $counter */
        $counter = self::getContainer()->get(UserGroupCounter::class);

        $catalog = CatalogFactory::new()->create();
        $userGrp = UserGroupFactory::new(['catalogs' => new ArrayCollection([$catalog->_real()])])->create();
        UserGroupMembershipFactory::new(['group' => $userGrp])->many(2)->create();
        UserGroupFactory::new(['parent' => $userGrp])->many(3)->create();
        UserGroupScopeFactory::new(['group' => $userGrp])->many(4)->create();
        $userGrp->_save();

        $counter->executeSync([$userGrp->id]);

        $userGrp->_real();

        self::assertEquals(2, $userGrp->counters->users);
        self::assertEquals(3, $userGrp->counters->groups);
        self::assertEquals(1, $userGrp->counters->catalogs);
        self::assertEquals(4, $userGrp->counters->scopes);
        self::assertEquals(DateTime::class, $userGrp->counters->updatedAt::class);
        self::assertCount(0, array_diff(self::COUNTERS_PROPERTIES, array_keys(get_class_vars(UserGroupCounters::class))));
    }
}
