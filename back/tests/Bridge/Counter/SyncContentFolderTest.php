<?php declare(strict_types=1);

namespace Tests\Bridge\Counter;

use App\Bridge\Counter\Counter\ContentFolderCounter;
use App\Bridge\Counter\Entity\ContentFolderCounters;
use DateTime;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Tests\Factory\ContentFactory;
use Tests\Factory\ContentFolderFactory;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;

class SyncContentFolderTest extends KernelTestCase
{
    private const array COUNTERS_PROPERTIES = ['contents', 'folders', 'updatedAt'];
    use ResetDatabase;
    use Factories;

    public function testValidSync(): void
    {
        /** @var ContentFolderCounter $counter */
        $counter = self::getContainer()->get(ContentFolderCounter::class);

        $contentFolder = ContentFolderFactory::new()->create();
        ContentFactory::new(['folder' => $contentFolder])->many(1)->create();
        ContentFolderFactory::new(['parent' => $contentFolder])->many(2)->create();

        $counter->executeSync([$contentFolder->id]);

        $contentFolder->_real();

        self::assertEquals(1, $contentFolder->counters->contents);
        self::assertEquals(2, $contentFolder->counters->folders);
        self::assertEquals(DateTime::class, $contentFolder->counters->updatedAt::class);
        self::assertCount(0, array_diff(self::COUNTERS_PROPERTIES, array_keys(get_class_vars(ContentFolderCounters::class))));
    }
}
