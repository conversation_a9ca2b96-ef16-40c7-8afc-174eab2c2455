<?php declare(strict_types=1);

namespace Tests\Bridge\Counter;

use App\Bridge\Counter\Counter\WorkflowCounter;
use App\Bridge\Counter\Entity\WorkflowCounters;
use DateTime;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Tests\Factory\RuleFactory;
use Tests\Factory\WorkflowFactory;
use Tests\Factory\WorkflowStepFactory;
use Tests\Factory\WorkflowStepRuleGroupFactory;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;

class SyncWorkflowTest extends KernelTestCase
{
    private const array COUNTERS_PROPERTIES = ['steps', 'rules', 'updatedAt'];
    use ResetDatabase;
    use Factories;

    public function testValidSync(): void
    {
        /** @var WorkflowCounter $counter */
        $counter = self::getContainer()->get(WorkflowCounter::class);

        $workflow = WorkflowFactory::new()->create();
        $steps = WorkflowStepFactory::new(['workflow' => $workflow])->many(3)->create();
        foreach ($steps as $step) {
            $workflowstepRuleGrp = WorkflowStepRuleGroupFactory::new(['workflowStep' => $step])->create();
            RuleFactory::new(['ruleGroup' => $workflowstepRuleGrp->ruleGroup])->many(4)->create();
        }

        $counter->executeSync([$workflow->id]);

        $workflow->_real();

        self::assertEquals(3, $workflow->counters->steps);
        self::assertEquals(12, $workflow->counters->rules);
        self::assertEquals(DateTime::class, $workflow->counters->updatedAt::class);
        self::assertCount(0, array_diff(self::COUNTERS_PROPERTIES, array_keys(get_class_vars(WorkflowCounters::class))));
    }
}
