<?php declare(strict_types=1);

namespace Tests\Bridge\Counter;

use App\Bridge\Counter\Counter\AttributeGroupCounter;
use App\Bridge\Counter\Entity\AttributeGroupCounters;
use DateTime;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Tests\Factory\AttributeFactory;
use Tests\Factory\AttributeGroupFactory;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;

class SyncAttributeGroupTest extends KernelTestCase
{
    private const array COUNTERS_PROPERTIES = ['attributes', 'updatedAt'];
    use ResetDatabase;
    use Factories;

    public function testValidSync(): void
    {
        /** @var AttributeGroupCounter $counter */
        $counter = self::getContainer()->get(AttributeGroupCounter::class);

        $attributeGrp = AttributeGroupFactory::new()->create();
        AttributeFactory::new(['attributeGroup' => $attributeGrp])->many(3)->create();

        $counter->executeSync([$attributeGrp->code]);

        $attributeGrp->_real();

        self::assertEquals(3, $attributeGrp->counters->attributes);
        self::assertEquals(DateTime::class, $attributeGrp->counters->updatedAt::class);
        self::assertCount(0, array_diff(self::COUNTERS_PROPERTIES, array_keys(get_class_vars(AttributeGroupCounters::class))));
    }
}
