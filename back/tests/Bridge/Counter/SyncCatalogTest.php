<?php declare(strict_types=1);

namespace Tests\Bridge\Counter;

use App\Bridge\Counter\Counter\CatalogCounter;
use App\Bridge\Counter\Entity\CatalogCounters;
use DateTime;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Tests\Factory\CatalogContentFactory;
use Tests\Factory\CatalogFactory;
use Tests\Factory\CatalogMediaFactory;
use Tests\Factory\CatalogProductFactory;
use Tests\Factory\CategoryFactory;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;

class SyncCatalogTest extends KernelTestCase
{
    private const array COUNTERS_PROPERTIES = ['uses', 'products', 'categories', 'contents', 'medias', 'updatedAt'];
    use ResetDatabase;
    use Factories;

    public function testValidSync(): void
    {
        /** @var CatalogCounter $counter */
        $counter = self::getContainer()->get(CatalogCounter::class);

        $catalog = CatalogFactory::new()->create();
        CatalogProductFactory::new(['catalog' => $catalog])->many(1)->create();
        CategoryFactory::new(['catalog' => $catalog])->many(2)->create();
        CatalogContentFactory::new(['catalog' => $catalog])->many(3)->create();
        CatalogMediaFactory::new(['catalog' => $catalog])->many(4)->create();

        $counter->executeSync([$catalog->code]);

        $catalog->_real();

        self::assertEquals(10, $catalog->counters->uses);
        self::assertEquals(1, $catalog->counters->products);
        self::assertEquals(2, $catalog->counters->categories);
        self::assertEquals(3, $catalog->counters->contents);
        self::assertEquals(4, $catalog->counters->medias);
        self::assertEquals(DateTime::class, $catalog->counters->updatedAt::class);
        self::assertCount(0, array_diff(self::COUNTERS_PROPERTIES, array_keys(get_class_vars(CatalogCounters::class))));
    }
}
