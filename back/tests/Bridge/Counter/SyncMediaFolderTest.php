<?php declare(strict_types=1);

namespace Tests\Bridge\Counter;

use App\Bridge\Counter\Counter\MediaFolderCounter;
use App\Bridge\Counter\Entity\MediaFolderCounters;
use DateTime;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Tests\Factory\MediaFactory;
use Tests\Factory\MediaFolderFactory;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;

class SyncMediaFolderTest extends KernelTestCase
{
    private const array COUNTERS_PROPERTIES = ['medias', 'folders', 'updatedAt'];
    use ResetDatabase;
    use Factories;

    public function testValidSync(): void
    {
        /** @var MediaFolderCounter $counter */
        $counter = self::getContainer()->get(MediaFolderCounter::class);

        $mediaFolder = MediaFolderFactory::new()->create();
        MediaFactory::new(['folder' => $mediaFolder])->many(2)->create();
        MediaFolderFactory::new(['parent' => $mediaFolder])->many(3)->create();

        $counter->executeSync([$mediaFolder->id]);

        $mediaFolder->_real();

        self::assertEquals(2, $mediaFolder->counters->medias);
        self::assertEquals(3, $mediaFolder->counters->folders);
        self::assertEquals(DateTime::class, $mediaFolder->counters->updatedAt::class);
        self::assertCount(0, array_diff(self::COUNTERS_PROPERTIES, array_keys(get_class_vars(MediaFolderCounters::class))));
    }
}
