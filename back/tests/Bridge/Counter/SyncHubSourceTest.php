<?php declare(strict_types=1);

namespace Tests\Bridge\Counter;

use App\Bridge\Counter\Counter\HubSourceCounter;
use App\Bridge\Counter\Entity\HubSourceCounters;
use DateTime;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Tests\Factory\HubSourceFactory;
use Tests\Factory\HubStockFactory;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;

class SyncHubSourceTest extends KernelTestCase
{
    private const array COUNTERS_PROPERTIES = ['stocks', 'updatedAt'];
    use ResetDatabase;
    use Factories;

    public function testValidSync(): void
    {
        /** @var HubSourceCounter $counter */
        $counter = self::getContainer()->get(HubSourceCounter::class);

        $source = HubSourceFactory::new()->create();
        HubStockFactory::new(['source' => $source])->many(2)->create();

        $counter->executeSync([$source->code]);

        $source->_real();

        self::assertEquals(2, $source->counters->stocks);
        self::assertEquals(DateTime::class, $source->counters->updatedAt::class);
        self::assertCount(0, array_diff(self::COUNTERS_PROPERTIES, array_keys(get_class_vars(HubSourceCounters::class))));
    }
}
