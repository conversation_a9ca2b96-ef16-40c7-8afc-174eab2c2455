<?php declare(strict_types=1);

namespace Tests\Bridge\Counter;

use App\Bridge\Counter\Counter\WorkflowStepCounter;
use App\Bridge\Counter\Entity\WorkflowStepCounters;
use DateTime;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Tests\Factory\RuleFactory;
use Tests\Factory\WorkflowStepFactory;
use Tests\Factory\WorkflowStepRuleGroupFactory;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;

class SyncWorkflowStepTest extends KernelTestCase
{
    private const array COUNTERS_PROPERTIES = ['rules', 'updatedAt'];
    use ResetDatabase;
    use Factories;

    public function testValidSync(): void
    {
        /** @var WorkflowStepCounter $counter */
        $counter = self::getContainer()->get(WorkflowStepCounter::class);

        $step = WorkflowStepFactory::new()->create();
        $workflowstepRuleGrps = WorkflowStepRuleGroupFactory::new(['workflowStep' => $step])->many(3)->create();
        foreach ($workflowstepRuleGrps as $workflowstepRuleGrp) {
            RuleFactory::new(['ruleGroup' => $workflowstepRuleGrp->ruleGroup])->many(4)->create();
        }

        $counter->executeSync([$step->id]);

        $step->_real();

        self::assertEquals(12, $step->counters->rules);
        self::assertEquals(DateTime::class, $step->counters->updatedAt::class);
        self::assertCount(0, array_diff(self::COUNTERS_PROPERTIES, array_keys(get_class_vars(WorkflowStepCounters::class))));
    }
}
