<?php declare(strict_types=1);

namespace Tests\Bridge\Counter;

use App\Bridge\Counter\Counter\AttributeCounter;
use App\Bridge\Counter\Entity\AttributeCounters;
use DateTime;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Tests\Factory\AttributeFactory;
use Tests\Factory\AttributeOptionFactory;
use Tests\Factory\CategoryDataFactory;
use Tests\Factory\ContentDataFactory;
use Tests\Factory\MediaDataFactory;
use Tests\Factory\ProductDataFactory;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;
use function array_diff;

class SyncAttributeTest extends KernelTestCase
{
    private const array COUNTERS_PROPERTIES = ['uses', 'categories', 'contents', 'medias', 'options', 'updatedAt'];
    use ResetDatabase;
    use Factories;

    public function testValidSync(): void
    {
        /** @var AttributeCounter $counter */
        $counter = self::getContainer()->get(AttributeCounter::class);
        $attribute = AttributeFactory::new()->create();
        ProductDataFactory::new(['attribute' => $attribute])->many(1)->create();
        CategoryDataFactory::new(['attribute' => $attribute])->many(2)->create();
        ContentDataFactory::new(['attribute' => $attribute])->many(3)->create();
        MediaDataFactory::new(['attribute' => $attribute])->many(4)->create();
        AttributeOptionFactory::new(['attribute' => $attribute])->many(5)->create();

        $counter->executeSync([$attribute->code]);

        $attribute->_real();

        self::assertEquals(15, $attribute->counters->uses);
        self::assertEquals(1, $attribute->counters->products);
        self::assertEquals(2, $attribute->counters->categories);
        self::assertEquals(3, $attribute->counters->contents);
        self::assertEquals(4, $attribute->counters->medias);
        self::assertEquals(5, $attribute->counters->options);
        self::assertEquals(DateTime::class, $attribute->counters->updatedAt::class);
        self::assertCount(0, array_diff(self::COUNTERS_PROPERTIES, array_keys(get_class_vars(AttributeCounters::class))));
    }
}
