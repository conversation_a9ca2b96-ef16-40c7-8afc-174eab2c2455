<?php declare(strict_types=1);

namespace Tests\Bridge\Counter;

use App\Bridge\Counter\Counter\RuleGroupCounter;
use App\Bridge\Counter\Entity\RuleGroupCounters;
use DateTime;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Tests\Factory\RuleFactory;
use Tests\Factory\RuleGroupFactory;
use Tests\Factory\WorkflowStepRuleGroupFactory;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;

class SyncRuleGroupTest extends KernelTestCase
{
    private const array COUNTERS_PROPERTIES = ['rules', 'steps', 'updatedAt'];
    use ResetDatabase;
    use Factories;

    public function testValidSync(): void
    {
        /** @var RuleGroupCounter $counter */
        $counter = self::getContainer()->get(RuleGroupCounter::class);

        $ruleGrp = RuleGroupFactory::new()->create();
        RuleFactory::new(['ruleGroup' => $ruleGrp])->many(1)->create();
        WorkflowStepRuleGroupFactory::new(['ruleGroup' => $ruleGrp])->many(2)->create();

        $counter->executeSync([$ruleGrp->code]);

        $ruleGrp->_real();

        self::assertEquals(1, $ruleGrp->counters->rules);
        self::assertEquals(2, $ruleGrp->counters->steps);
        self::assertEquals(DateTime::class, $ruleGrp->counters->updatedAt::class);
        self::assertCount(0, array_diff(self::COUNTERS_PROPERTIES, array_keys(get_class_vars(RuleGroupCounters::class))));
    }
}
