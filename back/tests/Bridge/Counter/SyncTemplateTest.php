<?php declare(strict_types=1);

namespace Tests\Bridge\Counter;

use App\Bridge\Counter\Counter\TemplateCounter;
use App\Bridge\Counter\Entity\TemplateCounters;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Tests\Factory\AttributeGroupFactory;
use Tests\Factory\TemplateFactory;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;

class SyncTemplateTest extends KernelTestCase
{
    private const array COUNTERS_PROPERTIES = ['attributeGroups', 'updatedAt'];
    use ResetDatabase;
    use Factories;

    public function testValidSync(): void
    {
        /** @var TemplateCounter $counter */
        $counter = self::getContainer()->get(TemplateCounter::class);

        $attributeGrp = AttributeGroupFactory::new()->create();
        $template = TemplateFactory::new(['attributeGroups' => new ArrayCollection([$attributeGrp->_real()])])->create();
        $template->_save();

        $counter->executeSync([$template->code]);

        $template->_real();

        self::assertEquals(1, $template->counters->attributeGroups);
        self::assertEquals(DateTime::class, $template->counters->updatedAt::class);
        self::assertCount(0, array_diff(self::COUNTERS_PROPERTIES, array_keys(get_class_vars(TemplateCounters::class))));
    }
}
