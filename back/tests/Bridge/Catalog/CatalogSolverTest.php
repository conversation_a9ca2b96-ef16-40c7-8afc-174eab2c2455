<?php declare(strict_types=1);

namespace Tests\Bridge\Catalog;

use App\Bridge\Catalog\Catalogs;
use App\Bridge\Catalog\Solver\CatalogSolver;
use App\Bridge\Filter\Enum\CombineOperatorEnum;
use App\Bridge\Filter\Enum\OperatorEnum;
use App\Bridge\Filter\Model\Filter;
use App\Bridge\Filter\Model\Filters;
use App\Bridge\Flat\Entity\FlatProduct;
use App\Bridge\Flat\Model\Value;
use App\Bridge\Flat\Model\ValueData;
use App\Bridge\Flat\Model\Values;
use App\Contracts\Enum\ApiTypeEnum;
use App\Contracts\Model\Header;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Serializer;
use Tests\Factory\CatalogFactory;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;

class CatalogSolverTest extends KernelTestCase
{
    use ResetDatabase;
    use Factories;

    public function testGetMatchingCatalogCodes(): void
    {
        /** @var Serializer $serializer */
        $serializer = self::getContainer()->get('serializer');
        /** @var CatalogSolver $solver */
        $solver = self::getContainer()->get(CatalogSolver::class);

        $catalog = CatalogFactory::new([
            'code' => 'catalog_1',
            'type' => ApiTypeEnum::PRODUCT,
            'filters' => new Filters(
                filters: [
                    new Filters(
                        filters: [
                            new Filter(Header::property('sku'), OperatorEnum::EQ, 'SKU_001'),
                            new Filter(Header::property('status'), OperatorEnum::EQ, true),
                        ]
                    ),
                    new Filters(
                        filters: [
                            new Filter(Header::attribute('ready', 'fr_FR', 'magento'), OperatorEnum::EQ, true),
                        ]
                    ),
                ],
                operator: CombineOperatorEnum::OR
            ),
        ])->create();
        $catalog = $catalog->_real();

        $product1 = new FlatProduct();
        $product1->sku = 'SKU_001';
        $product1->values = new Values([
            new Value('ready', [
                new ValueData('fr_FR', 'magento', false),
                new ValueData('en_GB', 'magento', false),
            ]),
        ]);

        /**
         * @see Catalogs::calculateDocuments()
         */
        $documentProperties = $serializer->normalize($product1);
        unset($documentProperties['values']);
        $documentProperties['type'] = ApiTypeEnum::PRODUCT;

        $matchingCatalogs = $solver->getMatchingCatalogCodes($product1->values, $documentProperties);

        $this->assertCount(1, $matchingCatalogs);
        $this->assertContains($catalog->code, $matchingCatalogs);

        $product2 = new FlatProduct();
        $product2->sku = 'SKU_002';
        $product2->status = false;
        $product2->values = new Values([
            new Value('ready', [
                new ValueData('fr_FR', 'magento', true),
                new ValueData('en_GB', 'magento', false),
            ]),
        ]);

        /**
         * @see Catalogs::calculateDocuments()
         */
        $documentProperties = $serializer->normalize($product2);
        unset($documentProperties['values']);
        $documentProperties['type'] = ApiTypeEnum::PRODUCT;

        $matchingCatalogs = $solver->getMatchingCatalogCodes($product2->values, $documentProperties);

        $this->assertCount(1, $matchingCatalogs);
        $this->assertContains($catalog->code, $matchingCatalogs);
    }
}
