<?php declare(strict_types=1);

namespace Tests\Factory\Embedded;

use App\Bridge\Translation\Entity\Translations;
use Zenstruck\Foundry\ObjectFactory;

/**
 * @extends ObjectFactory<Translations>
 */
final class TranslationsFactory extends ObjectFactory
{
    public static function class(): string
    {
        return Translations::class;
    }

    protected function defaults(): array|callable
    {
        return [
            'fr' => self::faker()->word() . 'fr',
            'en' => self::faker()->word() . 'en',
        ];
    }
}
