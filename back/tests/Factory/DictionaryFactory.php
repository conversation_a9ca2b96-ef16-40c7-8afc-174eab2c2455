<?php declare(strict_types=1);

namespace Factory;

use App\Entity\Dictionary;
use Tests\Factory\Embedded\TranslationsFactory;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<Dictionary>
 */
final class DictionaryFactory extends PersistentProxyObjectFactory
{
    public static function class(): string
    {
        return Dictionary::class;
    }

    protected function defaults(): array
    {
        return [
            'code' => 'dictionary_' . uniqid(),
            'names' => TranslationsFactory::new(),
        ];
    }
}
