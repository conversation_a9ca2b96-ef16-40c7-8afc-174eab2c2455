<?php declare(strict_types=1);

namespace Factory;

use App\Entity\DictionaryMapping;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<DictionaryMapping>
 */
final class DictionaryMappingFactory extends PersistentProxyObjectFactory
{
    public static function class(): string
    {
        return DictionaryMapping::class;
    }

    protected function defaults(): array
    {
        return [
            'dictionary' => DictionaryFactory::new(),
            'from' => self::faker()->word(),
            'to' => self::faker()->word(),
        ];
    }
}
