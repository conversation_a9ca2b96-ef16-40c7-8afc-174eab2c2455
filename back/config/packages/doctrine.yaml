doctrine:
  dbal:
    url: '%env(resolve:DATABASE_URL)%'
    profiling_collect_backtrace: '%kernel.debug%'
    use_savepoints: true
  orm:
    auto_generate_proxy_classes: true
    enable_lazy_ghost_objects: true
    report_fields_where_declared: true
    validate_xml_mapping: true
    naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
    auto_mapping: true
    controller_resolver:
      auto_mapping: true
    mappings:
      App:
        type: attribute
        is_bundle: false
        dir: '%kernel.project_dir%/src/Entity'
        prefix: App\Entity
      AppBridgeBreadcrumb:
        dir: '%kernel.project_dir%/src/Bridge/Breadcrumb/Entity'
        prefix: App\Bridge\Breadcrumb\Entity
      AppBridgeEav:
        dir: '%kernel.project_dir%/src/Bridge/Eav/Entity'
        prefix: App\Bridge\Eav\Entity
      AppBridgeElastic:
        dir: '%kernel.project_dir%/src/Bridge/Elastic/Entity'
        prefix: App\Bridge\Elastic\Entity
      AppBridgeFlat:
        dir: '%kernel.project_dir%/src/Bridge/Flat/Entity'
        prefix: App\Bridge\Flat\Entity
      AppBridgeMedia:
        dir: '%kernel.project_dir%/src/Bridge/Media/Entity'
        prefix: App\Bridge\Media\Entity
      AppBridgePlatform:
        dir: '%kernel.project_dir%/src/Bridge/Platform/Entity'
        prefix: App\Bridge\Platform\Entity
      AppBridgeTranslation:
        dir: '%kernel.project_dir%/src/Bridge/Translation/Entity'
        prefix: App\Bridge\Translation\Entity
      AppBridgeCounter:
        dir: '%kernel.project_dir%/src/Bridge/Counter/Entity'
        prefix: App\Bridge\Counter\Entity
      AppBridgeWorkflow:
        dir: '%kernel.project_dir%/src/Bridge/Workflow/Entity'
        prefix: App\Bridge\Workflow\Entity
      AppBridgeZip:
        dir: '%kernel.project_dir%/src/Bridge/Zip/Entity'
        prefix: App\Bridge\Zip\Entity
      AppModuleChannel:
        dir: '%kernel.project_dir%/src/Module/Channel/Entity'
        prefix: App\Module\Channel\Entity
      AppModuleCompletude:
        dir: '%kernel.project_dir%/src/Module/Completude/Entity'
        prefix: App\Module\Completude\Entity
      AppModuleConfig:
        dir: '%kernel.project_dir%/src/Module/Config/Entity'
        prefix: App\Module\Config\Entity
      AppModuleCsv:
        dir: '%kernel.project_dir%/src/Module/Csv/Entity'
        prefix: App\Module\Csv\Entity
      AppModuleHub:
        dir: '%kernel.project_dir%/src/Module/Hub/Entity'
        prefix: App\Module\Hub\Entity
      AppModuleScheduler:
        dir: '%kernel.project_dir%/src/Module/Scheduler/Entity'
        prefix: App\Module\Scheduler\Entity
      AppModuleStat:
        dir: '%kernel.project_dir%/src/Module/Stat/Entity'
        prefix: App\Module\Stat\Entity
      AppModuleView:
        dir: '%kernel.project_dir%/src/Module/View/Entity'
        prefix: App\Module\View\Entity
    dql:
      datetime_functions:
        DATE_FORMAT: DoctrineExtensions\Query\Mysql\DateFormat
        DAY: DoctrineExtensions\Query\Mysql\Day
        MONTH: DoctrineExtensions\Query\Mysql\Month
        YEAR: DoctrineExtensions\Query\Mysql\Year
        CAST: DoctrineExtensions\Query\Mysql\Cast
        TIMESTAMPDIFF: DoctrineExtensions\Query\Mysql\TimestampDiff
        SUBSTRING_INDEX: DoctrineExtensions\Query\Mysql\SubstringIndex
      string_functions:
        JSON_EXTRACT: Scienta\DoctrineJsonFunctions\Query\AST\Functions\Mysql\JsonExtract
        JSON_UNQUOTE: Scienta\DoctrineJsonFunctions\Query\AST\Functions\Mysql\JsonUnquote
        JSON_LENGTH: Scienta\DoctrineJsonFunctions\Query\AST\Functions\Mysql\JsonLength
        JSON_SEARCH: Scienta\DoctrineJsonFunctions\Query\AST\Functions\Mysql\JsonSearch

when@prod:
  doctrine:
    orm:
      auto_generate_proxy_classes: false
      proxy_dir: '%kernel.build_dir%/doctrine/orm/Proxies'
      query_cache_driver:
        type: pool
        pool: doctrine.system_cache_pool
      result_cache_driver:
        type: pool
        pool: doctrine.result_cache_pool

  framework:
    cache:
      pools:
        doctrine.result_cache_pool:
          adapter: cache.app
        doctrine.system_cache_pool:
          adapter: cache.system

when@test:
  doctrine:
    dbal:
      dbname_suffix: '_test%env(default::TEST_TOKEN)%'
