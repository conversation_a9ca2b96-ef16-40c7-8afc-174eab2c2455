framework:
  cache:
    default_redis_provider: '%env(REDIS_DSN)%'
    pools:
      cache.translation:
        default_lifetime: 86400
        adapters:
          - cache.adapter.array
      cache.medias:
        default_lifetime: 86400
        adapters:
          - cache.adapter.array
      cache.dashboard:
        default_lifetime: 3600
        adapters:
          - cache.adapter.array
          - cache.adapter.redis
      cache.config:
        default_lifetime: 86400
        adapters:
          - cache.adapter.array
          - cache.adapter.redis
      cache.mirakl:
        default_lifetime: 86400
        adapters:
          - cache.adapter.array
          - cache.adapter.redis

when@prod:
  framework:
    cache:
      pools:
        cache.translation:
          default_lifetime: 86400
          adapters:
            - cache.adapter.array
            - cache.adapter.redis

when@test:
  services:
    cache.adapter.null:
      class: Symfony\Component\Cache\Adapter\NullAdapter
      public: true
      arguments: [~] # small trick to avoid arguments errors on compile-time.

  framework:
    cache:
      pools:
        cache.translation:
          adapters:
            - cache.adapter.null
        cache.medias:
          adapters:
            - cache.adapter.null
        cache.dashboard:
          adapters:
            - cache.adapter.null
        cache.config:
          adapters:
            - cache.adapter.null
        cache.mirakl:
          adapters:
            - cache.adapter.null
