<?php declare(strict_types=1);

namespace App\Repository;

use App\Bridge\Security\Enum\RoleEnum;
use App\Bridge\Security\Utils\RoleUtils;
use App\Contracts\Enum\LoggerChannelEnum;
use App\Contracts\Enum\MembershipRoleEnum;
use App\Contracts\Enum\ScopeEnum;
use App\Contracts\Enum\TableEnum;
use App\Entity\User;
use App\Entity\UserGroup;
use App\Entity\UserLogin;
use App\Utils\DateUtils;
use App\Utils\JsonUtils;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Monolog\Attribute\WithMonologChannel;
use Psr\Log\LoggerInterface;
use Throwable;
use function count;

/**
 * @template-extends ServiceEntityRepository<User>
 */
#[WithMonologChannel(LoggerChannelEnum::CORE)]
final class UserRepository extends ServiceEntityRepository
{
    public function __construct(
        ManagerRegistry $registry,
        private readonly LoggerInterface $logger,
    ) {
        parent::__construct($registry, User::class);
    }

    public function saveLogin(string $userEmail): void
    {
        $co = $this->getEntityManager()->getConnection();

        $now = DateUtils::now();

        try {
            $tableUser = TableEnum::USER;

            $query1 = <<<SQL
            UPDATE {$tableUser}
            SET last_login = ?
            WHERE email = ?
            SQL;

            $co->executeStatement($query1, [
                $now->format('Y-m-d H:i:s'),
                $userEmail,
            ]);
        } catch (Throwable $e) {
            $this->logger->error('cannot save last login', [$e]);
        }

        try {
            $tableUserLogin = TableEnum::USER_LOGIN;

            $query2 = <<<SQL
            INSERT INTO {$tableUserLogin} (email, login_date, login_count)
            VALUES (?, ?, 1)
            ON DUPLICATE KEY UPDATE
                login_count = login_count + 1
            SQL;

            $co->executeStatement($query2, [
                $userEmail,
                $now->format(UserLogin::DATE_FORMAT),
            ]);
        } catch (Throwable $e) {
            $this->logger->error('cannot save login', [$e]);
        }
    }

    public function sync(): void
    {
        $co = $this->getEntityManager()->getConnection();

        // tables
        $tableUserGroup = TableEnum::USER_GROUP;
        $tableUserGroupCatalog = TableEnum::USER_GROUP_CATALOG;
        $tableUserGroupScope = TableEnum::USER_GROUP_SCOPE;
        $tableUser = TableEnum::USER;
        $tableUserGroupMembership = TableEnum::USER_GROUP_MEMBERSHIP;
        $tableUserGroupMembershipRequest = TableEnum::USER_GROUP_MEMBERSHIP_REQUEST;

        // cleanup
        $query1 = <<<SQL
        DELETE FROM {$tableUserGroupMembership}
        WHERE user_id IN ( SELECT id FROM {$tableUser} WHERE role = ? OR role = ? )
        SQL;
        $co->executeStatement($query1, [RoleEnum::TECH, RoleEnum::SUPER_ADMIN]);

        $query2 = <<<SQL
        DELETE FROM {$tableUserGroupMembershipRequest}
        WHERE user_id IN ( SELECT id FROM {$tableUser} WHERE role = ? OR role = ? )
        SQL;
        $co->executeStatement($query2, [RoleEnum::TECH, RoleEnum::SUPER_ADMIN]);

        // data
        $userGroups = $co->executeQuery("SELECT id, name FROM {$tableUserGroup}")->fetchAllAssociative();
        $userGroupCatalogs = $co->executeQuery("SELECT user_group_id, catalog_code FROM {$tableUserGroupCatalog}")->fetchAllAssociative();
        $userGroupScopes = $co->executeQuery("SELECT group_id, attribute_group_code, scope FROM {$tableUserGroupScope}")->fetchAllAssociative();
        $users = $co->executeQuery("SELECT id, email, firstname, lastname, `role` FROM {$tableUser}")->fetchAllAssociative();
        $userGroupMemberships = $co->executeQuery("SELECT group_id, user_id, role FROM {$tableUserGroupMembership}")->fetchAllAssociative();

        $baseScopesStructure = array_combine(
            ScopeEnum::ALL,
            array_fill(0, count(ScopeEnum::ALL), []),
        );

        // $compiledGroups['userGroupId']['catalogCode'] = [
        //   'read' => ['attributeGroupCode'],
        //   'write' => ['attributeGroupCode']
        // ]
        $compiledGroups = [];
        foreach ($userGroups as ['id' => $userGroupId]) {
            $compiledGroups[$userGroupId] ??= [];
            foreach ($userGroupCatalogs as $userGroupCatalog) {
                if ($userGroupId === $userGroupCatalog['user_group_id']) {
                    $catalogCode = $userGroupCatalog['catalog_code'];

                    $compiledGroups[$userGroupId][$catalogCode] ??= $baseScopesStructure;
                    foreach ($userGroupScopes as $userGroupScope) {
                        if ($userGroupId === $userGroupScope['group_id']) {
                            $scope = $userGroupScope['scope'];
                            $compiledGroups[$userGroupId][$catalogCode][$scope] ??= [];
                            $compiledGroups[$userGroupId][$catalogCode][$scope][] = $userGroupScope['attribute_group_code'];
                        }
                    }
                }
            }
        }

        // update users
        $userGroupsPerId = array_combine(
            array_column($userGroups, 'id'),
            $userGroups,
        );
        foreach ($users as $user) {
            $userId = $user['id'];
            $isAdmin = false;

            $saveCatalogs = [];
            $saveGroups = [];

            foreach ($userGroupMemberships as $userGroupMembership) {
                if ($userId === $userGroupMembership['user_id']) {
                    $groupId = $userGroupMembership['group_id'];

                    foreach ($compiledGroups[$groupId] ?? [] as $catalogCode => $group) {
                        $saveCatalogs[$catalogCode] ??= $baseScopesStructure;
                        foreach ($group as $scope => $ids) {
                            $saveCatalogs[$catalogCode][$scope] = array_unique([
                                ...$saveCatalogs[$catalogCode][$scope],
                                ...$ids,
                            ]);
                        }
                    }

                    if (MembershipRoleEnum::ADMIN === $userGroupMembership['role']) {
                        $isAdmin = true;
                    }

                    $saveGroups[$groupId] = [
                        'id' => $groupId,
                        'name' => $userGroupsPerId[$groupId]['name'] ?? null,
                        'role' => $userGroupMembership['role'] ?? MembershipRoleEnum::MEMBER,
                    ];
                }
            }

            if ($isAdmin) {
                $inheriteds = $this->getEntityManager()->getRepository(UserGroup::class)->getGroupsManagedByUser($userId, true);

                foreach ($inheriteds as $groupId) {
                    $saveGroups[$groupId] = [
                        'id' => $groupId,
                        'name' => $userGroupsPerId[$groupId]['name'] ?? null,
                        'role' => MembershipRoleEnum::ADMIN_INHERITED,
                    ];
                }
            }

            $co->executeStatement("UPDATE {$tableUser} SET computed_catalogs = ?, computed_groups = ? WHERE id = ?", [
                RoleUtils::roleIsSuperAdmin($user['role']) ? null : JsonUtils::encode($saveCatalogs),
                JsonUtils::encode(array_values($saveGroups)),
                $userId,
            ]);
        }

        // update groups
        $usersPerId = array_combine(
            array_column($users, 'id'),
            $users,
        );
        foreach ($userGroups as $userGroup) {
            $userGroupId = $userGroup['id'];

            $saveAttributeGroups = [];
            $saveUsers = [];
            foreach ($userGroupScopes as $userGroupScope) {
                if ($userGroupId === $userGroupScope['group_id']) {
                    $saveAttributeGroups[] = [
                        'code' => $userGroupScope['attribute_group_code'],
                        'scope' => $userGroupScope['scope'] ?? ScopeEnum::READ,
                    ];
                }
            }
            foreach ($userGroupMemberships as $userGroupMembership) {
                if ($userGroupId === $userGroupMembership['group_id']) {
                    $user = $usersPerId[$userGroupMembership['user_id']] ?? null;
                    if (null !== $user) {
                        $saveUsers[] = [
                            'id' => $user['id'],
                            'email' => $user['email'],
                            'firstname' => $user['firstname'],
                            'lastname' => $user['lastname'],
                            'role' => $userGroupMembership['role'] ?? MembershipRoleEnum::MEMBER,
                        ];
                    }
                }
            }

            $co->executeStatement("UPDATE {$tableUserGroup} SET computed_attribute_groups = ?, computed_users = ? WHERE id = ?", [
                JsonUtils::encode($saveAttributeGroups),
                JsonUtils::encode($saveUsers),
                $userGroupId,
            ]);
        }
    }
}
