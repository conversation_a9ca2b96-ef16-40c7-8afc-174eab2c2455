<?php declare(strict_types=1);

namespace App\Api\OpenApi;

use ApiPlatform\OpenApi\Factory\OpenApiFactoryInterface;
use ApiPlatform\OpenApi\OpenApi;
use App\Bridge\Platform\Environment;
use ArrayObject;
use Symfony\Component\DependencyInjection\Attribute\AsDecorator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\RequestStack;

#[AsDecorator('api_platform.openapi.factory', onInvalid: ContainerInterface::IGNORE_ON_INVALID_REFERENCE)]
readonly class OpenApiFactory implements OpenApiFactoryInterface
{
    public const string TAG_STATUS = '01/ Status';
    public const string TAG_LOGIN = '02/ Login';
    public const string TAG_SECURITY = '03/ Security';
    public const string TAG_SETTINGS = '04/ Settings';
    public const string TAG_USER = '05/ User';
    public const string TAG_CONFIGURATION = '06/ Configuration';
    public const string TAG_CATALOG = '07/ Catalog';
    public const string TAG_SCOPE = '08/ Scope';
    public const string TAG_WORKFLOW = '09/ Workflow';
    public const string TAG_COMPLETUDE = '10/ Completude';
    public const string TAG_DOCUMENT = '11/ Document';
    public const string TAG_SYNC = '12/ Sync';
    public const string TAG_HUB = '13/ Hub';
    public const string TAG_HUB_DASH = '14/ Hub Dash';
    public const string TAG_CHANNEL = '15/ Channel';
    public const string TAG_UI = '16/ UI';
    public const string TAG_AUTOCOMPLETION = '17/ Autocompletion';
    public const string TAG_MAGENTO = '18/ Magento';

    public function __construct(
        private OpenApiFactoryInterface $decorated,
        private Environment $environment,
        private RequestStack $requestStack,
    ) {
    }

    public function __invoke(array $context = []): OpenApi
    {
        /** @var OpenApi $specifications */
        $specifications = $this->decorated->__invoke($context);

        $specifications = $specifications->withTags([
            ['name' => self::TAG_STATUS, 'description' => null],
            ['name' => self::TAG_LOGIN, 'description' => null],
            ['name' => self::TAG_SECURITY, 'description' => null],
            ['name' => self::TAG_SETTINGS, 'description' => null],
            ['name' => self::TAG_USER, 'description' => null],
            ['name' => self::TAG_CONFIGURATION, 'description' => null],
            ['name' => self::TAG_CATALOG, 'description' => null],
            ['name' => self::TAG_SCOPE, 'description' => null],
            ['name' => self::TAG_WORKFLOW, 'description' => null],
            ['name' => self::TAG_COMPLETUDE, 'description' => null],
            ['name' => self::TAG_DOCUMENT, 'description' => null],
            ['name' => self::TAG_SYNC, 'description' => null],
            ['name' => self::TAG_HUB, 'description' => null],
            ['name' => self::TAG_HUB_DASH, 'description' => null],
            ['name' => self::TAG_CHANNEL, 'description' => null],
            ['name' => self::TAG_UI, 'description' => null],
            ['name' => self::TAG_AUTOCOMPLETION, 'description' => null],
            ['name' => self::TAG_MAGENTO, 'description' => null],
        ]);

        // fix Translations definition
        $schemas = $specifications->getComponents()->getSchemas();
        foreach (['Translations', 'Translations.jsonld'] as $name) {
            $schemas[$name]['type'] = 'object';
            $schemas[$name]['description'] = 'An object with langs as keys and string messages as values.';
            foreach ($this->environment->langs as $lang) {
                $schemas[$name]['description'] .= "<br><strong>{$lang}</strong>: string";
            }
            unset($schemas[$name]['properties']);
        }

        // fix Values definition
        foreach (['Values', 'Values.jsonld'] as $name) {
            $schemas[$name]['type'] = 'object';
            $schemas[$name]['description'] = 'An object with attribute codes as keys and object as values.';
            unset($schemas[$name]['properties']);
        }

        // fix Filters definition
        foreach (['Filters', 'Filters.jsonld'] as $name) {
            $schemas[$name] = $schemas[$name]['properties']['filters'];
        }

        // add url when necessary
        foreach (['Media', 'Media.jsonld', 'Zip', 'Zip.jsonld'] as $name) {
            $schemas[$name]['properties']['url'] = new ArrayObject([
                'readOnly' => true,
                'type' => 'string',
            ]);
        }

        // info
        $baseUri = $this->requestStack->getCurrentRequest()?->getSchemeAndHttpHost();

        $info = $specifications->getInfo();
        $info = $info->withDescription(str_replace(
            '{platform_base_uri}',
            $baseUri,
            $info->getDescription(),
        ));

        return $specifications->withInfo($info);
    }
}
