<?php declare(strict_types=1);

namespace App\Listener;

use App\Contracts\Enum\ApiTypeEnum;
use App\Entity\Dictionary;
use App\Entity\DictionaryMapping;
use App\Module\Cache\DoctrineCache;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\PostFlushEventArgs;
use Doctrine\ORM\Event\PostPersistEventArgs;
use Doctrine\ORM\Event\PostRemoveEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;

#[AsDoctrineListener(event: Events::postPersist)]
#[AsDoctrineListener(event: Events::preUpdate)]
#[AsDoctrineListener(event: Events::postRemove)]
#[AsDoctrineListener(event: Events::postFlush)]
final class DictionaryListener
{
    private bool $mustUpdate = false;

    public function __construct(
        private readonly DoctrineCache $doctrineCache,
    ) {
    }

    public function postPersist(PostPersistEventArgs $args): void
    {
        $object = $args->getObject();

        if ($object instanceof Dictionary || $object instanceof DictionaryMapping) {
            $this->mustUpdate = true;
        }
    }

    public function preUpdate(PreUpdateEventArgs $args): void
    {
        $object = $args->getObject();

        if (
            $object instanceof DictionaryMapping
            && (
                $args->hasChangedField('dictionary')
                || $args->hasChangedField('from')
                || $args->hasChangedField('to')
            )
        ) {
            $this->mustUpdate = true;
        }
    }

    public function postRemove(PostRemoveEventArgs $args): void
    {
        $object = $args->getObject();

        if ($object instanceof Dictionary || $object instanceof DictionaryMapping) {
            $this->mustUpdate = true;
        }
    }

    public function postFlush(PostFlushEventArgs $args): void
    {
        $mustUpdate = $this->mustUpdate;

        $this->mustUpdate = false;

        if ($mustUpdate) {
            $this->doctrineCache->warmup(ApiTypeEnum::DICTIONARY);
        }
    }
}
