<?php declare(strict_types=1);

/** @todo this file is ignored from PHPstan and is broken */

namespace App\Connector\Shopify\Exporter;

use App\Api\Model\Uuids;
use App\Bridge\Flat\Entity\FlatProduct;
use App\Bridge\Shopify\Channel\Model\Accessor\ShopifyMediaAccessor;
use App\Bridge\Shopify\Channel\Model\Accessor\ShopifyMetafieldAccessor;
use App\Bridge\Shopify\Channel\Model\Accessor\ShopifyPropertyAccessor;
use App\Bridge\Shopify\Enum\ShopifyAttributeEnum;
use App\Bridge\Shopify\Enum\ShopifyConfigurationEnum;
use App\Bridge\Shopify\Enum\ShopifyPropertyEnum;
use App\Bridge\Shopify\Enum\ShopifyStatusEnum;
use App\Bridge\Shopify\GraphQL\Inventory\Input\InventoryActivateInput;
use App\Bridge\Shopify\GraphQL\Inventory\Input\InventoryItemInput;
use App\Bridge\Shopify\GraphQL\Inventory\Query\InventoryActivate;
use App\Bridge\Shopify\GraphQL\Inventory\Query\InventoryItemUpdate;
use App\Bridge\Shopify\GraphQL\Location\Input\AddLocationInput;
use App\Bridge\Shopify\GraphQL\Location\Query\AddLocation;
use App\Bridge\Shopify\GraphQL\Location\Query\QueryLocations;
use App\Bridge\Shopify\GraphQL\Media\Input\MediaInput;
use App\Bridge\Shopify\GraphQL\Metafield\Input\MetafieldInput;
use App\Bridge\Shopify\GraphQL\Products\Input\ProductSetInput;
use App\Bridge\Shopify\GraphQL\Products\Query\ProductSet;
use App\Bridge\Shopify\ShopifyClient;
use App\Connector\Shopify\Configurator\ShopifyConfigurator;
use App\Connector\Shopify\ShopifyConfig;
use App\Contracts\Enum\ApiTypeEnum;
use App\Contracts\Enum\TableEnum;
use App\Contracts\Event\DocumentsUpdatedEvent;
use App\Contracts\Model\Header;
use App\Module\Channel\Enum\ChannelSourceEnum;
use App\Module\Channel\Handler\Accessors;
use App\Module\Channel\Handler\Sources;
use App\Module\Channel\Model\Accessor\AttributeAccessor;
use App\Module\Channel\Model\Accessor\FormulaAccessor;
use App\Module\Channel\Model\Accessor\PropertyAccessor;
use App\Module\Channel\Model\Accessor\RawAccessor;
use App\Module\Channel\Model\Mapping;
use App\Module\Channel\Model\MappingColumn;
use App\Module\Channel\Model\Source;
use App\Module\Injector\Injectors;
use App\Module\Injector\Model\DataImport;
use App\Module\Injector\Model\DataImportItem;
use App\Utils\JsonUtils;
use App\Utils\UuidUtils;
use Doctrine\ORM\EntityManagerInterface;
use Generator;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Throwable;
use function array_key_exists;
use function count;
use function iterator_to_array;

final readonly class ProductExporter
{
    public function __construct(
        private ShopifyConfig $shopifyConfig,
        private EntityManagerInterface $em,
        private ShopifyConfigurator $configurator,
        private DenormalizerInterface $denormalizer,
        private Sources $dataSource,
        private Accessors $accessors,
        private Injectors $injectors,
        private EventDispatcherInterface $dispatcher,
        private LoggerInterface $logger,
    ) {
    }

    public function export(?array $skus = null): void
    {
        if (false === $this->configurator->ensureAttributesConfiguration()) {
            $this->logger->debug('configuration cannot be ensured, do not go further');

            return;
        }
        if (null === $catalog = $this->getCatalog()) {
            $this->logger->debug('catalog cannot be empty');

            return;
        }

        $source = new Source(
            ChannelSourceEnum::PRODUCT,
            ['catalog' => $catalog, 'sku' => $skus],
            ['updatedAt' => 'ASC'],
        );

        $this->doExport($source);
    }

    private function doExport(Source $source): void
    {
        $client = ShopifyClient::initClient(
            token: $this->shopifyConfig->token,
            shop: $this->shopifyConfig->shop,
        );

        $locations = $this->ourWarehousesToLocations($client);

        $setQuery = new ProductSet();

        $representationAttribute = ShopifyConfigurationEnum::getRepresentationAttribute($this->shopifyConfig->connector->code);
        $gidAttribute = ShopifyConfigurationEnum::getGidAttribute($this->shopifyConfig->connector->code);
        $currentScope = $this->shopifyConfig->connector->code;

        $mapping = $this->getMapping();

        $skus = [];
        /** @var FlatProduct $item */
        foreach ($this->dataSource->get($source) as $item) {
            if (array_key_exists($item->sku, $skus)) {
                continue;
            }

            $skus[$item->sku] = $item;
        }

        /** @var FlatProduct $item */
        foreach ($skus as $item) {
            $data = $this->accessors->map($mapping, $item);

            $currentValue = JsonUtils::decode($item->values->locate(Header::attribute($representationAttribute, scope: $currentScope))?->data);

            if (isset($data['variantStock'])) {
                $stock = [];
                foreach ($data['variantStock'] as $warehouse => $quantity) {
                    if (!isset($locations[$warehouse])) {
                        continue;
                    }

                    $stock[] = ['locationId' => $locations[$warehouse], 'quantity' => $quantity, 'name' => 'available'];
                }

                $data['variantStock'] = $stock;
            }

            $input = $this->mappingToGqlInput($data, $currentValue);

            try {
                $response = $client->request($setQuery, $input)->toArray();

                $shopifyProduct = $response['data']['productSet']['product'] ?? null;
                if (null === $shopifyProduct) {
                    throw new RuntimeException(JsonUtils::encode($response));
                }

                foreach ($shopifyProduct['variants']['edges'] ?? [] as $variant) {
                    if (false === ($variant['node']['inventoryItem']['tracked'] ?? true)) {
                        $client->request(new InventoryItemUpdate(), new InventoryItemInput($variant['node']['inventoryItem']['id']))->toArray();
                    }

                    foreach ($locations as $locationId) {
                        $client->request(new InventoryActivate(), new InventoryActivateInput($variant['node']['inventoryItem']['id'], $locationId))->toArray();
                    }
                }

                $g = static function () use ($item, $shopifyProduct, $representationAttribute, $gidAttribute, $currentScope): Generator {
                    yield new DataImport($item->uuid, $representationAttribute, null, $currentScope, [new DataImportItem(value: JsonUtils::encode($shopifyProduct))]);
                    yield new DataImport($item->uuid, $gidAttribute, null, $currentScope, [new DataImportItem(value: $shopifyProduct['id'])]);
                };

                $this->injectors->inject(ApiTypeEnum::PRODUCT_DATA, $g());
                $this->dispatcher->dispatch(new DocumentsUpdatedEvent(type: ApiTypeEnum::PRODUCT, uuids: new Uuids([$item->uuid])));

                $this->logger->debug('product {uuid} pushed to shopify', ['uuid' => $item->uuid->toRfc4122()]);
            } catch (Throwable $e) {
                $this->logger->error('unable to save shopify product', [$e]);
            }
        }
    }

    private function getCatalog(): ?string
    {
        try {
            $tableCatalogScope = TableEnum::CATALOG_SCOPE;

            $query = <<<SQL
            SELECT catalog_code
            FROM {$tableCatalogScope}
            WHERE code = ?
            SQL;

            if (false !== $catalogCode = $this->em->getConnection()->fetchOne($query, [$this->shopifyConfig->connector->code])) {
                return $catalogCode;
            }
        } catch (Throwable $e) {
            $this->logger->error('unable to get catalog for this shopify', [
                'exception' => $e,
            ]);
        }

        return null;
    }

    /**
     * @todo find a way to get mapping from DB
     */
    private function getMapping(): Mapping
    {
        return new Mapping(columns: iterator_to_array($this->getMappingColumns(), false));
    }

    /**
     * @return Generator<MappingColumn>
     * @todo enum for shopifyMetafieldType
     * @todo custom.vintage_hero_image => metafield de type file, non supporté actuellement
     */
    private function getMappingColumns(): Generator
    {
        yield new MappingColumn(
            UuidUtils::new(),
            new AttributeAccessor('title'),
            new ShopifyPropertyAccessor(ShopifyPropertyEnum::TITLE),
        );

        $statusActive = ShopifyStatusEnum::ACTIVE;
        $statusDraft = ShopifyStatusEnum::DRAFT;

        $status = <<<FORMULA
        document.status ? '{$statusActive}' : '{$statusDraft}'
        FORMULA;

        yield new MappingColumn(
            UuidUtils::new(),
            new FormulaAccessor($status),
            new ShopifyPropertyAccessor(ShopifyPropertyEnum::STATUS),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new AttributeAccessor('product_short_description'),
            new ShopifyPropertyAccessor(ShopifyPropertyEnum::DESCRIPTION_HTML),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new AttributeAccessor('pre_arrival'),
            new ShopifyMetafieldAccessor('pre_arrival'),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new FormulaAccessor('has_value("estate_classification") ? value("estate_classification") : value("millesimes_estate_classification")'),
            new ShopifyMetafieldAccessor('estate_classification'),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new AttributeAccessor('appellation'),
            new ShopifyMetafieldAccessor('estate_appellation_1'),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new AttributeAccessor('wine_country'),
            new ShopifyMetafieldAccessor('country_of_origin'),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new AttributeAccessor('key_wine_facts_region'),
            new ShopifyMetafieldAccessor('region_of_origin'),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new FormulaAccessor('value("vintage") < 1991 ? "Ready to Drink" : "Drink or Cellar"'),
            new ShopifyMetafieldAccessor('current_condition'),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new AttributeAccessor('key_wine_facts_color'),
            new ShopifyMetafieldAccessor('wine_color'),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new AttributeAccessor('product_short_description'),
            new ShopifyMetafieldAccessor('product_short_description'),
        );

        $keyWineFact = <<<FORMULA
        shopify_html(string_concat(
          '<ul>',
          has_value('wine_country') ? string_concat('<li><strong>Country of Origin</strong>: ', value('wine_country'), '</li>') : '',
          has_value('key_wine_facts_region') ? string_concat('<li><strong>Region of Origin</strong>: ', value('key_wine_facts_region'), '</li>') : '',
          has_value('key_wine_facts_varietals') ? string_concat('<li><strong>Varietals</strong>: ', value('key_wine_facts_varietals'), '</li>') : '',
          has_value('key_wine_facts_color') ? string_concat('<li><strong>Type</strong>: ', value('key_wine_facts_color'), '</li>') : '',
          has_value('key_wine_facts_alcohol_content') ? string_concat('<li><strong>Alcohol Content</strong>: ', value('key_wine_facts_alcohol_content'), '</li>') : '',
          has_value('key_wine_facts_vinification') ? string_concat('<li><strong>Vinification</strong>: ', value('key_wine_facts_vinification'), '</li>') : '',
          has_value('key_wine_facts_aging_potential') ? string_concat('<li><strong>Aging potential</strong>: ', value('key_wine_facts_aging_potential'), '</li>') : '',
          '</ul>'
        ))
        FORMULA;

        yield new MappingColumn(
            UuidUtils::new(),
            new FormulaAccessor($keyWineFact),
            new ShopifyMetafieldAccessor(
                shopifyMetafieldKey: 'key_wine_facts',
                shopifyMetafieldType: ShopifyAttributeEnum::RICH_TEXT_FIELD,
            ),
        );

        // ratings
        for ($i = 1; $i <= 3; $i++) {
            yield new MappingColumn(
                UuidUtils::new(),
                new AttributeAccessor("professional_rating_{$i}"),
                new ShopifyMetafieldAccessor("professional_rating_{$i}"),
            );

            yield new MappingColumn(
                UuidUtils::new(),
                new AttributeAccessor("professional_rating_author_{$i}"),
                new ShopifyMetafieldAccessor("professional_rating_author_{$i}"),
            );

            yield new MappingColumn(
                UuidUtils::new(),
                new AttributeAccessor("professional_rating_author_long_form_{$i}"),
                new ShopifyMetafieldAccessor("professional_rating_author_long_form_{$i}"),
            );

            $professionalCriticRatingQuote = <<<FORMULA
            shopify_html(string_concat(
                "<p>",
                value("professional_critic_rating_quote_{$i}"),
                "</p>"
            ))
            FORMULA;

            yield new MappingColumn(
                UuidUtils::new(),
                new FormulaAccessor($professionalCriticRatingQuote),
                new ShopifyMetafieldAccessor(
                    shopifyMetafieldKey: "professional_critic_rating_quote_{$i}",
                    shopifyMetafieldType: ShopifyAttributeEnum::RICH_TEXT_FIELD,
                ),
            );
        }

        yield new MappingColumn(
            UuidUtils::new(),
            new FormulaAccessor('has_value("vintage") ? value("vintage") : value("millesimes_vintage")'),
            new ShopifyMetafieldAccessor('vintage'),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new AttributeAccessor('badges'),
            new ShopifyMetafieldAccessor('badges'),
        );

        $estateDescription = <<<FORMULA
        shopify_html(value('estate_description'))
        FORMULA;

        yield new MappingColumn(
            UuidUtils::new(),
            new FormulaAccessor($estateDescription),
            new ShopifyMetafieldAccessor(
                shopifyMetafieldKey: 'estate_description',
                shopifyMetafieldType: ShopifyAttributeEnum::RICH_TEXT_FIELD,
            ),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new AttributeAccessor('estate_region'),
            new ShopifyMetafieldAccessor('estate_region'),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new AttributeAccessor('estate_terroir'),
            new ShopifyMetafieldAccessor('estate_terroir'),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new AttributeAccessor('estate_notes'),
            new ShopifyMetafieldAccessor('estate_notes'),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new AttributeAccessor('infotip_hover'),
            new ShopifyMetafieldAccessor('infotip_hover'),
        );

        $vintageHeroNotes = <<<FORMULA
        shopify_html(value('vintage_hero_notes'))
        FORMULA;

        yield new MappingColumn(
            UuidUtils::new(),
            new FormulaAccessor($vintageHeroNotes),
            new ShopifyMetafieldAccessor(
                shopifyMetafieldKey: 'vintage_hero_notes',
                shopifyMetafieldType: ShopifyAttributeEnum::RICH_TEXT_FIELD,
            ),
        );

        $vintageHeadline = <<<FORMULA
        shopify_html(value('vintage_headline'))
        FORMULA;

        yield new MappingColumn(
            UuidUtils::new(),
            new FormulaAccessor($vintageHeadline),
            new ShopifyMetafieldAccessor(
                shopifyMetafieldKey: 'vintage_headline',
                shopifyMetafieldType: ShopifyAttributeEnum::RICH_TEXT_FIELD,
            ),
        );

        $vintageHeroOverlay = <<<FORMULA
        shopify_html(value('vintage_hero_overlay'))
        FORMULA;

        yield new MappingColumn(
            UuidUtils::new(),
            new FormulaAccessor($vintageHeroOverlay),
            new ShopifyMetafieldAccessor(
                shopifyMetafieldKey: 'vintage_hero_overlay',
                shopifyMetafieldType: ShopifyAttributeEnum::RICH_TEXT_FIELD,
            ),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new FormulaAccessor('has_value("images") ? value_media_collection_thumbnails("images", "1000x1000-png") : value_media_collection_thumbnails("millesimes_images", "1000x1000-png")'),
            new ShopifyMediaAccessor('IMAGE'),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new FormulaAccessor('shopify_products_option(has_value("size") ? (value("size") === "75 cl" ? "750 ml" : value("size")) : value("millesimes_size"), "Size")'),
            new ShopifyPropertyAccessor(ShopifyPropertyEnum::PRODUCT_OPTIONS),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new AttributeAccessor('price'),
            new ShopifyPropertyAccessor(ShopifyPropertyEnum::VARIANT_PRICE),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new FormulaAccessor('shopify_product_stock()'),
            new ShopifyPropertyAccessor(ShopifyPropertyEnum::VARIANT_STOCK),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new PropertyAccessor('sku'),
            new ShopifyPropertyAccessor(ShopifyPropertyEnum::VARIANT_SKU),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new RawAccessor('gid://shopify/TaxonomyCategory/fb-1-1-7'), // @see https://shopify.github.io/product-taxonomy/releases/unstable/?categoryId=gid%3A%2F%2Fshopify%2FTaxonomyCategory%2Ffb-1-1-7&shpxid=62ab2c2f-6552-40BE-C007-322BA2FA0BDA
            new ShopifyPropertyAccessor(ShopifyPropertyEnum::PRODUCT_CATEGORY),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new AttributeAccessor('vendor'),
            new ShopifyPropertyAccessor(ShopifyPropertyEnum::PRODUCT_VENDOR),
        );

        yield new MappingColumn(
            UuidUtils::new(),
            new FormulaAccessor('shopify_products(value("vintage_products"), "shopify", "title")'),
            new ShopifyMetafieldAccessor(
                shopifyMetafieldKey: 'vintage_products',
                shopifyMetafieldType: ShopifyAttributeEnum::LIST_PRODUCT_REFERENCE,
            ),
        );
    }

    private function mappingToGqlInput(array $data, ?array $currentValue = null): ProductSetInput
    {
        if (null !== $currentValue) {
            $data['id'] = $currentValue['id'];
        }

        if (0 !== count($data['metafields'] ?? [])) {
            $data['metafields'] = array_values($data['metafields']);

            $data['metafields'] = array_map(
                fn(array $metafield): MetafieldInput => $this->denormalizer->denormalize($metafield, MetafieldInput::class),
                $data['metafields'],
            );
        }

        if (0 !== count($data['media'] ??= [])) {
            $data['media'] = array_values($data['media']);

            $data['media'] = array_map(
                fn(array $media): MediaInput => $this->denormalizer->denormalize($media, MediaInput::class),
                $data['media'],
            );
        }

        return $this->denormalizer->denormalize($data, ProductSetInput::class);
    }

    private function ourWarehousesToLocations(ShopifyClient $client): array
    {
        $locations = [];

        try {
            $tableHubSource = TableEnum::HUB_SOURCE;
            // country_code is required for shopify
            $query = <<<SQL
                SELECT code, country_code
                FROM {$tableHubSource}
                WHERE country_code IS NOT NULL
            SQL;

            $warehouses = $this->em->getConnection()->fetchAllKeyValue($query);
        } catch (Throwable $e) {
            $this->logger->error('unable to get our warehouses', [
                'exception' => $e,
            ]);

            return $locations;
        }

        $shopifyLocations = $client->request(new QueryLocations())->toArray()['data']['locations']['edges'] ?? [];

        foreach ($shopifyLocations as $location) {
            if (array_key_exists($location['node']['name'], $warehouses)) {
                $locations[$location['node']['name']] = $location['node']['id'];
                unset($warehouses[$location['node']['name']]);
            }
        }

        foreach ($warehouses as $code => $countryCode) {
            $location = $client->request(new AddLocation(), new AddLocationInput((string)$code, $countryCode))->toArray();
            $locations[$code] = $location['data']['locationAdd']['location']['id'];
        }

        return $locations;
    }
}
