<?php declare(strict_types=1);

namespace App\Connector\OpinionSystem\Client\Model;

use App\Utils\DateUtils;
use DateTime;

final readonly class Survey
{
    private function __construct(
        public int $id,
        public int $companyId,
        public string $description,
        public string $comment,
        public string $name,
        public int $rating,
        public DateTime $date,
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            $data['survey_id'],
            (int)$data['company_id'],
            (string)$data['invoice_detail'],
            (string)$data['comment'],
            (string)$data['name'],
            (int)$data['rating'],
            DateUtils::new($data['answer']),
        );
    }
}
