<?php declare(strict_types=1);

namespace App\Connector\ShoppingFeed\Client\Model;

use App\Module\Hub\Enum\HubOrderStateEnum;
use App\Module\Injector\Model\HubOrderImport;
use App\Module\Injector\Model\HubOrderItemImport;
use App\Utils\DateUtils;
use DateTime;
use Generator;
use function array_map;

class ShoppingFeedOrder implements ShoppingFeedModelInterface
{
    /**
     * @param array<ShoppingFeedItem> $items
     * @param array<ShoppingFeedError> $errors
     */
    private function __construct(
        public int $id,
        public int $storeId,
        public ShoppingFeedChannel $channel,
        public string $reference,
        public bool $isTest,
        public DateTime $createdAt,
        public DateTime $updatedAt,
        public string $status,
        public bool $anonymized,
        public ShoppingFeedAddress $shippingAddress,
        public ShoppingFeedAddress $billingAddress,
        public ShoppingFeedPayment $payment,
        public ShoppingFeedShipment $shipment,
        public ?float $commission,
        public ?array $additionalFields,
        public array $errors,
        public array $items,
        public string $fulfilledBy,
        public ?array $itemsReferencesAliases,
        public ?string $storeReference,
        public ?string $acknowledgedAt,
        public ?string $latestShipDate,
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            $data['id'],
            $data['storeId'],
            ShoppingFeedChannel::fromArray($data['_embedded']['channel'] ?? []),
            $data['reference'],
            $data['isTest'],
            DateUtils::new($data['createdAt']),
            DateUtils::new($data['updatedAt']),
            $data['status'],
            $data['anonymized'],
            ShoppingFeedAddress::fromArray($data['shippingAddress']),
            ShoppingFeedAddress::fromArray($data['billingAddress']),
            ShoppingFeedPayment::fromArray($data['payment']),
            ShoppingFeedShipment::fromArray($data['shipment']),
            $data['commission'],
            $data['additionalFields'],
            array_map(
                static fn(array $item): ShoppingFeedError => ShoppingFeedError::fromArray($item),
                $data['errors'],
            ),
            array_map(
                static fn(array $item): ShoppingFeedItem => ShoppingFeedItem::fromArray($item),
                $data['items'],
            ),
            $data['fulfilledBy'],
            $data['itemsReferencesAliases'],
            $data['storeReference'],
            $data['acknowledgedAt'],
            $data['latestShipDate'],
        );
    }

    /** @return Generator<HubOrderImport> */
    public function toImport(?string $owner = null): Generator
    {
        $tax = null;
        foreach ($this->items as $item) {
            $tax += $item->taxAmount;
        }

        yield new HubOrderImport(
            state: HubOrderStateEnum::PENDING_FOR_VALIDATION,
            billing: $this->billingAddress->toAddress(),
            shipping: $this->shippingAddress->toAddress(),
            price: $this->payment->totalAmount,
            taxPart: $tax,
            shippingPart: $this->payment->shippingAmount,
            currencyCode: $this->payment->currency,
            owner: $owner,
            ownerIdentifier: (string)$this->id,
            origin: $this->channel->name,
            originIdentifier: $this->reference,
            orderItems: array_map(callback: fn(ShoppingFeedItem $item): HubOrderItemImport => new HubOrderItemImport(
                productSku: $item->reference,
                productName: $item->name ?? 'N/A',
                price: (float)bcmul((string)$item->price, (string)$item->quantity, 2),
                taxPart: $item->taxAmount,
                unitPrice: $item->price,
                orderedQuantity: $item->quantity,
            ), array: $this->items),
            storeId: (string)$this->storeId,
        );
    }
}
