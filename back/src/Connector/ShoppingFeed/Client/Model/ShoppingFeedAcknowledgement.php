<?php declare(strict_types=1);

namespace App\Connector\ShoppingFeed\Client\Model;

use App\Utils\DateUtils;
use DateTime;

class ShoppingFeedAcknowledgement implements ShoppingFeedModelInterface
{
    private function __construct(
        public int $id,
        public ?string $storeReference,
        public string $status,
        public string $message,
        public ?DateTime $acknowledgedAt = null,
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            $data['id'],
            $data['storeReference'],
            $data['status'],
            $data['message'],
            DateUtils::new($data['acknowledgedAt'] ?? 'now'),
        );
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'storeReference' => $this->storeReference,
            'status' => $this->status,
            'message' => $this->message,
            'acknowledgedAt' => $this->acknowledgedAt->format('c'),
        ];
    }
}
