<?php declare(strict_types=1);

namespace App\Connector\ShoppingFeed\Client\Model;

use App\Utils\DateUtils;
use DateTime;

class ShoppingFeedTicket implements ShoppingFeedModelInterface
{
    public function __construct(
        public string $id,
        public string $batchId,
        public string $type,
        public string $state,
        public DateTime $scheduledAt,
        public ?DateTime $canceledAt,
        public ShoppingFeedTicketPayload $payload,
        public ShoppingFeedTicketResult $result,
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            $data['id'],
            $data['batchId'],
            $data['type'],
            $data['state'],
            DateUtils::new($data['scheduledAt']),
            DateUtils::new($data['canceledAt'] ?? null),
            ShoppingFeedTicketPayload::fromArray($data['payload']),
            ShoppingFeedTicketResult::fromArray($data['result'])
        );
    }
}
