<?php declare(strict_types=1);

namespace App\Connector\Akeneo\Command;

use App\Api\Model\Uuids;
use App\Connector\Akeneo\Akeneo;
use App\Connector\Akeneo\Message\AkeneoImportProductsMessage;
use App\Connector\Akeneo\Model\AkeneoProduct;
use App\Utils\ArrUtils;
use App\Utils\DateUtils;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;
use function array_combine;
use function array_map;
use function count;
use function iterator_to_array;

final class ImportProductsCommand extends Command
{
    public function __construct(
        private readonly Akeneo $akeneo,
        private readonly MessageBusInterface $bus,
    ) {
        parent::__construct();
    }

    public function configure(): void
    {
        $this
            ->addOption('since', null, InputOption::VALUE_OPTIONAL, 'Valid php datetime format.')
            ->addArgument('skus', InputArgument::IS_ARRAY, 'Product skus to import.')
            ->addOption('now', null, InputOption::VALUE_NONE, 'Run now.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>importing</info>');

        if (null !== $since = $input->getOption('since')) {
            try {
                $since = DateUtils::new($since);
                $output->writeln("\tsince: {$since->format('Y-m-d H:i:s')}");
            } catch (Throwable) {
                $since = null;
            }
        }

        $skus = ArrUtils::vfu($input->getArgument('skus'));
        $uuids = new Uuids();

        if (0 === count($skus)) {
            $output->writeln('no sku provided');
        } else {
            /** @var AkeneoProduct[] $products */
            $products = iterator_to_array($this->akeneo->getProducts(skus: $skus), false);
            $products = array_combine(
                array_map(static fn(AkeneoProduct $product): string => $product->identifier, $products),
                $products,
            );

            $table = new Table($output);
            $table->setHeaders(['SKU', 'UUID']);
            foreach ($skus as $sku) {
                $product = $products[$sku] ?? null;
                if (null !== $uuid = $product?->getUuid()) {
                    $uuids->add($uuid);
                }
                $table->addRow([$sku, $uuid->toRfc4122()]);
            }
            $table->render();
        }

        $this->bus->dispatch(new AkeneoImportProductsMessage(
            uuids: $uuids->isEmpty() ? null : $uuids,
            since: $since ?? null,
        )->now($input->getOption('now')));

        return self::SUCCESS;
    }
}
