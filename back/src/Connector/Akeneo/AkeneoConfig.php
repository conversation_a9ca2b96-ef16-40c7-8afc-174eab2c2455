<?php declare(strict_types=1);

namespace App\Connector\Akeneo;

use App\Bridge\Platform\Model\Connector;
use App\Connector\Akeneo\Model\AkeneoAttribute;
use App\Connector\Akeneo\Model\AkeneoProduct;
use App\Contracts\Enum\ClientEnum;
use App\Utils\DateUtils;
use SensitiveParameter;
use Throwable;
use function array_key_exists;
use function count;
use function in_array;
use function is_array;
use function is_string;

final readonly class AkeneoConfig
{
    private array $flatBrandNames;

    public function __construct(
        public Connector $connector,
        public string $url,
        #[SensitiveParameter]
        public string $clientId,
        #[SensitiveParameter]
        public string $clientSecret,
        #[SensitiveParameter]
        public string $username,
        #[SensitiveParameter]
        public string $password,
        public array $locales,
        public array $brandNames = [],
        public array $attributeSuffixes = [],
        public array $attributeSearchables = [],
        public bool $importFamilies = false,
        public bool $importParents = false,
    ) {
        $this->flatBrandNames = array_keys($this->brandNames);
    }

    public function isAcceptable(AkeneoProduct $product, string $client): bool
    {
        if (!$product->enabled) {
            return false;
        }

        // custom lvmh
        if (ClientEnum::LVMH === $client || ClientEnum::LVMH_STAGING === $client) {
            $now = DateUtils::now();

            $mustFilterBrandNames = 0 !== count($this->flatBrandNames);
            $hasBrandName = false;
            $productValues = $product->getValues();
            foreach ($productValues as $code => $codeValues) {
                if ('brand_name' === $code) {
                    $hasBrandName = true;
                    foreach ($codeValues as $codeValue) {
                        // some brand only accept some Trading Partners
                        // it's defined in self::$brandNames, where key is brand_name and values are accepted TP
                        if (is_string($codeValue->data) && 0 !== count($this->brandNames[$codeValue->data] ?? [])) {
                            // attribute is required
                            if (!array_key_exists('trading_partners', $productValues)) {
                                return false;
                            }

                            foreach ($productValues['trading_partners'] as $partnerValues) {
                                if (!is_array($partnerValues->data)) {
                                    return false;
                                }

                                $isValidTradingPartner = false;
                                foreach ($partnerValues->data as $partnerValue) {
                                    if (in_array($partnerValue, $this->brandNames[$codeValue->data] ?? [], true)) {
                                        $isValidTradingPartner = true;
                                        break;
                                    }
                                }
                                if (!$isValidTradingPartner) {
                                    return false;
                                }
                            }
                        }

                        if (
                            $mustFilterBrandNames
                            && is_string($codeValue->data)
                            && !in_array($codeValue->data, $this->flatBrandNames, true)
                        ) {
                            return false;
                        }
                    }
                }

                if ('end_date' === $code || 'end_date_of_quotas' === $code) {
                    foreach ($codeValues as $codeValue) {
                        if (is_string($codeValue->data)) {
                            try {
                                $date = DateUtils::new($codeValue->data);
                                if ($date < $now) {
                                    return false;
                                }
                            } catch (Throwable) {
                            }
                        }
                    }
                }
            }
            if ($mustFilterBrandNames && !$hasBrandName) {
                return false;
            }
        }

        return true;
    }

    public function attributeIsSearchable(AkeneoAttribute $attribute): bool
    {
        if (0 !== count($this->attributeSearchables)) {
            return in_array($attribute->code, $this->attributeSearchables, true);
        }

        return $attribute->useable_as_grid_filter;
    }
}
