<?php declare(strict_types=1);

namespace App\Connector\Akeneo\Importer;

use App\Api\Model\Codes;
use App\Bridge\Counter\Message\SyncCountersMessage;
use App\Bridge\Platform\Environment;
use App\Connector\Akeneo\Akeneo;
use App\Connector\Akeneo\AkeneoConfig;
use App\Connector\Akeneo\AkeneoFeedback;
use App\Connector\Akeneo\Enum\AkeneoAttributeTypeEnum;
use App\Connector\Akeneo\Utils\MeasurementUtils;
use App\Contracts\Enum\ApiTypeEnum;
use App\Contracts\Enum\TableEnum;
use App\Contracts\SqlBuilderFactory;
use App\Contracts\Type\Enum\AttributeTypeEnum;
use App\Module\Injector\Injectors;
use App\Module\Injector\Model\AttributeGroupImport;
use App\Module\Injector\Model\AttributeImport;
use App\Module\Injector\Model\AttributeOptionImport;
use App\Module\Injector\Model\CurrencyImport;
use App\Module\Injector\Model\LocaleImport;
use App\Module\Injector\Model\MeasureFamilyImport;
use App\Module\Injector\Model\Result\InjectionFailure;
use App\Module\Injector\Model\Result\InjectionSuccess;
use App\Module\Injector\Model\ScopeImport;
use App\Module\Injector\Model\UnitImport;
use App\Utils\BulkUtils;
use App\Utils\DateUtils;
use App\Utils\JsonUtils;
use App\Validator\Constraints\Translation;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Generator;
use Psr\Log\LoggerInterface;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

// @todo there is no cleanup for attribute that were removed from akeneo
// @todo there is no cleanup for attribute options that were removed from akeneo
final readonly class ConfigurationImporter
{
    private DateTime $importedAt;

    public function __construct(
        private AkeneoFeedback $akeneoFeedback,
        private AkeneoConfig $config,
        private Akeneo $client,
        private EntityManagerInterface $em,
        private MessageBusInterface $bus,
        private LoggerInterface $logger,
        private Injectors $injectors,
        private SqlBuilderFactory $sqlBuilderFactory,
        private Environment $environment,
        private Filesystem $fs,
        private string $dir,
    ) {
        $this->importedAt = DateUtils::now();
    }

    public function importAll(): void
    {
        try {
            // locale
            $localeCodesFailure = new Codes();
            $localeCodesSuccess = new Codes();
            $this->injectors->consume(
                results: $this->injectors->get(ApiTypeEnum::LOCALE)->inject($this->generateLocaleImports()),
                onFailure: self::onFailureCodes($localeCodesFailure),
                onSuccess: self::onSuccessCodes($localeCodesSuccess),
            );
            if ($localeCodesFailure->isEmpty()) {
                $this->deleteAllButCodes(TableEnum::LOCALE, $localeCodesSuccess);
            } else {
                $this->logger->error('partial injection detected, locales were not cleaned', [
                    'failures' => $localeCodesFailure->count(),
                ]);
            }

            // attribute_group
            $attributeGroupCodesFailure = new Codes();
            $attributeGroupCodesSuccess = new Codes();
            $this->injectors->consume(
                results: $this->injectors->get(ApiTypeEnum::ATTRIBUTE_GROUP)->inject($this->generateAttributeGroupImports()),
                onFailure: self::onFailureCodes($attributeGroupCodesFailure),
                onSuccess: self::onSuccessCodes($attributeGroupCodesSuccess),
            );
            if ($attributeGroupCodesFailure->isEmpty()) {
                $this->deleteAllButCodes(TableEnum::ATTRIBUTE_GROUP, $attributeGroupCodesSuccess);
            } else {
                $this->logger->error('partial injection detected, attribute groups were not cleaned', [
                    'failures' => $attributeGroupCodesFailure->count(),
                ]);
            }

            // attribute
            $attributeCodesFailure = new Codes();
            $attributeCodesSuccess = new Codes();
            $this->injectors->consume(
                results: $this->injectors->get(ApiTypeEnum::ATTRIBUTE)->inject($this->generateAttributeImports()),
                onFailure: self::onFailureCodes($attributeCodesFailure),
                onSuccess: self::onSuccessCodes($attributeCodesSuccess),
            );
            if ($attributeCodesFailure->isEmpty()) {
                $this->deleteAllButCodes(TableEnum::ATTRIBUTE, $attributeCodesSuccess);
            } else {
                $this->logger->error('partial injection detected, attributes were not cleaned', [
                    'failures' => $attributeCodesFailure->count(),
                ]);
            }

            // attribute_option
            // @todo le delete est très difficile…
            $this->injectors->consume(
                results: $this->injectors->get(ApiTypeEnum::ATTRIBUTE_OPTION)->inject($this->generateAttributeOptionImports()),
            );

            // measure_family / unit
            // @todo le delete est moins important
            foreach ($this->generateMeasurementFamilyImports() as $measurementFamilyImport) {
                $this->injectors->consume($this->injectors->get(ApiTypeEnum::MEASURE_FAMILY)->inject(BulkUtils::one($measurementFamilyImport)));

                $this->injectors->consume($this->injectors->get(ApiTypeEnum::UNIT)->inject((static fn(): Generator => yield from $measurementFamilyImport->units)()));
                $this->injectors->get(ApiTypeEnum::MEASURE_FAMILY)->postInject();
            }

            // scope
            $channelCodesFailure = new Codes();
            $channelCodesSuccess = new Codes();
            $this->injectors->consume(
                results: $this->injectors->get(ApiTypeEnum::SCOPE)->inject($this->generateChannelImports()),
                onFailure: self::onFailureCodes($channelCodesFailure),
                onSuccess: self::onSuccessCodes($channelCodesSuccess),
            );
            if ($channelCodesFailure->isEmpty()) {
                $this->deleteAllButCodes(TableEnum::SCOPE, $channelCodesSuccess);
            } else {
                $this->logger->error('partial injection detected, channels were not cleaned', [
                    'failures' => $channelCodesFailure->count(),
                ]);
            }

            // currency
            $currencyCodesFailure = new Codes();
            $currencyCodesSuccess = new Codes();
            $this->injectors->consume(
                results: $this->injectors->get(ApiTypeEnum::CURRENCY)->inject($this->generateCurrenciesImports()),
                onFailure: self::onFailureCodes($currencyCodesFailure),
                onSuccess: self::onSuccessCodes($currencyCodesSuccess),
            );
            if ($currencyCodesFailure->isEmpty()) {
                $this->deleteAllButCodes(TableEnum::CURRENCY, $currencyCodesSuccess);
            } else {
                $this->logger->error('partial injection detected, currencies were not cleaned', [
                    'failures' => $currencyCodesFailure->count(),
                ]);
            }
        } catch (Throwable $e) {
            $this->logger->error('unable to import all akeneo configurations', [$e]);
        }

        $this->bus->dispatch(new SyncCountersMessage());
    }

    private static function onSuccessCodes(Codes $codes): callable
    {
        return function (InjectionSuccess $success) use ($codes): void {
            $codes->add($success->import->getId());
        };
    }

    private static function onFailureCodes(Codes $codes): callable
    {
        return function (InjectionFailure $failure) use ($codes): void {
            $codes->add($failure->import->getId());
        };
    }

    /**
     * @return Generator<LocaleImport>
     */
    private function generateLocaleImports(): Generator
    {
        foreach ($this->config->locales as $code) {
            yield new LocaleImport(
                code: $code,
                names: $this->environment->combine($code, maxLength: Translation::COMMON_MAX),
                owner: $this->config->connector->code,
            );
        }
    }

    /**
     * @return Generator<AttributeGroupImport>
     */
    private function generateAttributeGroupImports(): Generator
    {
        yield from $this->akeneoFeedback->generateAttributeGroups();

        $logs = [];
        foreach ($this->client->getAttributeGroups() as $attributeGroup) {
            yield new AttributeGroupImport(
                code: $attributeGroup->code,
                names: $this->environment->fromArray($attributeGroup->labels, forceDefault: true, maxLength: Translation::COMMON_MAX),
                status: $attributeGroup->status ?? true,
                owner: $this->config->connector->code,
            );
            $logs[] = $attributeGroup->toArray();
        }
        $this->logImportedData(ApiTypeEnum::ATTRIBUTE_GROUP, $logs);
    }

    /**
     * @return Generator<AttributeImport>
     */
    private function generateAttributeImports(): Generator
    {
        yield from $this->akeneoFeedback->generateAttributes();

        $logs = [];
        foreach ($this->client->getAttributes() as $attribute) {
            if (null === $type = AkeneoAttributeTypeEnum::from($attribute->type)) {
                $this->logger->error('unsupported akeneo attribute type', [
                    'code' => $attribute->code,
                    'type' => $attribute->type,
                ]);

                continue;
            }

            if ('sku' === $attribute->code) {
                $this->logger->info('do not import "sku" as an attribute');
                continue;
            }

            $parameters = [
                'family' => $attribute->metric_family,
            ];

            $names = $this->environment->fromArray(
                array: $attribute->labels,
                forceDefault: true,
                maxLength: Translation::COMMON_MAX,
                suffix: $this->config->attributeSuffixes[$attribute->code] ?? null,
            );

            yield new AttributeImport(
                attributeGroupCode: $attribute->group,
                code: $attribute->code,
                names: $names,
                type: $type,
                isLocalizable: $attribute->localizable,
                isScopable: $attribute->scopable,
                isSearchable: $this->config->attributeIsSearchable($attribute),
                parameters: $parameters,
                owner: $this->config->connector->code,
            );

            $logs[] = $attribute->toArray();
        }

        $this->logImportedData(ApiTypeEnum::ATTRIBUTE, $logs);
    }

    /**
     * @return Generator<AttributeOptionImport>
     */
    private function generateAttributeOptionImports(): Generator
    {
        $owner = $this->config->connector->code;

        yield from $this->akeneoFeedback->generateAttributeOptions();

        try {
            $tableAttribute = TableEnum::ATTRIBUTE;

            $query = <<<SQL
            SELECT code
            FROM {$tableAttribute}
            WHERE type IN (?, ?)
            SQL;

            $attributeCodes = $this
                ->em
                ->getConnection()
                ->executeQuery($query, [
                    AttributeTypeEnum::SELECT,
                    AttributeTypeEnum::MULTISELECT,
                ])
                ->fetchFirstColumn();

            $logs = [];
            foreach ($attributeCodes as $attributeCode) {
                if ($this->config->importFamilies && $this->akeneoFeedback->getFamilyAttributeCode() === $attributeCode) {
                    continue;
                }

                foreach ($this->client->getAttributeOptions($attributeCode) as $option) {
                    yield new AttributeOptionImport(
                        attributeCode: $attributeCode,
                        code: $option->code,
                        names: $this->environment->fromArray($option->labels, forceDefault: true, maxLength: Translation::COMMON_MAX),
                        owner: $owner,
                    );

                    $logs[] = $option->toArray();
                }
            }

            $this->logImportedData(ApiTypeEnum::ATTRIBUTE_OPTION, $logs);
        } catch (Throwable $e) {
            $this->logger->error('unable to generate attribute options import from akeneo', [$e]);
        }
    }

    /**
     * @return Generator<MeasureFamilyImport>
     */
    private function generateMeasurementFamilyImports(): Generator
    {
        foreach ($this->client->getMeasurementFamilies() as $family) {
            $units = [];
            foreach ($family['units'] ?? [] as $familyUnit) {
                $units[] = new UnitImport(
                    measureFamilyCode: $family['code'],
                    code: $familyUnit['code'],
                    names: $this->environment->fromArray($familyUnit['labels'], maxLength: Translation::COMMON_MAX),
                    symbol: $familyUnit['symbol'],
                    convertToStandard: MeasurementUtils::akeneoToOurConvertToStandard($familyUnit['convert_from_standard']),
                    convertFromStandard: MeasurementUtils::akeneoToOurConvertFromStandard($familyUnit['convert_from_standard']),
                );
            }

            yield new MeasureFamilyImport(
                code: $family['code'],
                names: $this->environment->fromArray($family['labels'], maxLength: Translation::COMMON_MAX),
                defaultUnit: $family['standard_unit_code'],
                units: $units,
                owner: $this->config->connector->code,
            );
        }
    }

    private function generateChannelImports(): Generator
    {
        foreach ($this->client->getChannels() as $channel) {
            // @todo We lose some information (not necessary but appreciable).
            yield new ScopeImport(
                code: $channel->code,
                names: $this->environment->fromArray($channel->labels, forceDefault: true, maxLength: Translation::COMMON_MAX),
                owner: $this->config->connector->code,
            );
        }
    }

    private function generateCurrenciesImports(): Generator
    {
        foreach ($this->client->getCurrencies() as $currency) {
            // filter from API didn't work, we must filter again here
            if (false === $currency->enabled) {
                continue;
            }

            yield new CurrencyImport(
                code: $currency->code,
                names: $this->environment->fromArray($currency->label ?? $currency->code, forceDefault: true, maxLength: Translation::COMMON_MAX),
                symbol: $currency->code,
                owner: $this->config->connector->code,
            );
        }
    }

    private function deleteAllButCodes(string $table, Codes $codes): void
    {
        $this
            ->sqlBuilderFactory
            ->createSqlDeleteBuilder($table, SqlBuilderFactory::STRATEGY_AND)
            ->add(owner: $this->config->connector->code)
            ->addNot(code: $codes->toArray())
            ->execute();
    }

    private function logImportedData(string $type, array $data): void
    {
        $dir = rtrim($this->dir, '/') . '/' . $this->config->connector->code . '/' . $this->importedAt->format('Ymd-His') . '/';

        $this->fs->mkdir($dir);

        $this->fs->dumpFile("{$dir}{$type}.json", JsonUtils::encode($data));
    }
}
