<?php declare(strict_types=1);

namespace App\Connector\Akeneo\Model;

use App\Contracts\Model\AbstractModel;
use App\Utils\DateUtils;
use DateTime;

/**
 * @property string $code
 * @property string $family
 * @property string $family_variant
 * @property string $parent
 * @property array $categories
 * @property array $values
 * @property string $created
 * @property string $updated
 */
class AkeneoProductModel extends AbstractModel
{
    /**
     * @phpstan-return array<string, AkeneoProductData[]>
     */
    public function getValues(): array
    {
        $values = [];
        foreach ($this->values as $code => $codeValues) {
            $values[$code] ??= [];
            foreach ($codeValues as $productData) {
                $values[$code][] = new AkeneoProductData($productData);
            }
        }

        return $values;
    }

    public function getCreatedAt(): DateTime
    {
        return DateUtils::new($this->created);
    }

    public function getUpdatedAt(): DateTime
    {
        return DateUtils::new($this->updated);
    }
}
