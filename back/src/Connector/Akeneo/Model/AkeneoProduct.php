<?php declare(strict_types=1);

namespace App\Connector\Akeneo\Model;

use App\Contracts\Model\AbstractModel;
use App\Utils\DateUtils;
use DateTime;
use Symfony\Component\Uid\Uuid;

/**
 * @property string $uuid
 * @property string $identifier
 * @property bool $enabled
 * @property string $family
 * @property array $categories
 * @property array $groups
 * @property string $parent
 * @property array $values
 * @property string $created
 * @property string $updated
 */
class AkeneoProduct extends AbstractModel
{
    public function getUuid(): Uuid
    {
        return Uuid::fromRfc4122($this->uuid);
    }

    /**
     * @phpstan-return array<string, AkeneoProductData[]>
     */
    public function getValues(): array
    {
        $values = [];
        foreach ($this->values as $code => $codeValues) {
            $values[$code] ??= [];
            foreach ($codeValues as $productData) {
                $values[$code][] = new AkeneoProductData($productData);
            }
        }

        return $values;
    }

    public function getCreatedAt(): DateTime
    {
        return DateUtils::new($this->created);
    }

    public function getUpdatedAt(): DateTime
    {
        return DateUtils::new($this->updated);
    }
}
