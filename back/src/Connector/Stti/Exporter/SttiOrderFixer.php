<?php declare(strict_types=1);

namespace App\Connector\Stti\Exporter;

use App\Contracts\Enum\TableEnum;
use App\Module\Hub\Enum\HubOrderStateEnum;
use App\Module\Hub\StateMachine\Enum\HubOrderTransitionEnum;
use App\Utils\DateUtils;
use App\Utils\SqlUtils;
use Doctrine\ORM\EntityManagerInterface;
use function count;
use function in_array;

final readonly class SttiOrderFixer
{
    public function __construct(
        private EntityManagerInterface $em,
    ) {
    }

    public function findErroredOrders(): array
    {
        $tableOrder = TableEnum::HUB_ORDER;
        $query = <<<SQL
        SELECT id
        FROM {$tableOrder}
        WHERE state = ?
        AND imported_at > ?
        SQL;

        $acceptedOrders = $this->em->getConnection()->fetchFirstColumn($query, [HubOrderStateEnum::VALIDATED, DateUtils::new('-1 month')->format('Y-m-d')]);

        if (0 === count($acceptedOrders)) {
            return [];
        }

        $placeholders = SqlUtils::createPlaceholdersFor($acceptedOrders);

        $tableOrderLog = TableEnum::HUB_ORDER_LOG;
        $query = <<<SQL
        SELECT order_id
        FROM {$tableOrderLog}
        WHERE order_id IN ({$placeholders})
        AND log = ?
        SQL;

        $sentToStti = $this->em->getConnection()->fetchFirstColumn($query, [...$acceptedOrders, HubOrderTransitionEnum::EXPORTED_STTI]);

        $toSend = [];
        foreach ($acceptedOrders as $orderId) {
            if (!in_array($orderId, $sentToStti)) {
                $toSend[] = $orderId;
            }
        }

        return $toSend;
    }
}
