<?php declare(strict_types=1);

namespace App\Connector\Stti\Exporter;

use App\Bridge\Flat\Entity\FlatProduct;
use App\Bridge\Platform\Platform;
use App\Connector\Stti\Enum\SttiPathEnum;
use App\Connector\Stti\SttiConfig;
use App\Contracts\Enum\ClientEnum;
use App\Module\Hub\Entity\HubOrder;
use App\Module\Hub\Entity\HubOrderItem;
use App\Module\Hub\Enum\HubLogStatusEnum;
use App\Module\Hub\HubLogCollector;
use App\Module\Hub\Model\HubLog;
use App\Module\Hub\StateMachine\Enum\HubOrderTransitionEnum;
use App\Utils\StrUtils;
use Doctrine\ORM\EntityManagerInterface;
use League\Flysystem\Config;
use League\Flysystem\FilesystemAdapter;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Component\Serializer\Encoder\CsvEncoder;
use Throwable;

final readonly class SttiOrderExporter
{
    public function __construct(
        private Platform $platform,
        private FilesystemAdapter $adapter,
        private LoggerInterface $logger,
        private EntityManagerInterface $em,
        private HubLogCollector $hubLogCollector,
        private SttiConfig $sttiConfig,
    ) {
    }

    public function export(int $orderId): bool
    {
        $order = $this->em->find(HubOrder::class, $orderId);

        if (null === $order) {
            $this->logger->error('Order can\'t be found and exported', [
                'orderId' => $orderId,
            ]);

            return false;
        }

        // order can be not ready because of synchros delta
        if (
            null === StrUtils::nullOrString($order->shipping->street)
            || (StrUtils::nullOrString($order->shipping->phone) && StrUtils::nullOrString($order->shipping->mobile))
        ) {
            $this->logger->info('Order is not ready, skipping it for this time', [
                'orderId' => $orderId,
            ]);

            return false;
        }

        $orderItems = $this->formatArrayFromOrder($order);
        if (empty($orderItems)) {
            $this->logger->error('unable to format order', ['order' => $order->id]);

            return false;
        }

        $path = SttiPathEnum::ORDERS_DIR . "/order_{$order->id}.csv";

        if ($this->platform->client->is(ClientEnum::GD)) {
            $path = "/var/www/html/out/orders/order_{$order->id}.csv";
        }

        if ($this->adapter->fileExists($path)) {
            $this->logger->error('Path already exists', ['path' => $path]);

            return false;
        }

        try {
            $contents = new CsvEncoder()->encode(
                $orderItems,
                'csv',
                [CsvEncoder::DELIMITER_KEY => $this->getDelimiter()],
            );

            if ($this->platform->client->is(ClientEnum::GD)) {
                $co = ssh2_connect($this->sttiConfig->connector->parameters['host']);
                if (false === $co) {
                    throw new RuntimeException('Cannot connect to GD server');
                }

                ssh2_auth_password($co,
                    $this->sttiConfig->connector->parameters['username'],
                    $this->sttiConfig->connector->parameters['password'],
                );
                $sftp = ssh2_sftp($co);

                $stream = fopen("ssh2.sftp://{$sftp}{$path}", 'w');
                if (false === $stream) {
                    throw new RuntimeException('Cannot open SFTP server');
                }

                fwrite($stream, $contents);
                fclose($stream);
            } else {
                $this->adapter->write(
                    $path,
                    $contents,
                    new Config(),
                );
            }
        } catch (Throwable $e) {
            $this->logger->error('unable to write distant file', [
                $e,
                'order' => $order->id,
            ]);

            return false;
        }

        $this->hubLogCollector->addLog(new HubLog($orderId, HubOrderTransitionEnum::EXPORTED_STTI, HubLogStatusEnum::SUCCESS));

        return true;
    }

    public function exportCanceled(HubOrder $order): bool
    {
        if ($this->platform->client->is(ClientEnum::GD)) {
            return true;
        }

        $path = SttiPathEnum::CANCELED_ORDERS_DIR . "order_{$order->id}.csv";

        if ($this->adapter->fileExists($path)) {
            $this->logger->error('Path already exists', ['path' => $path]);

            return false;
        }

        try {
            $contents = new CsvEncoder()->encode(
                ['order_identifier' => $order->id, 'order_shipment_identifier' => $order->id],
                'csv',
                [CsvEncoder::DELIMITER_KEY => $this->getDelimiter()],
            );

            $this->adapter->write(
                $path,
                $contents,
                new Config(),
            );

            $this->hubLogCollector->addLog(new HubLog($order->id, HubOrderTransitionEnum::EXPORTED_CANCELED_STTI, HubLogStatusEnum::SUCCESS));
            $this->hubLogCollector->saveAll();
        } catch (Throwable $e) {
            $this->logger->error('unable to write distant file', [
                $e,
                'order' => $order->id,
            ]);

            $this->hubLogCollector->addLog(new HubLog($order->id, HubOrderTransitionEnum::EXPORTED_CANCELED_FAILURE_STTI, HubLogStatusEnum::ERROR));
            $this->hubLogCollector->saveAll();

            return false;
        }

        return true;
    }

    private function formatArrayFromOrder(HubOrder $order): array
    {
        $orderArray = [
            'order_identifier' => $order->id,
            'order_shipment_identifier' => $order->id,
            'reference' => "{$this->platform->client->code}-{$order->invoiceId}",
            'order_date' => $order->createdAt->format('Y-m-d'),
            'order_shipping_name' => $order->shipping->lastname,
            'order_shipping_firstname' => $order->shipping->firstname,
            'order_shipping_address1' => $order->shipping->street,
            'order_shipping_address2' => $order->shipping->street2,
            'order_shipping_zipcode' => $order->shipping->postcode,
            'order_shipping_city' => $order->shipping->city,
            'order_shipping_country' => $order->shipping->country,
            'order_shipping_email' => $order->shipping->email,
            'order_shipping_phone' => StrUtils::nullOrString($order->shipping->phone) ?? $order->shipping->mobile ?? '',
        ];

        if ($this->platform->client->is(ClientEnum::GD)) {
            $orderArray = [
                ...$orderArray,
                'reference' => 'GD-' . $order->invoiceId,
                'order_customer_code' => '',
                'order_billing_name' => $order->billing->lastname,
                'order_billing_firstname' => $order->billing->firstname,
                'order_billing_address1' => $order->billing->street,
                'order_billing_address2' => $order->billing->street2,
                'order_billing_zipcode' => $order->billing->postcode,
                'order_billing_city' => $order->billing->city,
                'order_billing_country' => $order->billing->country,
                'order_billing_email' => $order->billing->email,
                'order_billing_phone' => StrUtils::nullOrString($order->billing->phone) ?? $order->billing->mobile ?? '',
            ];
        }

        $items = $this->em->getRepository(HubOrderItem::class)->findBy(['order' => $order]);
        if (empty($items)) {
            $this->logger->notice('no items linked to order', ['order' => $order->id]);

            return [];
        }

        $formattedArray = [];
        foreach ($items as $item) {
            if ($this->platform->client->is(ClientEnum::GD)) {
                $flatProduct = $this->em->getRepository(FlatProduct::class)->findOneBy(['sku' => $item->productSku]);

                $formattedArray[] = [
                    ...$orderArray,
                    'sku' => $item->productSku,
                    'ordered_quantity' => $item->orderedQuantity,
                    'ean' => '',
                    'product_name' => '',
                    'product_weight' => $flatProduct?->values?->find('product_weight')->data[0]->data ?? '',
                    'infos' => '',
                ];
            } else {
                $formattedArray[] = [
                    ...$orderArray,
                    'sku' => $item->productSku,
                    'quantity' => $item->orderedQuantity,
                ];
            }
        }

        return $formattedArray;
    }

    private function getDelimiter(): string
    {
        if ($this->platform->client->is(ClientEnum::GD)) {
            return ',';
        }

        return '|';
    }
}
