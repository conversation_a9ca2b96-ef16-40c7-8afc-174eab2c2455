<?php declare(strict_types=1);

namespace App\Connector\Gaia;

use App\Api\Model\Uuids;
use App\Bridge\Eav\Attributes;
use App\Bridge\Translation\Entity\Translations;
use App\Connector\Gaia\Model\GaiaDeliveryReport;
use App\Connector\Gaia\Model\GaiaHistory;
use App\Connector\Gaia\Model\GaiaIntegrationReport;
use App\Contracts\Enum\ApiTypeEnum;
use App\Contracts\Enum\TableEnum;
use App\Contracts\Model\Header;
use App\Module\Cache\DoctrineCache;
use App\Module\Injector\Injectors;
use App\Module\Injector\Model\DataImport;
use App\Utils\JsonUtils;
use App\Utils\UuidUtils;
use Doctrine\ORM\EntityManagerInterface;
use Generator;
use Psr\Log\LoggerInterface;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Uid\Uuid;
use function count;
use function date;
use function file_get_contents;
use function glob;
use function rtrim;
use function str_replace;

final readonly class Gaia
{
    public const string TYPE_INTEGRATION = 'integration';
    public const string TYPE_DELIVERY = 'delivery';
    public const string TYPE_DELIVERY_GAIA = 'delivery_gaia';
    public const string TYPE_DELIVERY_TRADING_PARTNER = 'delivery_trading_partner';
    public const string TYPE_ASSETS = 'assets';
    public const string SUBTYPE_PRODUCT = 'product';
    public const string SUBTYPE_LOGISTIC = 'logistic';
    public const string SUFFIX_DATE = 'date';
    public const string SUFFIX_STATUS = 'status';
    public const string SUFFIX_ERRORS = 'errors';
    public const string SUFFIX_TP_STATUS = 'tp_status';

    public function __construct(
        private GaiaConfig $config,
        private Injectors $injectors,
        private EntityManagerInterface $em,
        private DoctrineCache $doctrineCache,
        private Filesystem $fs,
        private Attributes $attributes,
        private LoggerInterface $logger,
        private string $dir,
    ) {
    }

    public function getDir(): string
    {
        return rtrim($this->dir, '/') . "/{$this->config->connector->code}";
    }

    public function guessType(string $path): ?string
    {
        return match (true) {
            str_contains($path, self::TYPE_INTEGRATION) => self::TYPE_INTEGRATION,
            str_contains($path, self::TYPE_DELIVERY) => self::TYPE_DELIVERY,
            default => null,
        };
    }

    public function inject(string $type, string $content, bool $saveReport = true, bool $now = false): int
    {
        // load file
        $count = 0;
        if (null !== $reports = JsonUtils::decode($content)) {
            // @todo continue saving the file for now
            if ($saveReport) {
                $date = date('Ymd-His');
                $uid = UuidUtils::new()->toRfc4122();
                $path = "{$this->getDir()}/{$date}-{$type}-{$uid}.json";
                $this->fs->dumpFile($path, JsonUtils::encode($reports));
            }

            // read and inject
            $generator = $this->generateProductData($type, $reports);
            $this->injectors->consume($this->injectors->get(ApiTypeEnum::PRODUCT_DATA)->inject($generator));

            /** @var Uuids $uuids */
            $uuids = $generator->getReturn();
            $this->injectors->postBulk(ApiTypeEnum::PRODUCT, $uuids->toBinaries(), now: $now);

            // @todo useless with current postInject but can be nice one day ?
            // $this->injectors->postInject(ApiTypeEnum::PRODUCT);

            // add count
            $count += count($reports);
        }

        return $count;
    }

    public function listFiles(): array
    {
        if (false === $paths = glob("{$this->getDir()}/*.json")) {
            return [];
        }

        return $paths;
    }

    /**
     * @return Generator<GaiaHistory>
     */
    public function generateHistories(string $identifier): Generator
    {
        if (Uuid::isValid($identifier)) {
            $searchUuid = Uuid::fromRfc4122($identifier);
        } else {
            $searchUuid = $this->doctrineCache->products->getUuid($identifier);
        }

        if (null === $searchUuid) {
            return;
        }

        $paths = $this->listFiles();
        foreach ($paths as $path) {
            if (false === $content = file_get_contents($path)) {
                continue;
            }
            if (null === $reports = JsonUtils::decode($content)) {
                continue;
            }

            $type = $this->guessType($path);

            foreach ($reports as $reportData) {
                if (null === $uuid = $this->findUuid($reportData)) {
                    continue;
                }
                if ($uuid->toRfc4122() !== $searchUuid->toRfc4122()) {
                    continue;
                }

                if (self::TYPE_INTEGRATION === $type) {
                    if (null !== $report = GaiaIntegrationReport::create($reportData)) {
                        yield GaiaHistory::create($path, $report);
                    }
                } elseif (self::TYPE_DELIVERY === $type) {
                    $report = GaiaDeliveryReport::create($reportData, $this->config);
                    yield GaiaHistory::create($path, $report);
                }
            }
        }
    }

    private function createHeader(
        string $type,
        string $subtype,
        string $suffix,
        ?string $scope = null,
    ): Header {
        return Header::attribute(
            code: "{$this->config->connector->code}_{$type}_{$subtype}_{$suffix}",
            scope: $scope,
        );
    }

    private function findUuid(array $reportData): ?Uuid
    {
        // if we have a SKU
        if (null !== $sku = ($reportData['internalReference'] ?? null)) {
            // look for it in the cache
            if (null !== $uuid = $this->doctrineCache->products->getUuid($sku)) {
                return $uuid;
            }

            // if not found…
            // @todo should we stop?
            return null;
        }

        // maybe find uuid base on the gtin?
        if (null !== $gtin = ($reportData['gtin'] ?? null)) {
            $tableProductData = TableEnum::PRODUCT_DATA;

            $query = <<<SQL
            SELECT product_uuid
            FROM {$tableProductData}
            WHERE attribute_code = ?
            AND locale_code IS NULL
            AND scope_code IS NULL
            AND value = ?
            LIMIT 0, 1
            SQL;

            if (false !== $row = $this->em->getConnection()->executeQuery($query, ['gtin_item', $gtin])->fetchAssociative()) {
                return Uuid::fromBinary($row['product_uuid']);
            }
        }

        return null;
    }

    /**
     * @return Generator<DataImport>
     */
    private function generateProductData(string $type, array $reports): Generator
    {
        $uuids = new Uuids();
        foreach ($reports as $reportData) {
            // find the uuid
            if (null === $uuid = $this->findUuid($reportData)) {
                $this->logger->warning('could not find uuid for gaia report', [
                    'report' => $reportData,
                ]);

                continue;
            }

            $uuids->add($uuid);

            if (self::TYPE_INTEGRATION === $type) {
                yield from $this->generateIntegrationProductData($uuid, $reportData);
            } elseif (self::TYPE_DELIVERY === $type) {
                yield from $this->generateDeliveryProductData($uuid, $reportData);
            }
        }

        return $uuids;
    }

    /**
     * @return Generator<DataImport>
     */
    private function generateIntegrationProductData(Uuid $uuid, array $reportData): Generator
    {
        if (null === $report = GaiaIntegrationReport::create($reportData)) {
            $this->logger->error('could not find type for gaia report', [
                'report' => $reportData,
            ]);

            return;
        }

        $dateHeader = $this->createHeader(self::TYPE_INTEGRATION, $report->subtype, self::SUFFIX_DATE);
        $statusHeader = $this->createHeader(self::TYPE_INTEGRATION, $report->subtype, self::SUFFIX_STATUS);
        $errorsHeader = $this->createHeader(self::TYPE_INTEGRATION, $report->subtype, self::SUFFIX_ERRORS);

        yield DataImport::fromHeader(uuid: $uuid, header: $dateHeader)
            ->withValue($report->date);

        yield DataImport::fromHeader(uuid: $uuid, header: $statusHeader)
            ->withValue($report->status);

        yield DataImport::fromHeader(uuid: $uuid, header: $errorsHeader)
            ->withValues($report->errors);
    }

    /**
     * @return Generator<DataImport>
     */
    private function generateDeliveryProductData(Uuid $uuid, array $reportData): Generator
    {
        $report = GaiaDeliveryReport::create($reportData, $this->config);

        foreach ($report->subtypes as $subtype) {
            $gaiaDateHeader = $this->createHeader(self::TYPE_DELIVERY_GAIA, $subtype, self::SUFFIX_DATE);
            $gaiaStatusHeader = $this->createHeader(self::TYPE_DELIVERY_GAIA, $subtype, self::SUFFIX_STATUS);
            $gaiaErrorsHeader = $this->createHeader(self::TYPE_DELIVERY_GAIA, $subtype, self::SUFFIX_ERRORS);

            // values
            yield DataImport::fromHeader(uuid: $uuid, header: $gaiaDateHeader)
                ->withValue($report->date);

            yield DataImport::fromHeader(uuid: $uuid, header: $gaiaStatusHeader)
                ->withValue($report->gaiaStatus);

            yield DataImport::fromHeader(uuid: $uuid, header: $gaiaErrorsHeader)
                ->withValues($report->gaiaErrors);

            if (null === $report->tradingPartner) {
                $this->logger->notice('no connector for gaia delivery, weird but lets just skip', [
                    'report' => $reportData,
                ]);

                continue;
            }

            if (null === $report->scope) {
                $this->logger->error('connector name is not mapped for gaia report value', [
                    'report' => $reportData,
                    'connectorName' => $report->tradingPartner,
                ]);

                continue;
            }

            if (null !== $report->tradingPartnerStatusMessage) {
                $tpTextSatusHeaders = $this->createHeader(self::TYPE_DELIVERY_TRADING_PARTNER, $subtype, self::SUFFIX_TP_STATUS, $report->scope);

                $optionCode = $tpTextSatusHeaders->code;
                $optionCode .= '_';
                $optionCode .= str_replace([' ', '-'], '_', strtolower($report->tradingPartnerStatusMessage));

                $optionId = $this->attributes->getOrCreateOptionId(
                    attribute: $tpTextSatusHeaders->code,
                    code: $optionCode,
                    names: new Translations(
                        $report->tradingPartnerStatusMessage,
                        $report->tradingPartnerStatusMessage,
                    ),
                    owner: $this->config->connector->code,
                );

                if (null !== $optionId) {
                    yield DataImport::fromHeader(uuid: $uuid, header: $tpTextSatusHeaders)
                        ->withOption($optionId);
                }
            }

            if (!$report->hasTradingPartnerData) {
                $this->logger->info('no information about trading partner feedback');

                continue;
            }

            $tpDateHeaders = $this->createHeader(self::TYPE_DELIVERY_TRADING_PARTNER, $subtype, self::SUFFIX_DATE, $report->scope);
            $tpStatusHeader = $this->createHeader(self::TYPE_DELIVERY_TRADING_PARTNER, $subtype, self::SUFFIX_STATUS, $report->scope);
            $tpErrorsHeader = $this->createHeader(self::TYPE_DELIVERY_TRADING_PARTNER, $subtype, self::SUFFIX_ERRORS, $report->scope);

            yield DataImport::fromHeader(uuid: $uuid, header: $tpDateHeaders)
                ->withValue($report->date);

            yield DataImport::fromHeader(uuid: $uuid, header: $tpStatusHeader)
                ->withValue($report->tradingPartnerStatus);

            yield DataImport::fromHeader(uuid: $uuid, header: $tpErrorsHeader)
                ->withValues($report->tradingPartnerErrors);
        }
    }
}
