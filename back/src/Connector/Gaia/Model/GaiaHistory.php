<?php declare(strict_types=1);

namespace App\Connector\Gaia\Model;

use App\Connector\Gaia\Gaia;
use App\Utils\DateUtils;

final class GaiaHistory
{
    public string $date {
        get => $this->integration->date ?? $this->delivery->date ?? DateUtils::now()->format('c');
    }

    private function __construct(
        public readonly string $path,
        public readonly string $type,
        public readonly ?GaiaIntegrationReport $integration = null,
        public readonly ?GaiaDeliveryReport $delivery = null,
    ) {
    }

    public static function create(string $path, GaiaIntegrationReport|GaiaDeliveryReport $report): self
    {
        if ($report instanceof GaiaIntegrationReport) {
            return new self(
                path: $path,
                type: Gaia::TYPE_INTEGRATION,
                integration: $report,
            );
        }

        return new self(
            path: $path,
            type: Gaia::TYPE_DELIVERY,
            delivery: $report,
        );
    }
}
