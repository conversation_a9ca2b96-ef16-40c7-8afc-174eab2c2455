<?php declare(strict_types=1);

namespace App\Connector\Gaia\Model;

use App\Connector\Gaia\Gaia;
use App\Connector\Gaia\GaiaConfig;
use App\Utils\DateUtils;
use App\Utils\StrUtils;
use function count;
use function is_array;
use function is_string;
use function mb_strtolower;

final class GaiaDeliveryReport
{
    public array $subtypes {
        get => '' !== $this->report['logisticGtin'] ? [Gaia::SUBTYPE_PRODUCT, Gaia::SUBTYPE_LOGISTIC] : [Gaia::SUBTYPE_PRODUCT];
    }

    public string $date {
        get => $this->report['date'] ?? DateUtils::now()->format('c');
    }

    public bool $gaiaStatus {
        get => 'success' === $this->gaiaStatusMessage;
    }

    public ?string $gaiaStatusMessage {
        get => StrUtils::nullOrString(mb_strtolower($this->report['equadisStatus'] ?? ''));
    }

    public array $gaiaErrors {
        get {
            $errors = [];
            foreach ($this->report['equadisErrors'] ?? [] as $error) {
                if (is_array($error)) {
                    $id = StrUtils::nullOrString($error['fieldId'] ?? null) ?? 'N/A';
                    $message = StrUtils::nullOrString($error['error'] ?? null) ?? 'N/A';
                    $error = "[{$id}] {$message}";
                }
                if (is_string($error)) {
                    $errors[] = $error;
                }
            }

            return $errors;
        }
    }

    public bool $hasTradingPartnerData {
        get => !(
            (
                null === $this->tradingPartnerStatusMessage
                || 'waiting' === $this->tradingPartnerStatusMessage
            )
            && 0 === count($this->tradingPartnerErrors)
        );
    }

    public ?string $tradingPartner {
        get => StrUtils::nullOrString($this->report['connectorName'] ?? null);
    }

    public bool $tradingPartnerStatus {
        get => null !== $this->tradingPartnerStatusMessage
            && !str_contains($this->tradingPartnerStatusMessage, 'error');
    }

    public ?string $tradingPartnerStatusMessage {
        get => StrUtils::nullOrString(mb_strtolower($this->report['connectorStatus'] ?? ''));
    }

    public array $tradingPartnerErrors {
        get {
            $errors = [];
            foreach ($this->report['connectorErrors'] ?? [] as $error) {
                if (is_array($error)) {
                    $error = StrUtils::nullOrString($error['error'] ?? null) ?? 'N/A';
                }
                if (is_string($error)) {
                    $errors[] = $error;
                }
            }

            return $errors;
        }
    }

    public ?string $scope {
        get => $this->config->tradingPartnersMapping[$this->tradingPartner] ?? null;
    }

    private function __construct(
        public readonly array $report,
        public GaiaConfig $config,
    ) {
    }

    public static function create(array $report, GaiaConfig $config): self
    {
        return new self(
            $report,
            $config,
        );
    }
}
