<?php declare(strict_types=1);

namespace App\Connector\Gaia\Command;

use App\Connector\Gaia\Gaia;
use App\Utils\JsonUtils;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use function basename;
use function fputcsv;
use function implode;
use const PHP_EOL;
use const STDOUT;

final class HistoryCommand extends Command
{
    public function __construct(
        private readonly Gaia $gaia,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('identifier', InputArgument::REQUIRED);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $identifier = $input->getArgument('identifier');

        fputcsv(STDOUT, [
            'file',
            'date',
            'type',
            'subtype',
            'status',
            'errors',
            'hook',
        ]);

        foreach ($this->gaia->generateHistories($identifier) as $history) {
            if (null !== $integration = $history->integration) {
                fputcsv(STDOUT, [
                    basename($history->path),
                    $history->date,
                    $history->type,
                    $integration->subtype,
                    $integration->statusMessage,
                    implode(PHP_EOL, $integration->errors),
                    JsonUtils::encode($integration->report),
                ]);
            }
            if (null !== $delivery = $history->delivery) {
                foreach ($delivery->subtypes as $subtype) {
                    fputcsv(STDOUT, [
                        basename($history->path),
                        $history->date,
                        Gaia::TYPE_DELIVERY_GAIA,
                        $subtype,
                        $delivery->gaiaStatusMessage,
                        implode(PHP_EOL, $delivery->gaiaErrors),
                        JsonUtils::encode($delivery->report),
                    ]);

                    if ($delivery->hasTradingPartnerData) {
                        fputcsv(STDOUT, [
                            basename($history->path),
                            $history->date,
                            Gaia::TYPE_DELIVERY_TRADING_PARTNER . "[{$delivery->scope}]",
                            $subtype,
                            $delivery->tradingPartnerStatusMessage,
                            implode(PHP_EOL, $delivery->tradingPartnerErrors),
                            JsonUtils::encode($delivery->report),
                        ]);
                    }
                }
            }
        }

        return self::SUCCESS;
    }
}
