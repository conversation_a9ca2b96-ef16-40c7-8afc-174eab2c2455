<?php declare(strict_types=1);

namespace App\Connector\Gaia\Command;

use App\Connector\Gaia\Gaia;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use function file_get_contents;

final class ImportCommand extends Command
{
    public function __construct(
        private readonly Gaia $gaia,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('path', InputArgument::REQUIRED)
            ->addOption('type', null, InputOption::VALUE_REQUIRED)
            ->addOption('now', null, InputOption::VALUE_NONE);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $path = $input->getArgument('path');
        $type = $input->getOption('type');

        if (is_dir($path)) {
            foreach ($this->gaia->listFiles() as $path) {
                $this->injectFile($path, $type, $input->getOption('now'));
            }
        } else {
            $this->injectFile($path, $type, $input->getOption('now'));
        }

        return self::SUCCESS;
    }

    private function injectFile(string $path, ?string $type, bool $now): void
    {
        $type ??= $this->gaia->guessType($path);
        if (null === $type) {
            return;
        }

        if (false !== $content = file_get_contents($path)) {
            $this->gaia->inject(
                type: $type,
                content: $content,
                saveReport: false,
                now: $now,
            );
        }
    }
}
