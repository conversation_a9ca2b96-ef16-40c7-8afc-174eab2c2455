<?php declare(strict_types=1);

namespace App\Utils;

use DateTime;
use DateTimeImmutable;
use DateTimeZone;
use Throwable;

abstract class DateUtils
{
    public const string FORMAT_START_DAY = 'Y-m-d 00:00:00';
    public const string FORMAT_END_DAY = 'Y-m-d 23:59:59';

    private static ?DateTimeZone $timezone = null;

    public static function timezone(): DateTimeZone
    {
        return self::$timezone ??= new DateTimeZone('Europe/Paris');
    }

    public static function now(): DateTime
    {
        return new DateTime(timezone: self::timezone());
    }

    public static function new(?string $datetime, ?string $format = null): ?DateTime
    {
        if (null === $datetime) {
            return null;
        }

        try {
            if (null === $format) {
                $date = new DateTime($datetime, timezone: self::timezone());
            } else {
                $date = DateTime::createFromFormat($format, $datetime, self::timezone());
                if (false === $date) {
                    return null;
                }
            }

            $date->setTimezone(self::timezone());

            return $date;
        } catch (Throwable) {
        }

        return null;
    }

    public static function fromDatabase(?string $datetime): ?DateTime
    {
        if (null === $datetime) {
            return null;
        }

        $date = DateTime::createFromFormat('Y-m-d H:i:s', $datetime, self::timezone());
        if (false === $date) {
            return null;
        }

        $date->setTimezone(self::timezone());

        return $date;
    }

    public static function immutableNow(): DateTimeImmutable
    {
        return new DateTimeImmutable(timezone: self::timezone());
    }

    public static function toImmutable(?string $date): ?DateTimeImmutable
    {
        try {
            if (null !== $date) {
                return new DateTimeImmutable($date, timezone: self::timezone());
            }
        } catch (Throwable) {
        }

        return null;
    }
}
