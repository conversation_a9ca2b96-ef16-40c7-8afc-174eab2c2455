<?php declare(strict_types=1);

namespace App;

use App\Bridge\Doctrine\Type\DateTimeType;
use App\Bridge\Doctrine\Type\DateType;
use Doctrine\DBAL\Types\Type;
use Doctrine\DBAL\Types\Types;
use Symfony\Bundle\FrameworkBundle\Kernel\MicroKernelTrait;
use Symfony\Component\HttpKernel\Kernel as BaseKernel;

class Kernel extends BaseKernel
{
    use MicroKernelTrait;

    public function __construct(string $environment, bool $debug)
    {
        Type::overrideType(Types::DATETIME_MUTABLE, DateTimeType::class);
        Type::overrideType(Types::DATE_MUTABLE, DateType::class);

        parent::__construct($environment, $debug);
    }
}
