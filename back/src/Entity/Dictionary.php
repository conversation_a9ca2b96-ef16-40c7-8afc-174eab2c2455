<?php declare(strict_types=1);

namespace App\Entity;

use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata as APM;
use App\Api\Filter\QFilter;
use App\Bridge\Counter\Entity\DictionaryCounters;
use App\Bridge\Translation\Api\Filter\TranslationsFilter;
use App\Bridge\Translation\Entity\Translations;
use App\Contracts\Enum\TableEnum;
use App\Contracts\Traits\CodeAsIdTrait;
use App\Contracts\Traits\TimestampableTrait;
use App\Validator\Constraints\Translation;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Table(name: TableEnum::DICTIONARY)]
#[ORM\Entity]
#[APM\GetCollection]
#[APM\Post]
#[APM\Get]
#[APM\Put]
#[APM\Delete]
#[APM\ApiFilter(TranslationsFilter::class, properties: ['names'])]
#[APM\ApiFilter(QFilter::class, properties: ['code', 'names'])]
#[APM\ApiFilter(SearchFilter::class, properties: ['code'])]
class Dictionary
{
    use CodeAsIdTrait;
    use TimestampableTrait;

    #[ORM\Embedded]
    #[Assert\Valid]
    #[Translation(defaultLangRequired: true, max: Translation::COMMON_MAX)]
    public Translations $names;

    #[ORM\Embedded]
    #[APM\ApiProperty(writable: false)]
    public DictionaryCounters $counters;

    public function __construct()
    {
        $this->names = new Translations();
        $this->counters = new DictionaryCounters();
    }
}
