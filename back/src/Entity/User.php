<?php declare(strict_types=1);

namespace App\Entity;

use ApiPlatform\Doctrine\Orm\Filter\BooleanFilter;
use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation as OpenApiOperation;
use App\Api\Filter\QFilter;
use App\Api\Filter\UserMembershipRoleFilter;
use App\Api\Model\RightsOutputModel;
use App\Api\Model\UserPreferences;
use App\Api\OpenApi\OpenApiFactory;
use App\Bridge\Security\Api\Filter\UserFilter;
use App\Bridge\Security\Api\Provider\UserProvider;
use App\Bridge\Security\Api\Provider\UserRightProvider;
use App\Bridge\Security\Enum\RoleEnum;
use App\Bridge\Security\Utils\RoleUtils;
use App\Bridge\Security\Voter\UserVoter;
use App\Contracts\Enum\MembershipRoleEnum;
use App\Contracts\Enum\TableEnum;
use App\Contracts\Traits\IdTrait;
use App\Contracts\Traits\StatusTrait;
use App\Contracts\Traits\TimestampableTrait;
use App\Contracts\Traits\UploadTrait;
use App\Repository\UserRepository;
use DateTime;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Dunglas\DoctrineJsonOdm\Type\JsonDocumentType;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Serializer\Attribute\Ignore;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\Table(name: TableEnum::USER)]
#[UniqueEntity(fields: 'email', message: 'user.email.already_used', errorPath: 'email')]
#[APM\ApiResource(openapi: new OpenApiOperation(tags: [OpenApiFactory::TAG_USER]))]
#[APM\GetCollection(security: 'is_granted("' . UserVoter::GET_COLLECTION . '")')]
#[APM\Get(security: 'is_granted("' . UserVoter::GET . '", object)', provider: UserProvider::class)]
#[APM\Get(
    uriTemplate: '/users/0/rights',
    openapi: new OpenApiOperation(
        tags: [OpenApiFactory::TAG_USER],
        summary: 'Get current user right',
        description: 'Get current user right. Impossible to retrieve other user rights despite your role.',
    ),
    output: RightsOutputModel::class,
    provider: UserRightProvider::class,
)]
#[APM\Post(securityPostDenormalize: 'is_granted("' . UserVoter::POST . '", object)')]
#[APM\Put(security: 'is_granted("' . UserVoter::PUT . '", object)')]
#[APM\Delete(security: 'is_granted("' . UserVoter::DELETE . '", object)')]
#[APM\ApiFilter(SearchFilter::class, properties: ['email' => 'partial', 'firstname' => 'partial', 'lastname' => 'partial', 'role' => 'exact'])]
#[APM\ApiFilter(QFilter::class, properties: ['email', 'firstname', 'lastname'])]
#[APM\ApiFilter(OrderFilter::class, properties: ['lastname' => 'ASC', 'firstname' => 'ASC', 'email', 'lastLogin'])]
#[APM\ApiFilter(UserFilter::class)]
#[APM\ApiFilter(UserMembershipRoleFilter::class)]
#[APM\ApiFilter(BooleanFilter::class, properties: ['status'])]
class User implements UserInterface, PasswordAuthenticatedUserInterface
{
    use IdTrait;
    use TimestampableTrait;
    use UploadTrait;
    use StatusTrait;

    #[ORM\Column(nullable: true)]
    #[Assert\Length(max: 255)]
    #[Ignore]
    public ?string $oauthId = null;

    #[ORM\Column(length: 20, nullable: true)]
    #[Assert\Length(max: 20)]
    #[Ignore]
    public ?string $oauthClient = null;

    #[ORM\Column(length: 100, unique: true)]
    #[Assert\NotBlank]
    #[Assert\Length(max: 100)]
    #[Assert\Email]
    public ?string $email = null;

    #[ORM\Column(nullable: true)]
    #[Ignore]
    public ?string $password = null;

    #[ORM\Column(length: 50)]
    #[Assert\NotBlank]
    #[Assert\Length(max: 50)]
    public ?string $firstname = null;

    #[ORM\Column(length: 50)]
    #[Assert\NotBlank]
    #[Assert\Length(max: 50)]
    public ?string $lastname = null;

    #[ORM\Column(nullable: true)]
    #[APM\ApiProperty(writable: false)]
    public ?DateTime $lastLogin = null;

    #[ORM\Column(options: ['default' => RoleEnum::USER])]
    #[Assert\NotNull]
    #[Assert\Choice(RoleEnum::ALL_ROLES)]
    #[APM\ApiProperty(writable: false)]
    public string $role = RoleEnum::USER;

    #[ORM\Column(type: JsonDocumentType::NAME)]
    #[Assert\Valid]
    public UserPreferences $preferences;

    /**
     * Computed version of catalogs "authorization" read + write access by catalog.
     * Note: `null` means the user is an admin.
     * Note: an empty array means the user does not have any group.
     * @var array{read: string[], write: string[]}[]
     * @phpstan-var array<int, array{read: string[], write: string[]}>
     */
    #[ORM\Column(name: 'computed_catalogs', type: Types::JSON, nullable: true)]
    #[Ignore]
    public ?array $catalogs = [];

    /**
     * Used to display on front.
     * @var array{id:int,name:string,role:string}[]
     * @phpstan-var array<int, array{id:int,name:string,role:string}>
     */
    #[ORM\Column(name: 'computed_groups', type: Types::JSON)]
    #[APM\ApiProperty(writable: false)]
    public array $groups = [];

    public function __construct()
    {
        $this->preferences = new UserPreferences();
    }

    #[SerializedName('isSso')]
    public function isSso(): bool
    {
        return null !== $this->oauthId || null !== $this->oauthClient;
    }

    #[SerializedName('isTech')]
    public function isTech(): bool
    {
        return RoleUtils::isTech($this);
    }

    #[SerializedName('isSuperAdmin')]
    public function isSuperAdmin(): bool
    {
        return RoleUtils::isSuperAdmin($this);
    }

    // @deprecated
    #[SerializedName('isAdmin')]
    public function isAdmin(): bool
    {
        return array_any(
            $this->groups,
            fn($group): bool => MembershipRoleEnum::ADMIN === $group['role'],
        );
    }

    #[Ignore]
    public function isAdminOf(?int $id): bool
    {
        if (null !== $id) {
            foreach ($this->groups ?? [] as $group) {
                if ($id === $group['id']) {
                    if (MembershipRoleEnum::ADMIN === $group['role']) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * @phpstan-return non-empty-string
     */
    #[Ignore]
    public function getUserIdentifier(): string
    {
        return (string)$this->email;
    }

    #[Ignore]
    public function getPassword(): string
    {
        return (string)$this->password;
    }

    #[Ignore]
    public function getRoles(): array
    {
        return [
            $this->role,
        ];
    }

    #[Ignore]
    public function getSalt(): ?string
    {
        return null;
    }

    public function eraseCredentials(): void
    {
    }
}
