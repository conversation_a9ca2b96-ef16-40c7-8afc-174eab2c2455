<?php declare(strict_types=1);

namespace App\Entity;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata as APM;
use App\Api\Filter\QFilter;
use App\Contracts\Enum\TableEnum;
use App\Contracts\Traits\TimestampableTrait;
use App\Contracts\Traits\UuidAsIdTrait;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Table(name: TableEnum::DICTIONARY_MAPPING)]
#[ORM\Entity]
#[UniqueEntity(fields: ['dictionary', 'from'], errorPath: 'from')]
#[APM\GetCollection]
#[APM\Post]
#[APM\Get]
#[APM\Put]
#[APM\Delete]
#[APM\ApiFilter(SearchFilter::class, properties: ['dictionary' => 'exact'])]
#[APM\ApiFilter(QFilter::class, properties: ['from', 'to'])]
#[APM\ApiFilter(OrderFilter::class, properties: ['from', 'to'])]
class DictionaryMapping
{
    use UuidAsIdTrait;
    use TimestampableTrait;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'dictionary_code', referencedColumnName: 'code', nullable: false, onDelete: 'CASCADE')]
    #[ApiProperty(readableLink: true, writableLink: false)]
    #[Assert\NotNull]
    public ?Dictionary $dictionary = null;

    #[ORM\Column(name: 'mapping_from')]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public string $from;

    #[ORM\Column(name: 'mapping_to')]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public string $to;

    public function __construct()
    {
        $this->defineUuid();
    }
}
