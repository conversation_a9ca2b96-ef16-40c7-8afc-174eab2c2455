<?php declare(strict_types=1);

namespace App\Module\Hub\Entity;

use ApiPlatform\Doctrine\Orm\Filter\DateFilter;
use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation as OpenApiOperation;
use App\Api\Filter\QFilter;
use App\Api\OpenApi\OpenApiFactory;
use App\Contracts\Enum\BinaryFormatEnum;
use App\Contracts\Enum\TableEnum;
use App\Contracts\Traits\IdTrait;
use App\Contracts\Traits\OwnerTrait;
use App\Contracts\Traits\TimestampableTrait;
use App\Module\Hub\Api\Action\HubOrderGetBinaryAction;
use App\Module\Hub\Api\Action\HubOrderGetShippingBinaryAction;
use App\Module\Hub\Api\Model\AvailableTransitionsModel;
use App\Module\Hub\Api\Model\HubOrderCreate;
use App\Module\Hub\Api\Model\TaxClass;
use App\Module\Hub\Api\Model\TransitionInputModel;
use App\Module\Hub\Api\Processor\HubOrderCreateProcessor;
use App\Module\Hub\Api\Processor\HubOrderTransitionProcessor;
use App\Module\Hub\Api\Provider\HubOrderAvailableTransitionProvider;
use App\Module\Hub\Enum\HubOrderStateEnum;
use App\Module\Hub\Model\Address;
use DateTime;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Dunglas\DoctrineJsonOdm\Type\JsonDocumentType;
use Symfony\Component\Serializer\Attribute\Ignore;
use function round;

#[ORM\Entity]
#[ORM\Table(name: TableEnum::HUB_ORDER)]
#[ORM\UniqueConstraint(fields: ['owner', 'ownerIdentifier'])]
#[APM\ApiResource(openapi: new OpenApiOperation(tags: [OpenApiFactory::TAG_HUB]))]
#[APM\GetCollection(order: ['createdAt' => 'DESC'])]
#[APM\GetCollection(uriTemplate: '/hub-orders/{id}/available-transitions', output: AvailableTransitionsModel::class, provider: HubOrderAvailableTransitionProvider::class)]
#[APM\Post(uriTemplate: '/hub-orders/{id}/process-transitions', input: TransitionInputModel::class, processor: HubOrderTransitionProcessor::class)]
#[APM\Post(input: HubOrderCreate::class, processor: HubOrderCreateProcessor::class)]
#[APM\Get]
#[APM\Get(
    uriTemplate: '/hub-orders/{id}/binary',
    formats: ['binary' => BinaryFormatEnum::ALL],
    controller: HubOrderGetBinaryAction::class,
    openapi: new OpenApiOperation(tags: [OpenApiFactory::TAG_HUB], security: []),
    read: false,
)]
#[APM\Get(
    uriTemplate: '/hub-orders/{id}/shipping/binary',
    formats: ['binary' => BinaryFormatEnum::ALL],
    controller: HubOrderGetShippingBinaryAction::class,
    openapi: new OpenApiOperation(tags: [OpenApiFactory::TAG_HUB], security: []),
    read: false,
)]
#[APM\ApiFilter(OrderFilter::class, properties: ['subTotal', 'shippingAmount', 'totalAmount', 'owner', 'ownerIdentifier', 'price', 'createdAt', 'invoiceId'])]
#[APM\ApiFilter(SearchFilter::class, properties: ['state', 'owner', 'currencyCode', 'billing.lastname', 'shipping.lastname'])]
#[APM\ApiFilter(DateFilter::class, properties: ['importedAt', 'createdAt', 'updatedAt'])]
#[APM\ApiFilter(QFilter::class, properties: ['q', 'invoiceId'])]
class HubOrder
{
    use TimestampableTrait;
    use IdTrait;
    use OwnerTrait;

    #[ORM\Column(insertable: false, updatable: false, generated: 'INSERT')]
    public ?int $invoiceId = null;

    #[Ignore]
    #[ORM\Column(length: 1000, insertable: false, updatable: false, generated: 'INSERT')]
    public ?string $q = null;

    #[ORM\Column]
    public ?string $state = null;

    #[ORM\Column(type: JsonDocumentType::NAME)]
    public Address $billing;

    #[ORM\Column(type: JsonDocumentType::NAME)]
    public Address $shipping;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, insertable: false, updatable: false, options: ['default' => 'CURRENT_TIMESTAMP'], generated: 'INSERT')]
    public ?DateTime $importedAt = null;

    #[ORM\Column]
    public ?float $price = null; // total price tax included (with taxPart and shippingPart)

    #[ORM\Column(nullable: true)]
    public ?float $taxPart = null; // total of tax

    #[ORM\Column(nullable: true)]
    public ?float $shippingPart = null; // total of shipping cost

    #[ORM\Column]
    public ?string $currencyCode = null;

    #[ORM\Column(nullable: true)]
    public ?string $storeId = null;

    #[ORM\Column(nullable: true)]
    public ?string $origin = null;

    #[ORM\Column(nullable: true)]
    public ?string $originIdentifier = null;

    #[ORM\Column(length: 32, nullable: true)]
    public ?string $emailSignature = null;

    #[ORM\Column(length: 1500, nullable: true)]
    public ?string $commentary = null;

    /** @var HubShipment|null used only for order_state_machine */
    #[Ignore]
    public ?HubShipment $shipment = null;

    public function __construct()
    {
        $this->billing = new Address();
        $this->shipping = new Address();
    }

    public function getSubTotal(): float
    {
        return round($this->price - ($this->shippingPart ?? .0), 2);
    }

    public function getSubTotalWithoutTax(): float
    {
        $taxAmout = array_sum(array_map(fn(TaxClass $taxClass) => $taxClass->amount, $this->getTaxClasses()));

        if (HubOrderStateEnum::AVOIR === $this->state) {
            $taxAmout = -$taxAmout;
        }

        return round($this->price - ($this->shippingPart ?? .0) - $taxAmout, 2);
    }

    /**
     * @return TaxClass[]
     * thats a fucked up method to please GD, don't trust it
     */
    public function getTaxClasses(): array
    {
        if (null !== $this->taxPart && 0.0 !== $this->taxPart) {
            return [new TaxClass(TaxClass::DEFAULT_TAX_RATE, $this->taxPart)];
        }

        return [TaxClass::calculate(TaxClass::DEFAULT_TAX_RATE, $this->price)];
    }
}
