<?php declare(strict_types=1);

namespace App\Module\Monitoring\Monitor;

use App\Bridge\Elastic\Index\DocumentIndex;
use App\Bridge\Filter\Enum\OperatorEnum;
use App\Bridge\Filter\Model\Filter;
use App\Bridge\Filter\Model\Filters;
use App\Contracts\Enum\ApiTypeEnum;
use App\Contracts\Enum\TableEnum;
use App\Contracts\Model\Header;
use App\Module\Monitoring\Model\DocumentCount;
use Doctrine\ORM\EntityManagerInterface;
use Throwable;

final readonly class DocumentMonitor
{
    public function __construct(
        private EntityManagerInterface $em,
        private DocumentIndex $documentIndex,
    ) {
    }

    /**
     * @phpstan-return array{"Product":DocumentCount,"Media":DocumentCount,"Content":DocumentCount,}
     */
    public function getStats(): array
    {
        return [
            ApiTypeEnum::PRODUCT => new DocumentCount(
                count: $this->countDatabase(TableEnum::PRODUCT),
                countFlat: $this->countDatabase(TableEnum::FLAT_PRODUCT),
                countIndex: $this->countElastic(ApiTypeEnum::PRODUCT),
            ),
            ApiTypeEnum::MEDIA => new DocumentCount(
                count: $this->countDatabase(TableEnum::MEDIA),
                countFlat: $this->countDatabase(TableEnum::FLAT_MEDIA),
                countIndex: $this->countElastic(ApiTypeEnum::MEDIA),
            ),
            ApiTypeEnum::CONTENT => new DocumentCount(
                count: $this->countDatabase(TableEnum::CONTENT),
                countFlat: $this->countDatabase(TableEnum::FLAT_CONTENT),
                countIndex: $this->countElastic(ApiTypeEnum::CONTENT),
            ),
        ];
    }

    private function countDatabase(string $table): int
    {
        try {
            return (int)$this->em->getConnection()->executeQuery("SELECT COUNT(*) FROM {$table}")->fetchOne();
        } catch (Throwable) {
        }

        return 0;
    }

    private function countElastic(string $type): int
    {
        try {
            $builder = $this->documentIndex->createBuilder();
            $builder->handleFilters(new Filters([new Filter(Header::property('type'), OperatorEnum::EQ, $type)]));

            return $builder->countHits();
        } catch (Throwable) {
        }

        return 0;
    }
}
