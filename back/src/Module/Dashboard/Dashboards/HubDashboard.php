<?php declare(strict_types=1);

namespace App\Module\Dashboard\Dashboards;

use App\Contracts\Enum\LoggerChannelEnum;
use App\Contracts\Enum\TableEnum;
use App\Module\Dashboard\DashboardInterface;
use App\Module\Dashboard\Enum\DashboardTypeEnum;
use App\Module\Dashboard\Model\HubStats;
use App\Module\Dashboard\Model\HubStatsDay;
use App\Utils\DateUtils;
use DateInterval;
use Doctrine\ORM\EntityManagerInterface;
use Monolog\Attribute\WithMonologChannel;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Throwable;
use function ksort;

#[WithMonologChannel(LoggerChannelEnum::DASHBOARD)]
final readonly class HubDashboard implements DashboardInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private RequestStack $requestStack,
        private LoggerInterface $logger,
    ) {
    }

    public static function getType(): string
    {
        return DashboardTypeEnum::HUB;
    }

    public function getStats(): HubStats
    {
        $request = $this->requestStack->getCurrentRequest();

        $from = DateUtils::new($request->query->get('from'));
        $from?->setTime(0, 0);

        $to = DateUtils::new($request->query->get('to'));
        $to?->setTime(23, 59, 59, 999);

        if (null === $from || null === $to || $from > $to) {
            throw new BadRequestHttpException();
        }

        $stats = new HubStats($from, $to);

        // complete with real data
        $this->handleOrderStats($stats);
        $this->handleItemStats($stats);

        // complete empty days
        $attempts = 0;
        $oneDay = DateInterval::createFromDateString('1 day');
        do {
            $attempts++;
            $stats->days[$from->format('Y-m-d')] ??= HubStatsDay::from($from);
            $from->add($oneDay);
        } while ($from <= $to && $attempts < 120);

        // sort and clean
        ksort($stats->days);

        $stats->days = array_values($stats->days);

        return $stats;
    }

    private static function nonZeroM(float $target, float $value, callable $fn): float
    {
        return .0 === $target ? $value : $fn($target, $value);
    }

    private function handleOrderStats(HubStats $stats): void
    {
        try {
            $tableHubOrder = TableEnum::HUB_ORDER;

            $query = <<<SQL
            SELECT
                DATE(created_at) AS date,
                COUNT(*) AS orders_count,
                SUM(price) AS orders_price,
                MIN(price) AS orders_price_min,
                MAX(price) AS orders_price_max
            FROM {$tableHubOrder}
            WHERE created_at BETWEEN ? AND ?
            GROUP BY DATE(created_at)
            SQL;

            $results = $this->em->getConnection()->executeQuery($query, [
                $stats->from->format(DateUtils::FORMAT_START_DAY),
                $stats->to->format(DateUtils::FORMAT_END_DAY),
            ]);
            while (false !== $row = $results->fetchAssociative()) {
                $date = DateUtils::new($row['date']);

                $stats->ordersCount += $row['orders_count'];
                $stats->ordersPrice += $row['orders_price'];
                $stats->ordersPriceMin = self::nonZeroM($stats->ordersPriceMin, $row['orders_price_min'], min(...));
                $stats->ordersPriceMax = self::nonZeroM($stats->ordersPriceMax, $row['orders_price_max'], max(...));

                $day = $stats->days[$date->format('Y-m-d')] = HubStatsDay::from($date);
                $day->ordersCount = $row['orders_count'];
                $day->ordersPrice = $row['orders_price'];
                $day->ordersPriceMin = self::nonZeroM($day->ordersPriceMin, $row['orders_price_min'], min(...));
                $day->ordersPriceMax = self::nonZeroM($day->ordersPriceMax, $row['orders_price_max'], max(...));
            }
        } catch (Throwable $e) {
            $this->logger->error('cannot handle order stats', [$e]);
        }
    }

    private function handleItemStats(HubStats $stats): void
    {
        try {
            $tableHubOrder = TableEnum::HUB_ORDER;
            $tableHubOrderItem = TableEnum::HUB_ORDER_ITEM;

            $query = <<<SQL
            SELECT
                DATE(o.created_at) AS date,
                SUM(i.ordered_quantity) AS items_count,
                SUM(i.price) AS items_price,
                MIN(i.price) AS items_price_min,
                MAX(i.price) AS items_price_max
            FROM {$tableHubOrderItem} i
            INNER JOIN {$tableHubOrder} o ON i.order_id = o.id
            WHERE o.created_at BETWEEN ? AND ?
            GROUP BY DATE(o.created_at)
            SQL;

            $results = $this->em->getConnection()->executeQuery($query, [
                $stats->from->format('Y-m-d H:i:s'),
                $stats->to->format('Y-m-d H:i:s'),
            ]);
            while (false !== $row = $results->fetchAssociative()) {
                $date = DateUtils::new($row['date']);

                $day = $stats->days[$date->format('Y-m-d')] = HubStatsDay::from($date);
                $day->itemsCount += $row['items_count'];
                $day->itemsPrice += $row['items_price'];
                $day->itemsPriceMin = self::nonZeroM($day->itemsPriceMin, $row['items_price_min'], min(...));
                $day->itemsPriceMax = self::nonZeroM($day->itemsPriceMax, $row['items_price_max'], max(...));
            }
        } catch (Throwable $e) {
            $this->logger->error('cannot handle item stats', [$e]);
        }
    }
}
