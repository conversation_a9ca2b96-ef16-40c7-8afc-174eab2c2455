<?php declare(strict_types=1);

namespace App\Module\Dashboard\Api\Resource;

use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation as OpenApiOperation;
use App\Api\OpenApi\OpenApiFactory;
use App\Module\Dashboard\Api\Provider\DashboardPlatformStatisticCollectionProvider;
use App\Module\Dashboard\Api\Provider\DashboardPlatformStatisticConfigCollectionProvider;
use App\Module\Dashboard\Model\PlatformStatistic;

#[APM\ApiResource(openapi: new OpenApiOperation(tags: [OpenApiFactory::TAG_SETTINGS]))]
#[APM\Get(uriTemplate: '/dashboard-stats/{code}', provider: DashboardPlatformStatisticCollectionProvider::class)]
#[APM\Get(uriTemplate: '/dashboard-stats', provider: DashboardPlatformStatisticConfigCollectionProvider::class)]
final class DashboardPlatformStatistic
{
    /**
     * @param PlatformStatistic[] $statistics
     */
    public function __construct(
        #[APM\ApiProperty(identifier: true)]
        public string $code,
        public ?int $percentage,
        public ?int $amount = 0,
        public ?array $statistics = [],
    ) {
    }
}
