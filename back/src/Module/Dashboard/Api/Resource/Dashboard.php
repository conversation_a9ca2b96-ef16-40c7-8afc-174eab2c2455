<?php declare(strict_types=1);

namespace App\Module\Dashboard\Api\Resource;

use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation as OpenApiOperation;
use App\Api\OpenApi\OpenApiFactory;
use App\Module\Dashboard\Api\Provider\DashboardProvider;
use App\Module\Dashboard\Dashboards\StatsInterface;

#[APM\ApiResource(openapi: new OpenApiOperation(tags: [OpenApiFactory::TAG_SETTINGS]))]
#[APM\Get(uriTemplate: '/dashboards', provider: DashboardProvider::class)]
#[APM\Get(uriTemplate: '/dashboards/{type}', provider: DashboardProvider::class)]
final class Dashboard
{
    public function __construct(
        #[APM\ApiProperty(identifier: true)]
        public ?string $type = null,
        public ?StatsInterface $stats = null,
    ) {
    }
}
