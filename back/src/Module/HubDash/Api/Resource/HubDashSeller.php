<?php declare(strict_types=1);

namespace App\Module\HubDash\Api\Resource;

use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation as OpenApiOperation;
use App\Api\OpenApi\OpenApiFactory;
use App\Module\HubDash\Api\Filter\HubDashSellerFilter;
use App\Module\HubDash\Api\Provider\HubDashSellerCollectionProvider;

#[APM\ApiResource(openapi: new OpenApiOperation(tags: [OpenApiFactory::TAG_HUB_DASH]))]
#[APM\GetCollection(paginationEnabled: false, provider: HubDashSellerCollectionProvider::class)]
#[APM\ApiFilter(HubDashSellerFilter::class)]
final readonly class HubDashSeller
{
    public function __construct(
        #[APM\ApiProperty(identifier: true)]
        public string $code,
        public float $price,
        public int $count,
    ) {
    }
}
