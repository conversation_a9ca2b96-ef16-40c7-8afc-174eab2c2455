<?php declare(strict_types=1);

namespace App\Module\HubDash\Api\Resource;

use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation as OpenApiOperation;
use App\Api\OpenApi\OpenApiFactory;
use App\Bridge\Flat\Entity\FlatProduct;
use App\Module\HubDash\Api\Filter\HubDashTopProductFilter;
use App\Module\HubDash\Api\Provider\HubDashTopProductCollectionProvider;

#[APM\ApiResource(openapi: new OpenApiOperation(tags: [OpenApiFactory::TAG_HUB_DASH]))]
#[APM\GetCollection(paginationEnabled: false, provider: HubDashTopProductCollectionProvider::class)]
#[APM\ApiFilter(HubDashTopProductFilter::class)]
final readonly class HubDashTopProduct
{
    public function __construct(
        #[APM\ApiProperty(identifier: true)]
        public string $sku,
        public string $name,
        public float $price,
        public int $quantity,
        public ?FlatProduct $product,
    ) {
    }
}
