<?php declare(strict_types=1);

namespace App\Module\HubDash\Api\Resource;

use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation as OpenApiOperation;
use App\Api\OpenApi\OpenApiFactory;
use App\Module\HubDash\Api\Filter\HubDashGroupByFilter;
use App\Module\HubDash\Api\Provider\HubDashOrderCollectionProvider;

#[APM\ApiResource(openapi: new OpenApiOperation(tags: [OpenApiFactory::TAG_HUB_DASH]))]
#[APM\GetCollection(provider: HubDashOrderCollectionProvider::class)]
#[APM\ApiFilter(HubDashGroupByFilter::class)]
final readonly class HubDashOrder
{
    public function __construct(
        public string $date,
        public int $count,
        public float $price,
        public float $priceMax,
        public float $priceMin,
    ) {
    }
}
