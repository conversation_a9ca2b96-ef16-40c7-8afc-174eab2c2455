<?php declare(strict_types=1);

namespace App\Module\HubDash\Api\Resource;

use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation as OpenApiOperation;
use App\Api\OpenApi\OpenApiFactory;
use App\Module\HubDash\Api\Filter\HubDashFilter;
use App\Module\HubDash\Api\Provider\HubDashStatCollectionProvider;

#[APM\ApiResource(openapi: new OpenApiOperation(tags: [OpenApiFactory::TAG_HUB_DASH]))]
#[APM\GetCollection(paginationEnabled: false, provider: HubDashStatCollectionProvider::class)]
#[APM\ApiFilter(HubDashFilter::class)]
final readonly class HubDashStat
{
    public function __construct(
        #[APM\ApiProperty(identifier: true)]
        public string $key,
        public mixed $value,
        #[APM\ApiProperty(default: '{}')]
        public array $metadata = [],
        public bool $isPeriodical = true,
    ) {
    }
}
