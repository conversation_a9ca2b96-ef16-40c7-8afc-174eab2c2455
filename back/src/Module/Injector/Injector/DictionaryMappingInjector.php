<?php declare(strict_types=1);

namespace App\Module\Injector\Injector;

use App\Contracts\Enum\ApiTypeEnum;
use App\Contracts\Enum\TableEnum;
use App\Contracts\Model\Builder\SqlBuilder;
use App\Module\Injector\InjectionConfig;
use App\Module\Injector\Model\DictionaryMappingImport;
use App\Utils\UuidUtils;
use Generator;

/**
 * @template-extends AbstractInjector<DictionaryMappingImport>
 */
final readonly class DictionaryMappingInjector extends AbstractInjector
{
    public static function getType(): string
    {
        return ApiTypeEnum::DICTIONARY_MAPPING;
    }

    public function inject(Generator $imports, InjectionConfig $config = new InjectionConfig()): Generator
    {
        $builder = $this->sqlBuilderFactory->createSqlBuilder(
            table: TableEnum::DICTIONARY_MAPPING,
            columns: ['uuid', 'dictionary_code', 'mapping_from', 'mapping_to'],
            updates: ['mapping_to'],
        );

        $deleteBuilder = $this->sqlBuilderFactory->createSqlDeleteBuilder(
            table: TableEnum::DICTIONARY_MAPPING,
        );

        yield from $this->injectBuilder(
            $imports,
            $builder,
            function (DictionaryMappingImport $import) use ($builder, $deleteBuilder): SqlBuilder {
                if ($import->wantsToDie) {
                    $deleteBuilder->add(code: $import->dictionaryCode);

                    return $builder;
                }

                return $builder->add(
                    uuid: UuidUtils::new()->toBinary(),
                    dictionary_code: $import->dictionaryCode,
                    mapping_from: $import->from,
                    mapping_to: $import->to,
                );
            },
            $config->postInject,
        );

        $deleteBuilder->execute();
    }

    public function postInject(bool $now = false): void
    {
        $this->doctrineCache->warmup(ApiTypeEnum::DICTIONARY);
    }
}
