<?php declare(strict_types=1);

namespace App\Module\Injector\Model;

use App\Validator\Constraints\Code;
use Symfony\Component\Validator\Constraints as Assert;

final class DictionaryMappingImport extends AbstractImport
{
    public function __construct(
        #[Assert\NotBlank]
        #[Assert\Length(max: 255)]
        #[Code]
        public ?string $dictionaryCode,
        #[Assert\NotBlank]
        #[Assert\Length(max: 255)]
        public ?string $from,
        #[Assert\NotBlank]
        #[Assert\Length(max: 255)]
        public ?string $to,
    ) {
    }

    public function getId(): string
    {
        return $this->dictionaryCode;
    }
}
