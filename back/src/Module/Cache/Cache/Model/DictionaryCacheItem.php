<?php declare(strict_types=1);

namespace App\Module\Cache\Cache\Model;

final readonly class DictionaryCacheItem
{
    private function __construct(
        public string $code,
        public array $mapping,
    ) {
    }

    public static function from(string $code, array $rows): self
    {
        $mapping = [];
        foreach ($rows as $row) {
            $mapping[] = DictionaryMappingCacheItem::from($row);
        }

        return new self(
            $code,
            $mapping,
        );
    }

    public function getDefinition(?string $from): ?string
    {
        $definition = array_find($this->mapping, fn ($item) => $item->from === $from);

        return $definition?->to;
    }
}
