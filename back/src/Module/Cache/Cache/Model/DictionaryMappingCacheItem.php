<?php declare(strict_types=1);

namespace App\Module\Cache\Cache\Model;

/** Used by DictionaryCache like model for property "mapping" */
final readonly class DictionaryMappingCacheItem
{
    private function __construct(
        public string $from,
        public string $to,
    ) {
    }

    public static function from(array $row): self
    {
        return new self(
            from: $row['mapping_from'],
            to: $row['mapping_to'],
        );
    }
}
