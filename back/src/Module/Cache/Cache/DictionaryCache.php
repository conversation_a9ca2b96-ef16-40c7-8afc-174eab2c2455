<?php declare(strict_types=1);

namespace App\Module\Cache\Cache;

use App\Contracts\Enum\ApiTypeEnum;
use App\Contracts\Enum\TableEnum;
use App\Module\Cache\Cache\Model\DictionaryCacheItem;
use App\Module\Cache\Enum\CacheEnum;
use App\Utils\BulkUtils;
use function count;

final readonly class DictionaryCache extends AbstractDoctrineCache
{
    public static function getType(): string
    {
        return ApiTypeEnum::DICTIONARY;
    }

    public function find(string $code): ?DictionaryCacheItem
    {
        return $this->getByCode($code);
    }

    public function exists(?string $code): bool
    {
        return null !== $this->getByCode($code);
    }

    public function warmup(): void
    {
        $this->prune();

        BulkUtils::run(function (int $offset, int $limit): int {
            $tableDictionary = TableEnum::DICTIONARY;

            $query = <<<SQL
            SELECT code
            FROM {$tableDictionary}
            LIMIT {$offset}, {$limit}
            SQL;

            $codes = $this->em->getConnection()->executeQuery($query)->fetchFirstColumn();
            foreach ($codes as $code) {
                $this->getByCode($code);
            }

            return count($codes);
        }, 500);
    }

    private function getByCode(?string $code): ?DictionaryCacheItem
    {
        if (null === $code) {
            return null;
        }

        $key = CacheEnum::getCacheKey(CacheEnum::DICTIONARY_BY_CODE, $code);

        $rows = $this->get($key, function () use ($code): ?array {
            $tableDictionaryMapping = TableEnum::DICTIONARY_MAPPING;

            $query = <<<SQL
            SELECT mapping_from, mapping_to
            FROM {$tableDictionaryMapping}
            WHERE dictionary_code = ?
            SQL;

            return $this->em->getConnection()->executeQuery($query, [$code])->fetchAllAssociative() ?: null;
        });

        return null === $rows ? null : DictionaryCacheItem::from($code, $rows);
    }
}
