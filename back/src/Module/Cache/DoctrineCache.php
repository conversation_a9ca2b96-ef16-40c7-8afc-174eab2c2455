<?php declare(strict_types=1);

namespace App\Module\Cache;

use App\Contracts\Enum\ApiTypeEnum;
use App\Contracts\Enum\LoggerChannelEnum;
use App\Module\Cache\Cache\AttributeCache;
use App\Module\Cache\Cache\AttributeGroupCache;
use App\Module\Cache\Cache\AttributeOptionCache;
use App\Module\Cache\Cache\CatalogCache;
use App\Module\Cache\Cache\CategoryCache;
use App\Module\Cache\Cache\ChannelCache;
use App\Module\Cache\Cache\CompletudeCache;
use App\Module\Cache\Cache\ContentCache;
use App\Module\Cache\Cache\ContentFolderCache;
use App\Module\Cache\Cache\CurrencyCache;
use App\Module\Cache\Cache\DictionaryCache;
use App\Module\Cache\Cache\DoctrineCacheInterface;
use App\Module\Cache\Cache\HubSourceCache;
use App\Module\Cache\Cache\LocaleCache;
use App\Module\Cache\Cache\MeasureFamilyCache;
use App\Module\Cache\Cache\MediaCache;
use App\Module\Cache\Cache\MediaFolderCache;
use App\Module\Cache\Cache\ProductCache;
use App\Module\Cache\Cache\RuleGroupCache;
use App\Module\Cache\Cache\ScopeCache;
use App\Module\Cache\Cache\UnitCache;
use App\Module\Cache\Cache\WorkflowCache;
use Monolog\Attribute\WithMonologChannel;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\TaggedLocator;
use Symfony\Component\DependencyInjection\ServiceLocator;
use Throwable;
use function array_keys;

#[WithMonologChannel(LoggerChannelEnum::DOCTRINE_CACHE)]
class DoctrineCache
{
    public LocaleCache $locales {
        get => $this->get(ApiTypeEnum::LOCALE);
    }

    public ChannelCache $channels {
        get => $this->get(ApiTypeEnum::CATALOG_SCOPE);
    }

    public DictionaryCache $dictionaries {
        get => $this->get(ApiTypeEnum::DICTIONARY);
    }

    public ScopeCache $scopes {
        get => $this->get(ApiTypeEnum::SCOPE);
    }

    public CurrencyCache $currencies {
        get => $this->get(ApiTypeEnum::CURRENCY);
    }

    public CatalogCache $catalogs {
        get => $this->get(ApiTypeEnum::CATALOG);
    }

    public CategoryCache $categories {
        get => $this->get(ApiTypeEnum::CATEGORY);
    }

    public MeasureFamilyCache $measureFamilies {
        get => $this->get(ApiTypeEnum::MEASURE_FAMILY);
    }

    public UnitCache $units {
        get => $this->get(ApiTypeEnum::UNIT);
    }

    public AttributeCache $attributes {
        get => $this->get(ApiTypeEnum::ATTRIBUTE);
    }

    public AttributeGroupCache $attributeGroups {
        get => $this->get(ApiTypeEnum::ATTRIBUTE_GROUP);
    }

    public AttributeOptionCache $attributeOptions {
        get => $this->get(ApiTypeEnum::ATTRIBUTE_OPTION);
    }

    public ProductCache $products {
        get => $this->get(ApiTypeEnum::PRODUCT);
    }

    public CompletudeCache $completudes {
        get => $this->get(ApiTypeEnum::COMPLETUDE);
    }

    public MediaFolderCache $mediaFolders {
        get => $this->get(ApiTypeEnum::MEDIA_FOLDER);
    }

    public MediaCache $medias {
        get => $this->get(ApiTypeEnum::MEDIA);
    }

    public ContentFolderCache $contentFolders {
        get => $this->get(ApiTypeEnum::CONTENT_FOLDER);
    }

    public ContentCache $contents {
        get => $this->get(ApiTypeEnum::CONTENT);
    }

    public WorkflowCache $workflows {
        get => $this->get(ApiTypeEnum::WORKFLOW);
    }

    public RuleGroupCache $ruleGroups {
        get => $this->get(ApiTypeEnum::RULE_GROUP);
    }

    public HubSourceCache $hubSources {
        get => $this->get(ApiTypeEnum::HUB_SOURCE);
    }

    /**
     * @phpstan-param ServiceLocator<DoctrineCacheInterface> $caches
     */
    public function __construct(
        #[TaggedLocator(DoctrineCacheInterface::class, defaultIndexMethod: 'getType')]
        private readonly ServiceLocator $caches,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @template T of array{
     *     Attribute: AttributeCache,
     *     AttributeGroup: AttributeGroupCache,
     *     AttributeOption: AttributeOptionCache,
     *     Catalog: CatalogCache,
     *     CatalogScope: ChannelCache,
     *     Category: CategoryCache,
     *     Channel: ChannelCache,
     *     Completude: CompletudeCache,
     *     Content: ContentCache,
     *     ContentFolder: ContentFolderCache,
     *     Currency: CurrencyCache,
     *     Dictionary: DictionaryCache,
     *     HubSource: HubSourceCache,
     *     Locale: LocaleCache,
     *     MeasureFamily: MeasureFamilyCache,
     *     Media: MediaCache,
     *     MediaFolder: MediaFolderCache,
     *     Product: ProductCache,
     *     RuleGroup: RuleGroupCache,
     *     Scope: ScopeCache,
     *     Unit: UnitCache,
     *     Workflow: WorkflowCache,
     * }
     * @template K of key-of<T>
     * @phpstan-param K $type
     * @return T[K]|null
     */
    public function get(string $type): ?DoctrineCacheInterface
    {
        try {
            /** @var AttributeCache|AttributeGroupCache|AttributeOptionCache|CatalogCache|CategoryCache|ChannelCache|CompletudeCache|ContentFolderCache|ContentCache|CurrencyCache|HubSourceCache|LocaleCache|MeasureFamilyCache|MediaFolderCache|MediaCache|ProductCache|RuleGroupCache|ScopeCache|UnitCache|WorkflowCache|null $cache */
            $cache = $this->caches->get($type);

            return $cache;
        } catch (Throwable $e) {
            $this->logger->error('unable to get doctrine cache', ['type' => $type, $e]);
        }

        return null;
    }

    public function warmup(?string $type = null): void
    {
        $types = match ($type) {
            ApiTypeEnum::RULE,
            ApiTypeEnum::WORKFLOW_STEP,
            ApiTypeEnum::WORKFLOW_STEP_RULE_GROUP => [
                ApiTypeEnum::WORKFLOW,
            ],
            ApiTypeEnum::RULE_GROUP => [
                ApiTypeEnum::WORKFLOW,
                ApiTypeEnum::RULE_GROUP,
            ],
            default => [$type],
        };

        foreach ($types as $type) {
            foreach (array_keys($this->caches->getProvidedServices()) as $expectedType) {
                if (null === $type || $type === $expectedType) {
                    try {
                        $this->logger->debug('warming cache', ['type' => $expectedType]);
                        $this->caches->get($expectedType)->warmup();
                    } catch (Throwable $exception) {
                        $this->logger->error('Error when warmup cache', ['type' => $expectedType, 'exception' => $exception]);
                    }
                }
            }
        }
    }
}
