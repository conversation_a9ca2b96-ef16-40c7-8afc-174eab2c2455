<?php declare(strict_types=1);

namespace App\Module\Cache\Enum;

use function sprintf;

abstract class CacheEnum
{
    public const string LOCALE_CODES = 'locale_codes';
    public const string LOCALE = 'locale_%s';
    public const string SCOPE_CODES = 'scope_codes';
    public const string SCOPE = 'scope_%s';
    public const string CURRENCY_BY_CODE = 'currency_by_code_%s';
    public const string CURRENCY_BY_SYMBOL = 'currency_by_symbol_%s';
    public const string CATALOG = 'catalog_%s';
    public const string CATEGORY = 'category_%s';
    public const string DICTIONARY_BY_CODE = 'dictionary_by_code_%s';
    public const string ATTRIBUTE = 'attribute_%s';
    public const string ATTRIBUTE_GROUP = 'attribute_group_%s';
    public const string ATTRIBUTE_OPTION_ID = 'attribute_option_%s';
    public const string ATTRIBUTE_OPTION_CODE = 'attribute_%s_option_%s';
    public const string COMPLETUDE_BY_CODE = 'completude_by_code_%s';
    public const string COMPLETUDES_BY_CHANNEL = 'completude_by_channel_%s';
    public const string CHANNEL = 'channel_%s';
    public const string COMPLETUDE_COUNT = 'completude_count';
    public const string PRODUCT_SKU = 'product_sku_%s';
    public const string PRODUCT_UUID = 'product_uuid_%s';
    public const string MEASURE_FAMILY = 'measure_family_%s';
    public const string UNIT = 'unit_%s_%s';
    public const string UNITS_BY_CODE = 'units_by_code_%s';
    public const string UNITS_BY_SYMBOL = 'units_by_symbol_%s';
    public const string MEDIA = 'media_%s';
    public const string MEDIA_FOLDER = 'media_folder_%s';
    public const string CONTENT = 'content_%s';
    public const string CONTENT_FOLDER = 'content_folder_%s';
    // WORKFLOW RELATED
    public const string WORKFLOW = 'workflow_%u';
    public const string WORKFLOW_COUNT = 'workflow_count';
    // HUB
    public const string HUB_SOURCE = 'hub_source_%s';
    public const string RULE_GROUP = 'rule_group_%s';

    public static function getCacheKey(string $key, int|string ...$identifiers): string
    {
        return sprintf($key, ...$identifiers);
    }
}
