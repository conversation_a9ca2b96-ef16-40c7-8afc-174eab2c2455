<?php declare(strict_types=1);

namespace App\Module\Cache\Enum;

abstract class CachePoolEnum
{
    public const string CACHE_POOL_ATTRIBUTE = 'cache.doctrine.attribute';
    public const string CACHE_POOL_ATTRIBUTE_GROUP = 'cache.doctrine.attribute_group';
    public const string CACHE_POOL_ATTRIBUTE_OPTION = 'cache.doctrine.attribute_option';
    public const string CACHE_POOL_CATALOG = 'cache.doctrine.catalog';
    public const string CACHE_POOL_CATEGORY = 'cache.doctrine.category';
    public const string CACHE_POOL_CURRENCY = 'cache.doctrine.currency';
    public const string CACHE_POOL_HUB_SOURCE = 'cache.doctrine.hub_source';
    public const string CACHE_POOL_LOCALE = 'cache.doctrine.locale';
    public const string CACHE_POOL_COMPLETUDE = 'cache.doctrine.completude';
    public const string CACHE_POOL_SCOPE = 'cache.doctrine.scope';
    public const string CACHE_POOL_PRODUCT = 'cache.doctrine.product';
    public const string CACHE_POOL_UNIT = 'cache.doctrine.unit';
    public const string CACHE_POOL_WORKFLOW = 'cache.doctrine.workflow';
    public const string CACHE_POOL_CHANNEL = 'cache.doctrine.channel';
    public const string CACHE_POOL_CONTENT = 'cache.doctrine.content';
    public const string CACHE_POOL_CONTENT_FOLDER = 'cache.doctrine.content_folder';
    public const string CACHE_POOL_MEDIA = 'cache.doctrine.media';
    public const string CACHE_POOL_MEDIA_FOLDER = 'cache.doctrine.media_folder';
    public const string CACHE_POOL_RULE_GROUP = 'cache.doctrine.rule_group';
    public const array ALL = [
        self::CACHE_POOL_ATTRIBUTE,
        self::CACHE_POOL_ATTRIBUTE_GROUP,
        self::CACHE_POOL_ATTRIBUTE_OPTION,
        self::CACHE_POOL_CATALOG,
        self::CACHE_POOL_CATEGORY,
        self::CACHE_POOL_CURRENCY,
        self::CACHE_POOL_HUB_SOURCE,
        self::CACHE_POOL_LOCALE,
        self::CACHE_POOL_COMPLETUDE,
        self::CACHE_POOL_SCOPE,
        self::CACHE_POOL_PRODUCT,
        self::CACHE_POOL_UNIT,
        self::CACHE_POOL_WORKFLOW,
        self::CACHE_POOL_CHANNEL,
        self::CACHE_POOL_CONTENT,
        self::CACHE_POOL_CONTENT_FOLDER,
        self::CACHE_POOL_MEDIA,
        self::CACHE_POOL_MEDIA_FOLDER,
        self::CACHE_POOL_RULE_GROUP,
    ];
}
