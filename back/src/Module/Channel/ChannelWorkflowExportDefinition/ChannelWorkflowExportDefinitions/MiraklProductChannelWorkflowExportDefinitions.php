<?php declare(strict_types=1);

namespace App\Module\Channel\ChannelWorkflowExportDefinition\ChannelWorkflowExportDefinitions;

use ApiPlatform\Api\UrlGeneratorInterface;
use App\Bridge\Mirakl\MiraklFactory;
use App\Bridge\Translation\Entity\Translations;
use App\Contracts\Enum\ChannelWorkflowExportTypeEnum;
use App\Module\Channel\Api\Resource\ChannelWorkflowExportMappingOption;
use App\Module\Channel\ChannelWorkflowExportDefinition\AbstractChannelWorkflowExportDefinition;
use App\Module\Channel\Entity\ChannelWorkflowExport;
use App\Module\Channel\Enum\ChannelAccessorEnum;
use App\Module\Channel\Enum\ChannelAdapterEnum;
use App\Module\Channel\Enum\ChannelFormatEnum;
use App\Module\Channel\Enum\ChannelScheduleEnum;
use App\Module\Channel\Enum\ChannelSourceEnum;
use App\Module\Channel\Handler\Format\CsvFormatHandler;
use App\Module\Channel\Model\Accessor\IgnoreMeAccessor;
use App\Module\Channel\Model\Accessor\PropertyAccessor;
use App\Module\Channel\Model\Adapter;
use App\Module\Channel\Model\Definition\AccessorDefinition;
use App\Module\Channel\Model\Definition\AdapterDefinition;
use App\Module\Channel\Model\Definition\AlertDefinition;
use App\Module\Channel\Model\Definition\FormatDefinition;
use App\Module\Channel\Model\Definition\MappingDefinition;
use App\Module\Channel\Model\Definition\ScheduleDefinition;
use App\Module\Channel\Model\Definition\SourceDefinition;
use App\Module\Channel\Model\Definition\UiDefinition;
use App\Module\Channel\Model\Format;
use App\Module\Channel\Model\Mapping;
use App\Module\Channel\Model\Schedule;
use App\Module\Channel\Model\Source;
use App\Utils\CsvUtils;
use Doctrine\ORM\EntityManagerInterface;

class MiraklProductChannelWorkflowExportDefinitions extends AbstractChannelWorkflowExportDefinition
{
    public function __construct(
        EntityManagerInterface $em,
        private readonly MiraklFactory $miraklFactory,
        private readonly UrlGeneratorInterface $urlGenerator,
    ) {
        parent::__construct($em);
    }

    public static function getType(): string
    {
        return ChannelWorkflowExportTypeEnum::MIRAKL_PRODUCT;
    }

    public function fillDefinition(ChannelWorkflowExport $channelWorkflowExport): ChannelWorkflowExport
    {
        $hasSchedule = null !== $channelWorkflowExport->schedule;
        $channelWorkflowExport->schedule ??= new Schedule(ChannelScheduleEnum::EVERY_HOURS, ['hours' => [0, 6, 12, 18]]);
        $channelWorkflowExport->schedule->definition = $this->getScheduleDefinition($channelWorkflowExport);

        if (!$hasSchedule) {
            $channelWorkflowExport->uiDefinition->isValid = false;

            return $channelWorkflowExport;
        }

        $hasSource = null !== $channelWorkflowExport->source;
        $channelWorkflowExport->source ??= new Source(ChannelSourceEnum::PRODUCT);
        $channelWorkflowExport->source->definition = $this->getSourceDefinition($channelWorkflowExport);
        if (!$hasSource) {
            $channelWorkflowExport->uiDefinition->isValid = false;

            return $channelWorkflowExport;
        }

        $hasFormat = null !== $channelWorkflowExport->format;
        $channelWorkflowExport->format ??= new Format(ChannelFormatEnum::CSV, [CsvFormatHandler::DELIMITER_KEY => CsvUtils::SEPARATOR_SEMICOLON]);
        $channelWorkflowExport->format->definition = $this->getFormatDefinition($channelWorkflowExport);
        if (!$hasFormat) {
            $channelWorkflowExport->uiDefinition->isValid = false;

            return $channelWorkflowExport;
        }

        $hasAdapter = null !== $channelWorkflowExport->adapter;
        $channelWorkflowExport->adapter ??= new Adapter(ChannelAdapterEnum::LOCAL, ['filename' => 'products.csv']);
        $channelWorkflowExport->adapter->definition = $this->getAdapterDefinition($channelWorkflowExport);

        $hasMapping = null !== $channelWorkflowExport->mapping;
        $channelWorkflowExport->mapping ??= new Mapping();
        $channelWorkflowExport->mapping->definition = $this->getMappingDefinition($channelWorkflowExport);

        if (!$hasAdapter || !$hasMapping) {
            $channelWorkflowExport->uiDefinition->isValid = false;
        }

        return $channelWorkflowExport;
    }

    public function getScheduleDefinition(ChannelWorkflowExport $channelWorkflowExport): ScheduleDefinition
    {
        return new ScheduleDefinition(
            ui: new UiDefinition(isEnabled: true),
            sync: null,
            types: [ChannelScheduleEnum::EVERY_HOURS],
            max: 4,
        );
    }

    public function getSourceDefinition(ChannelWorkflowExport $channelWorkflowExport): SourceDefinition
    {
        return new SourceDefinition(
            ui: new UiDefinition(isEnabled: true),
            types: [ChannelSourceEnum::PRODUCT],
            isFilterable: true,
            isOrderable: false,
        );
    }

    public function getFormatDefinition(ChannelWorkflowExport $channelWorkflowExport): FormatDefinition
    {
        return new FormatDefinition(
            ui: new UiDefinition(isEnabled: true),
            types: [ChannelFormatEnum::CSV, ChannelFormatEnum::XML],
        );
    }

    public function getAdapterDefinition(ChannelWorkflowExport $channelWorkflowExport): AdapterDefinition
    {
        return new AdapterDefinition(
            ui: new UiDefinition(),
            types: [ChannelAdapterEnum::LOCAL, ChannelAdapterEnum::FTP],
        );
    }

    public function getMappingDefinition(ChannelWorkflowExport $channelWorkflowExport): MappingDefinition
    {
        $mirakl = $this->miraklFactory->getMirakl($channel = $channelWorkflowExport->channel);
        $params = $channel->getParametersAsObject();
        $mappingDefinition = new MappingDefinition(new UiDefinition(isEnabled: true));

        $defaultIn = new IgnoreMeAccessor(
            accessorDefinition: new AccessorDefinition(
                types: ChannelAccessorEnum::EXPORT_SUPPORTED_ACCESSORS
            )
        );

        foreach ($mirakl->getProductAttributes() as $attribute) {
            $in = null;
            if ($attribute->belongToMiraklConfig($params)) {
                $optionIri = null;
                if ($attribute->hasList()) {
                    $optionIri = $this->urlGenerator->generate(ChannelWorkflowExportMappingOption::ROUTE_NAME, [
                        'uuid' => $channelWorkflowExport->uuid,
                        'code' => $attribute->code,
                    ]);
                }

                if ('shop_sku' === $attribute->code) {
                    $in = new PropertyAccessor('sku', new AccessorDefinition(
                        ui: new UiDefinition(
                            isRequired: true,
                            isReadonly: true,
                            isValid: true,
                            alert: AlertDefinition::info(
                                new Translations(
                                    fr: 'Pré-rempli par Sinfin',
                                    en: 'Pre-filled by Sinfin',
                                ),
                                new Translations(
                                    fr: 'Ce champ est nécessaire à une bonne intégration chez Sinfin et a été pré-rempli.',
                                    en: 'This field is required for proper integration with Sinfin and has been pre-filled',
                                ),
                            )
                        ),
                        types: [ChannelAccessorEnum::PROPERTY_ACCESSOR]
                    ));
                }

                $channelWorkflowExport->mapping->addOrUpdateOutColumn(
                    out: new PropertyAccessor(
                        property: $attribute->code,
                        accessorDefinition: new AccessorDefinition(
                            ui: new UiDefinition(isReadonly: true),
                        ),
                    ),
                    mappingColumnDefinition: $attribute->toMappingColumnDefinition($optionIri),
                    defaultIn: $in ?? $defaultIn
                );
            }
        }

        return $mappingDefinition;
    }

    public function getAttributeOptions(ChannelWorkflowExport $channelWorkflowExport, string $code): array
    {
        $mirakl = $this->miraklFactory->getMirakl($channelWorkflowExport->channel);

        foreach ($mirakl->getProductAttributes() as $attribute) {
            if ($attribute->code === $code) {
                return $mirakl
                    ->getAttributeValues($attribute->valuesList)
                    ->get($attribute->valuesList)
                    ?->toMappingColumnChoiceDefinitions() ?? [];
            }
        }

        return [];
    }
}
