<?php declare(strict_types=1);

namespace App\Module\Channel\Enum;

abstract class ChannelAccessorEnum
{
    public const string ATTRIBUTE_ACCESSOR = 'ExAttributeAccessor';
    public const string CALLBACK_ACCESSOR = 'ExCallbackAccessor';
    public const string EMBEDDED_ACCESSOR = 'ExEmbeddedAccessor';
    public const string FORMULA_ACCESSOR = 'ExFormulaAccessor';
    public const string IGNORE_ME_ACCESSOR = 'ExIgnoreMeAccessor';
    public const string PROPERTY_ACCESSOR = 'ExPropertyAccessor';
    public const string RAW_ACCESSOR = 'ExRawAccessor';

    public const array ALL = [
        self::ATTRIBUTE_ACCESSOR,
        self::CALLBACK_ACCESSOR,
        self::EMBEDDED_ACCESSOR,
        self::FORMULA_ACCESSOR,
        self::IGNORE_ME_ACCESSOR,
        self::PROPERTY_ACCESSOR,
        self::RAW_ACCESSOR,
    ];

    public const array EXPORT_SUPPORTED_ACCESSORS = [
        self::ATTRIBUTE_ACCESSOR,
        self::FORMULA_ACCESSOR,
        self::IGNORE_ME_ACCESSOR,
        self::PROPERTY_ACCESSOR,
        self::RAW_ACCESSOR,
    ];
}
