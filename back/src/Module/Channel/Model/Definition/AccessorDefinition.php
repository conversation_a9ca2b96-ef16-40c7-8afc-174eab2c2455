<?php declare(strict_types=1);

namespace App\Module\Channel\Model\Definition;

use ApiPlatform\Metadata as APM;
use App\Module\Channel\Enum\ChannelAccessorEnum;

final readonly class AccessorDefinition
{
    /**
     * @param string[] $types
     */
    public function __construct(
        public UiDefinition $ui = new UiDefinition(),
        #[APM\ApiProperty(openapiContext: ['enum' => ChannelAccessorEnum::ALL])]
        public array $types = [],
    ) {
    }
}
