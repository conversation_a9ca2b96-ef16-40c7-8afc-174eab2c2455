<?php declare(strict_types=1);

namespace App\Module\Channel\Model\Definition;

final class MappingDefinition
{
    public function __construct(
        public UiDefinition $ui = new UiDefinition(),
        public bool $hasRoot = false,
        public ?string $root = null,
        public bool $canAddColumn = false,
    ) {
    }

    public function withRoot(string $root): self
    {
        $this->hasRoot = true;
        $this->root = $root;

        return $this;
    }
}
