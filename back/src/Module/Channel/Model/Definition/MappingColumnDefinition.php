<?php declare(strict_types=1);

namespace App\Module\Channel\Model\Definition;

use ApiPlatform\Metadata as APM;
use App\Bridge\Translation\Entity\Translations;
use App\Contracts\Enum\TypeEnum;
use Symfony\Component\Uid\Uuid;

final readonly class MappingColumnDefinition
{
    /**
     * @param ?string $options iri to options
     */
    public function __construct(
        public Uuid $uuid,
        public UiDefinition $ui,
        public Translations $names,
        public ?Translations $descriptions,
        public bool $isRecommended = false,
        public bool $isRemovable = false,
        #[APM\ApiProperty(openapiContext: ['enum' => TypeEnum::ALL])]
        public ?string $type = null,
        public ?string $options = null,
    ) {
    }
}
