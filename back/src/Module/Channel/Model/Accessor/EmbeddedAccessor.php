<?php declare(strict_types=1);

namespace App\Module\Channel\Model\Accessor;

use App\Module\Channel\Enum\ChannelAccessorEnum;
use App\Module\Channel\Model\Definition\AccessorDefinition;
use App\Module\Channel\Model\Mapping;

class EmbeddedAccessor extends AbstractAccessor
{
    public const string TYPE = ChannelAccessorEnum::EMBEDDED_ACCESSOR;

    public function __construct(
        public Mapping $mapping,
        AccessorDefinition $accessorDefinition = new AccessorDefinition(),
    ) {
        parent::__construct($accessorDefinition);
    }

    public function getType(): string
    {
        return self::TYPE;
    }
}
