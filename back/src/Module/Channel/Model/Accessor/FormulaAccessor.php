<?php declare(strict_types=1);

namespace App\Module\Channel\Model\Accessor;

use App\Module\Channel\Enum\ChannelAccessorEnum;
use App\Module\Channel\Model\Definition\AccessorDefinition;

class FormulaAccessor extends AbstractAccessor
{
    public const string TYPE = ChannelAccessorEnum::FORMULA_ACCESSOR;

    public function __construct(
        public string $formula,
        public ?string $locale = null,
        public ?string $scope = null,
        AccessorDefinition $accessorDefinition = new AccessorDefinition(),
    ) {
        parent::__construct($accessorDefinition);
    }

    public function getType(): string
    {
        return self::TYPE;
    }
}
