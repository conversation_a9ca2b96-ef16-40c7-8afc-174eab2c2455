<?php declare(strict_types=1);

namespace App\Module\Channel\Model\Accessor;

use App\Module\Channel\Enum\ChannelAccessorEnum;
use App\Module\Channel\Model\Definition\AccessorDefinition;

class RawAccessor extends AbstractAccessor
{
    public const string TYPE = ChannelAccessorEnum::RAW_ACCESSOR;

    public function __construct(
        public string|int|float|bool $raw,
        AccessorDefinition $accessorDefinition = new AccessorDefinition(),
    ) {
        parent::__construct($accessorDefinition);
    }

    public function getType(): string
    {
        return self::TYPE;
    }
}
