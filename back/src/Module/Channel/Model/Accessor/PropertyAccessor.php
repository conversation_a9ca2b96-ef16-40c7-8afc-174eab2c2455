<?php declare(strict_types=1);

namespace App\Module\Channel\Model\Accessor;

// @todo rename to avoid confusion with symfony ?

use App\Module\Channel\Enum\ChannelAccessorEnum;
use App\Module\Channel\Model\Definition\AccessorDefinition;

class PropertyAccessor extends AbstractAccessor
{
    public const string TYPE = ChannelAccessorEnum::PROPERTY_ACCESSOR;

    public function __construct(
        public string $property,
        AccessorDefinition $accessorDefinition = new AccessorDefinition(),
    ) {
        parent::__construct($accessorDefinition);
    }

    public function getType(): string
    {
        return self::TYPE;
    }

    public function getId(): int|string
    {
        return $this->property;
    }
}
