<?php declare(strict_types=1);

namespace App\Module\Channel\Model\Accessor;

use App\Module\Channel\Enum\ChannelAccessorEnum;
use App\Module\Channel\Model\Definition\AccessorDefinition;

final class CallbackAccessor extends AbstractAccessor
{
    public const string TYPE = ChannelAccessorEnum::CALLBACK_ACCESSOR;

    public function __construct(
        public mixed $fn,
        AccessorDefinition $accessorDefinition = new AccessorDefinition(),
    ) {
        parent::__construct($accessorDefinition);
    }

    public function getType(): string
    {
        return self::TYPE;
    }
}
