<?php declare(strict_types=1);

namespace App\Module\Channel\Model\Accessor;

use App\Contracts\Model\Header;
use App\Module\Channel\Enum\ChannelAccessorEnum;
use App\Module\Channel\Model\Definition\AccessorDefinition;

class AttributeAccessor extends AbstractAccessor
{
    public const string TYPE = ChannelAccessorEnum::ATTRIBUTE_ACCESSOR;

    public function __construct(
        public string $code,
        public ?string $locale = null,
        public ?string $scope = null,
        AccessorDefinition $accessorDefinition = new AccessorDefinition(),
    ) {
        parent::__construct($accessorDefinition);
    }

    public function getType(): string
    {
        return self::TYPE;
    }

    public function asHeader(): Header
    {
        return Header::attribute($this->code, $this->locale, $this->scope);
    }

    public function getId(): int|string
    {
        return $this->asHeader()->toString();
    }
}
