<?php declare(strict_types=1);

namespace App\Module\Channel\Model\Accessor;

use App\Module\Channel\Model\Definition\AccessorDefinition;
use Symfony\Component\Serializer\Attribute\Ignore;

abstract class AbstractAccessor implements AccessorInterface
{
    public function __construct(
        public AccessorDefinition $definition = new AccessorDefinition(),
    ) {
    }

    #[Ignore]
    public function getId(): int|string
    {
        return uniqid();
    }
}
