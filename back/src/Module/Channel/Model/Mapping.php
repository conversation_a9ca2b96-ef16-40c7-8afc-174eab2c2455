<?php declare(strict_types=1);

namespace App\Module\Channel\Model;

use ApiPlatform\Metadata\ApiProperty;
use App\Bridge\Translation\Entity\Translations;
use App\Contracts\Model\Header;
use App\Module\Channel\Model\Accessor\AbstractAccessor;
use App\Module\Channel\Model\Accessor\AttributeAccessor;
use App\Module\Channel\Model\Accessor\FormulaAccessor;
use App\Module\Channel\Model\Accessor\IgnoreMeAccessor;
use App\Module\Channel\Model\Accessor\PropertyAccessor;
use App\Module\Channel\Model\Definition\MappingColumnDefinition;
use App\Module\Channel\Model\Definition\MappingDefinition;
use App\Utils\ArrUtils;
use App\Utils\UuidUtils;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Validator\Constraints as Assert;

final class Mapping
{
    public const string TYPE = 'ExMapping';

    /**
     * @param MappingColumn[] $columns
     */
    public function __construct(
        #[Assert\Count(min: 1)]
        #[Assert\Valid]
        public array $columns = [],
        public ?string $root = null,
        #[ApiProperty(writable: false)]
        public ?MappingDefinition $definition = null,
    ) {
    }

    public function exportProperty(string $in, ?string $out = null, ?Uuid $uuid = null): self
    {
        return $this->add(new MappingColumn(
            uuid: $uuid ?? UuidUtils::new(),
            in: new PropertyAccessor($in),
            out: new PropertyAccessor($out ?? $in),
        ));
    }

    public function add(MappingColumn ...$columns): self
    {
        foreach ($columns as $column) {
            $this->columns[] = $column;
        }

        return $this;
    }

    public function exportAttribute(Header $header, ?Uuid $uuid = null): self
    {
        return $this->add(new MappingColumn(
            uuid: $uuid ?? UuidUtils::new(),
            in: new AttributeAccessor(
                $header->code,
                $header->localeCode,
                $header->scopeCode,
            ),
            out: new PropertyAccessor($header->toString()),
        ));
    }

    public function exportFormula(string $formula, string $out, ?Uuid $uuid = null): self
    {
        return $this->add(new MappingColumn(
            uuid: $uuid ?? UuidUtils::new(),
            in: new FormulaAccessor($formula),
            out: new PropertyAccessor($out),
        ));
    }

    /** @todo insane shortcut to retrieve CSV mapping */
    public function toArray(): array
    {
        return ArrUtils::vf(array_map(static function (MappingColumn $column): ?string {
            if ($column->out instanceof PropertyAccessor) {
                return $column->out->property;
            }

            return null;
        }, $this->columns));
    }

    public function addTemplatedColumn(string $out, ?Translations $helper = null): self
    {
        $column = new MappingColumn(
            uuid: UuidUtils::new(),
            in: new IgnoreMeAccessor(),
            out: new PropertyAccessor($out),
        );

        if (null !== $helper) {
            $column->withHelper($helper);
        }

        return $this->add($column);
    }

    public function addOrUpdateOutColumn(
        AbstractAccessor $out,
        MappingColumnDefinition $mappingColumnDefinition,
        AbstractAccessor $defaultIn = new IgnoreMeAccessor(),
    ): void {
        /** @var ?MappingColumn $column */
        $column = array_find(
            $this->columns,
            fn($column) => $column->out->getId() === $out->getId()
        );

        if (null === $column) {
            $column = new MappingColumn(
                uuid: UuidUtils::new(),
                in: $defaultIn,
                out: $out,
            );
            $this->add($column);
        } else {
            $column->in->definition = $defaultIn->definition;
        }

        $column->definition = $mappingColumnDefinition;
    }
}
