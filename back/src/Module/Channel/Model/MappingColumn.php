<?php declare(strict_types=1);

namespace App\Module\Channel\Model;

use ApiPlatform\Metadata\ApiProperty;
use App\Bridge\Translation\Entity\Translations;
use App\Module\Channel\Model\Accessor\AbstractAccessor;
use App\Module\Channel\Model\Accessor\PropertyAccessor;
use App\Module\Channel\Model\Definition\MappingColumnDefinition;
use App\Utils\UuidUtils;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Validator\Constraints\Valid;

final class MappingColumn
{
    public const string TYPE = 'ExMappingColumn';

    private ?Translations $helper = null;

    public function __construct(
        public Uuid $uuid,
        #[Valid]
        public ?AbstractAccessor $in,
        #[Valid]
        public ?AbstractAccessor $out,
        #[ApiProperty(writable: false)]
        public ?MappingColumnDefinition $definition = null,
    ) {
    }

    public static function propertyMapping(
        string $propertyName,
        ?string $outputName = null,
        ?Uuid $uuid = null,
    ): self {
        return new self(
            uuid: $uuid ?? UuidUtils::new(),
            in: new PropertyAccessor($propertyName),
            out: new PropertyAccessor($outputName ?? $propertyName),
        );
    }

    public function getHelper(): ?Translations
    {
        return $this->helper;
    }

    public function withHelper(Translations $helper): self
    {
        $this->helper = $helper;

        return $this;
    }
}
