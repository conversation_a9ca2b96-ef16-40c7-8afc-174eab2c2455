<?php declare(strict_types=1);

namespace App\Module\Scheduler\Message;

use App\Utils\DateUtils;
use DateTime;

final class CleanAbstractLogMessage extends AbstractMessage
{
    public const string DEFAULT_OLDER_THAN = '6 month ago';

    public function __construct(
        public ?DateTime $olderThan = null,
    ) {
        if (null === $olderThan) {
            $this->olderThan = DateUtils::new(self::DEFAULT_OLDER_THAN);
        }
    }
}
