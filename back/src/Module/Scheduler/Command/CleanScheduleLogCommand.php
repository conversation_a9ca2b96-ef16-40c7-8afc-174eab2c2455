<?php declare(strict_types=1);

namespace App\Module\Scheduler\Command;

use App\Module\Scheduler\Message\CleanAbstractLogMessage;
use App\Utils\DateUtils;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsCommand('app:module:scheduler:clean-log', 'Clean old schedule logs')]
final class CleanScheduleLogCommand extends Command
{
    public function __construct(
        private readonly MessageBusInterface $bus,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $default = CleanAbstractLogMessage::DEFAULT_OLDER_THAN;

        $this->addArgument('olderThan', InputArgument::OPTIONAL, "Delete all schedule log older than {$default}", $default);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->bus->dispatch(new CleanAbstractLogMessage(DateUtils::new($input->getArgument('olderThan'))));

        $output->writeln('<info>Log cleaned</info>');

        return self::SUCCESS;
    }
}
