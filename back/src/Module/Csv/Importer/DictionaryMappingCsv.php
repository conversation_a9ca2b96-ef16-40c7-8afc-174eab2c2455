<?php declare(strict_types=1);

namespace App\Module\Csv\Importer;

use App\Api\Model\FilterHelper;
use App\Module\Channel\Model\Mapping;
use App\Module\Csv\Csv\AbstractCsvRows;
use App\Module\Csv\Enum\CsvTypeEnum;
use App\Module\Csv\Model\CsvRow;
use App\Module\Injector\Model\DictionaryMappingImport;
use Generator;

final readonly class DictionaryMappingCsv extends AbstractCsvRows
{
    public static function getType(): string
    {
        return CsvTypeEnum::DICTIONARY_MAPPING;
    }

    public function generateImports(Generator $rows): Generator
    {
        /** @var CsvRow $row */
        foreach ($rows as $row) {
            yield new DictionaryMappingImport(
                dictionaryCode: $row->get('dictionary_code'),
                from: $row->get('from'),
                to: $row->get('to'),
            )->withRow($row->line)->killMe($row->hasToBeDeleted);
        }
    }

    public function getMapping(FilterHelper $filter): Mapping
    {
        return new Mapping()
            ->exportProperty(in: 'dictionary.code', out: 'dictionary_code')
            ->exportProperty(in: 'from', out: 'from')
            ->exportProperty(in: 'to', out: 'to');
    }
}
