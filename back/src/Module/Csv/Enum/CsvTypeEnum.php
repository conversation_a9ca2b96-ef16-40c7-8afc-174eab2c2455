<?php declare(strict_types=1);

namespace App\Module\Csv\Enum;

use App\Contracts\Enum\ApiTypeEnum;

abstract class CsvTypeEnum
{
    public const string ATTRIBUTE = ApiTypeEnum::ATTRIBUTE;
    public const string ATTRIBUTE_GROUP = ApiTypeEnum::ATTRIBUTE_GROUP;
    public const string ATTRIBUTE_OPTION = ApiTypeEnum::ATTRIBUTE_OPTION;
    public const string CHANNEL = ApiTypeEnum::CATALOG_SCOPE;
    public const string COMPLETUDE = ApiTypeEnum::COMPLETUDE;
    public const string CONTENT = ApiTypeEnum::CONTENT;
    public const string CURRENCY = ApiTypeEnum::CURRENCY;
    public const string DICTIONARY_MAPPING = ApiTypeEnum::DICTIONARY_MAPPING;
    public const string HUB_ORDER = ApiTypeEnum::HUB_ORDER;
    public const string HUB_ORDER_ITEM = ApiTypeEnum::HUB_ORDER_ITEM;
    public const string HUB_ORIGIN = 'HubOrigin';
    public const string HUB_INVOICE = 'HubInvoice';
    public const string HUB_STOCK = ApiTypeEnum::HUB_STOCK;
    public const string HUB_SOURCE = ApiTypeEnum::HUB_SOURCE;
    public const string LOCALE = ApiTypeEnum::LOCALE;
    public const string MEDIA = ApiTypeEnum::MEDIA;
    public const string PRODUCT = ApiTypeEnum::PRODUCT;
    public const string RULE = ApiTypeEnum::RULE;
    public const string RULE_GROUP = ApiTypeEnum::RULE_GROUP;
    public const string SCOPE = ApiTypeEnum::SCOPE;
    public const string TEMPLATE = ApiTypeEnum::TEMPLATE;
    public const string USER = ApiTypeEnum::USER;
    public const array ALL = [
        self::ATTRIBUTE,
        self::ATTRIBUTE_GROUP,
        self::ATTRIBUTE_OPTION,
        self::CHANNEL,
        self::COMPLETUDE,
        self::CONTENT,
        self::CURRENCY,
        self::DICTIONARY_MAPPING,
        self::HUB_ORDER,
        self::HUB_ORDER_ITEM,
        self::HUB_ORIGIN,
        self::HUB_INVOICE,
        self::HUB_STOCK,
        self::HUB_SOURCE,
        self::LOCALE,
        self::MEDIA,
        self::PRODUCT,
        self::RULE,
        self::RULE_GROUP,
        self::SCOPE,
        self::TEMPLATE,
        self::USER,
    ];
    public const array HUB_ATTRIBUTE = [
        self::HUB_STOCK => self::PRODUCT,
    ];
}
