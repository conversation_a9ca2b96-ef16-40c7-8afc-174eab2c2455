<?php declare(strict_types=1);

namespace App\Bridge\Filter\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Bridge\Eav\Entity\Attribute;
use App\Bridge\Eav\Entity\AttributeOption;
use App\Bridge\Filter\Api\Resource\FiltersResolve;
use App\Bridge\Filter\Model\Filter;
use App\Contracts\Type\AttributeTypes;
use App\Entity\Locale;
use App\Entity\Scope;
use Doctrine\ORM\EntityManagerInterface;
use function array_unique;
use function count;
use function is_array;
use function is_string;
use function iterator_to_array;

/**
 * @template-implements ProcessorInterface<FiltersResolve, FiltersResolve>
 */
final readonly class FiltersResolveProcessor implements ProcessorInterface
{
    public function __construct(
        private AttributeTypes $attributeTypes,
        private EntityManagerInterface $em,
    ) {
    }

    /**
     * @param FiltersResolve $data
     * @todo security of attributes
     */
    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): FiltersResolve
    {
        $attributeCodes = array_unique(iterator_to_array($data->filters->getAttributeCodes()));
        $localeCodes = array_unique(iterator_to_array($data->filters->getLocales()));
        $scopeCodes = array_unique(iterator_to_array($data->filters->getScopes()));

        /** @var array<string, Locale> $locales */
        $locales = [];
        if (0 < count($localeCodes)) {
            $locales = $this->em->getRepository(Locale::class)->findBy(['code' => $localeCodes]);
            $locales = array_combine(array_map(static fn(Locale $locale): string => $locale->code, $locales), $locales);
        }

        /** @var array<string, Attribute> $attributes */
        $attributes = [];
        if (0 < count($attributeCodes)) {
            $attributes = $this->em->getRepository(Attribute::class)->findBy(['code' => $attributeCodes]);
            $attributes = array_combine(array_map(static fn(Attribute $attribute): string => $attribute->code, $attributes), $attributes);
        }

        /** @var array<string, Locale> $scopes */
        $scopes = [];
        if (0 < count($scopeCodes)) {
            $scopes = $this->em->getRepository(Scope::class)->findBy(['code' => $scopeCodes]);
            $scopes = array_combine(array_map(static fn(Scope $scope): string => $scope->code, $scopes), $scopes);
        }

        $attributeOptionCodes = [];
        foreach ($data->filters->filters as $filter) {
            // @todo handle Filters
            if ($filter instanceof Filter) {
                $attribute = $attributes[$filter->header->code] ?? null;
                if (null !== $attribute && $this->attributeTypes->hasOption($attribute->type)) {
                    if (is_string($filter->value)) {
                        $attributeOptionCodes[] = $filter->value;
                    } elseif (is_array($filter->value)) {
                        $attributeOptionCodes = [...$attributeOptionCodes, ...$filter->value];
                    }
                }
            }
        }

        /** @var array<string, AttributeOption> $attributeOptions */
        $attributeOptions = [];
        if (0 < count($attributeOptionCodes)) {
            $attributeOptions = $this->em->getRepository(AttributeOption::class)->findBy(['code' => $attributeOptionCodes]);
            $attributeOptions = array_combine(
                array_map(static fn(AttributeOption $attributeOption): string => $attributeOption->code, $attributeOptions),
                $attributeOptions,
            );
        }

        // generate response
        $data->id = time();
        $data->attributes = $attributes;
        $data->locales = $locales;
        $data->attributeOptions = $attributeOptions;

        return $data;
    }
}
