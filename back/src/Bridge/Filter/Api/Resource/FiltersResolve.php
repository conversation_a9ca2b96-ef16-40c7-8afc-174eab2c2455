<?php declare(strict_types=1);

namespace App\Bridge\Filter\Api\Resource;

use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation as OpenApiOperation;
use App\Api\OpenApi\OpenApiFactory;
use App\Bridge\Eav\Entity\Attribute;
use App\Bridge\Eav\Entity\AttributeOption;
use App\Bridge\Filter\Api\Processor\FiltersResolveProcessor;
use App\Bridge\Filter\Model\Filters;
use App\Entity\Locale;
use App\Entity\Scope;
use Symfony\Component\Validator\Constraints\NotNull;
use Symfony\Component\Validator\Constraints\Valid;

#[APM\ApiResource(openapi: new OpenApiOperation(tags: [OpenApiFactory::TAG_CATALOG]))]
#[APM\Post(processor: FiltersResolveProcessor::class)]
class FiltersResolve
{
    #[APM\ApiProperty(writable: false, identifier: true)]
    public ?int $id = null;

    #[NotNull]
    #[Valid]
    public ?Filters $filters = null;

    /**
     * @var Attribute[]
     * @phpstan-var array<string, Attribute>
     */
    #[APM\ApiProperty(writable: false)]
    public array $attributes = [];

    /**
     * @var AttributeOption[]
     * @phpstan-var array<string, AttributeOption>
     */
    #[APM\ApiProperty(writable: false)]
    public array $attributeOptions = [];

    /**
     * @var Locale[]
     * @phpstan-var array<string, Locale>
     */
    #[APM\ApiProperty(writable: false)]
    public array $locales = [];

    /**
     * @var Scope[]
     * @phpstan-var array<string, Scope>
     */
    #[APM\ApiProperty(writable: false)]
    public array $scopes = [];
}
