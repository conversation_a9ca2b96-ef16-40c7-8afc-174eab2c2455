<?php declare(strict_types=1);

namespace App\Bridge\Filter\Serializer;

use ApiPlatform\Validator\Exception\ValidationException;
use App\Api\Model\FilterHelper;
use App\Bridge\Filter\Enum\CombineOperatorEnum;
use App\Bridge\Filter\Enum\FilterTypeEnum;
use App\Bridge\Filter\Model\Filter;
use App\Bridge\Filter\Model\Filters;
use App\Contracts\Model\Header;
use App\Contracts\Type\Enum\AttributeTypeEnum;
use App\Module\Cache\DoctrineCache;
use App\Utils\StrUtils;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use function is_array;

final readonly class FiltersSerializer implements NormalizerInterface, DenormalizerInterface
{
    private const int MAX_FILTER_DEPTH = 3;

    public function __construct(
        private DoctrineCache $doctrineCache,
    ) {
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof Filters;
    }

    /**
     * @phpstan-param Filters $object
     */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        return $this->encode($object);
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        return is_array($data) && Filters::class === $type;
    }

    /**
     * @phpstan-param array $data
     */
    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): Filters
    {
        return $this->decode($data);
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            Filters::class => true,
        ];
    }

    private function encode(Filters $filters, int $filterDepth = 0): array
    {
        $data = [];
        $data['operator'] = $filters->operator;
        $filterDepth++;

        foreach ($filters->filters as $filter) {
            if ($filter instanceof Filter) {
                $data['filters'][] = [
                    'header' => $filter->header->toString(),
                    'operator' => $filter->operator,
                    'value' => $this->normalizeValue($filter->header, $filter->value),
                ];
            } elseif ($filter instanceof Filters) {
                if (self::MAX_FILTER_DEPTH >= $filterDepth) {
                    $data['filters'][] = $this->encode($filter, $filterDepth);
                }
            }
        }

        return $data;
    }

    private function decode(array $data, int $filterDepth = 0): Filters
    {
        $filters = [];
        $filterDepth++;

        if (isset($data['filters'])) {
            foreach ($data['filters'] as $filter) {
                $header = StrUtils::nullOrString($filter['header'] ?? null);
                if (null !== $header) {
                    $filters[] = new Filter(
                        $header = Header::from($filter['header']),
                        $filter['operator'] ?? null,
                        $this->normalizeValue(
                            $header,
                            $filter['value'] ?? null,
                        ),
                    );
                } elseif (isset($filter['filters'])) {
                    if (self::MAX_FILTER_DEPTH >= $filterDepth) {
                        $filters[] = $this->decode($filter, $filterDepth);
                    }
                } else {
                    throw new ValidationException('Invalid filter');
                }
            }
        }

        return new Filters($filters, $data['operator'] ?? CombineOperatorEnum::AND);
    }

    private function normalizeValue(Header $header, mixed $value): mixed
    {
        if (FilterTypeEnum::ATTRIBUTE === $header->type) {
            if (null !== $attribute = $this->doctrineCache->attributes->find($header->code)) {
                // @todo we cannot use fromScalar because it requires a UUID
                // maybe introduce a normalize method in TypeInterface?
                // $value = $this->attributeTypes->fromScalar();
                if (AttributeTypeEnum::SWITCH === $attribute->type) {
                    $value = FilterHelper::toBool($value);
                }
            }
        }

        return $value;
    }
}
