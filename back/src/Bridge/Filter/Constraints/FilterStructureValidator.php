<?php declare(strict_types=1);

namespace App\Bridge\Filter\Constraints;

use App\Bridge\Filter\Enum\FilterTypeEnum;
use App\Bridge\Filter\Model\Filter;
use App\Contracts\Type\AttributeTypes;
use App\Contracts\Type\Enum\AttributeTypeEnum;
use App\Module\Cache\DoctrineCache;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use function in_array;

final class FilterStructureValidator extends ConstraintValidator
{
    public const array PROPERTIES = [
        'id' => AttributeTypeEnum::NUMBER, // ok
        'uuid' => AttributeTypeEnum::TEXT, // ok
        'documentUuid' => AttributeTypeEnum::TEXT, // ok
        'sku' => AttributeTypeEnum::TEXT, // ok
        'documentSku' => AttributeTypeEnum::TEXT, // ok
        'name' => AttributeTypeEnum::TEXT, // ok
        'documentName' => AttributeTypeEnum::TEXT, // ok
        'createdAt' => AttributeTypeEnum::DATE, // ok
        'updatedAt' => AttributeTypeEnum::DATE, // ok
        'status' => AttributeTypeEnum::SWITCH, // ok
        'catalogs' => AttributeTypeEnum::MULTISELECT,
        'productSku' => AttributeTypeEnum::TEXT,
        'catalogScope' => AttributeTypeEnum::TEXT,
        'catalogScopeCode' => AttributeTypeEnum::TEXT,
        'catalogCode' => AttributeTypeEnum::TEXT,
        'scopeCode' => AttributeTypeEnum::TEXT,
        'locale' => AttributeTypeEnum::TEXT,
        'localeCode' => AttributeTypeEnum::TEXT,
        'workflowId' => AttributeTypeEnum::NUMBER,
        'workflowStepId' => AttributeTypeEnum::NUMBER,
        'rule' => AttributeTypeEnum::TEXT,
        'ruleCode' => AttributeTypeEnum::TEXT,
        'ruleGroupCode' => AttributeTypeEnum::TEXT,
        'level' => AttributeTypeEnum::TEXT,
        'ruleLevel' => AttributeTypeEnum::TEXT,
        'type' => AttributeTypeEnum::TEXT,
        'errorTargets' => AttributeTypeEnum::TEXT,
        'categories' => AttributeTypeEnum::TEXT,
        'folderId' => AttributeTypeEnum::NUMBER,
        'documentFolderId' => AttributeTypeEnum::NUMBER,
    ];

    public function __construct(
        private readonly DoctrineCache $doctrineCache,
        private readonly AttributeTypes $attributeTypes,
    ) {
    }

    /**
     * @phpstan-param FilterStructure $constraint
     */
    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$value instanceof Filter) {
            return;
        }

        // header is required
        if (null === $value->header) {
            $this->addViolation('property', 'filter_structure.header');

            return;
        }

        // property is localizable
        $isLocalizable = (
            FilterTypeEnum::ATTRIBUTE === $value->header->type
            && $this->doctrineCache->attributes->codeIsLocalizable($value->header->code)
        );
        if ($isLocalizable && null === $value->header->localeCode) {
            $this->addViolation('locale', 'filter_structure.localizable');

            return;
        }
        if (!$isLocalizable && null !== $value->header->localeCode) {
            $this->addViolation('locale', 'filter_structure.not_localizable');

            return;
        }

        // Check if attribute type is valid
        if (FilterTypeEnum::ATTRIBUTE === $value->header->type) {
            $attributeType = $this->doctrineCache->attributes->codeToType($value->header->code);

            if (!in_array($attributeType, AttributeTypeEnum::ALL, true)) {
                $this->addViolation('property', 'filter_structure.unsupported_type');

                return;
            }
        } elseif (FilterTypeEnum::PROPERTY === $value->header->type) {
            $attributeType = self::PROPERTIES[$value->header->code] ?? null;

            if (!in_array($attributeType, AttributeTypeEnum::ALL, true)) {
                $this->addViolation('property', 'filter_structure.unsupported_type');
            }

            return;
        } else {
            $this->addViolation('property', 'filter_structure.unsupported_type');

            return;
        }

        $operators = $this->attributeTypes->getOperatorsFor($attributeType);
        if (!in_array($value->operator, $operators, true)) {
            $this->addViolation('operator', 'filter_structure.unsupported_operator');

            return;
        }

        if (!$this->attributeTypes->supports($attributeType, $value->operator, $value->value)) {
            $this->addViolation('value', 'filter_structure.unsupported_operator_value');
        }
    }

    private function addViolation(string $path, string $violation): void
    {
        $this
            ->context
            ->buildViolation($violation)
            ->atPath($path)
            ->addViolation();
    }
}
