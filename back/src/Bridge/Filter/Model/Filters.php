<?php declare(strict_types=1);

namespace App\Bridge\Filter\Model;

use App\Bridge\Filter\Enum\CombineOperatorEnum;
use App\Utils\ArrUtils;
use Generator;
use Symfony\Component\Serializer\Attribute\Ignore;
use Symfony\Component\Validator\Constraints as Assert;
use function array_map;

final class Filters
{
    public const string TYPE = 'filters';

    /**
     * @param (Filter|self)[] $filters
     */
    public function __construct(
        #[Assert\Valid]
        public array $filters = [],
        #[Assert\NotNull]
        #[Assert\Choice(choices: CombineOperatorEnum::ALL)]
        public string $operator = CombineOperatorEnum::AND,
    ) {
        $this->operator = CombineOperatorEnum::clean($operator);
    }

    public function and(self $filters): self
    {
        return new self([
            $this,
            $filters,
        ]);
    }

    public static function merge(self $f1, self $f2): self
    {
        return new self([
            ...$f1->filters,
            ...$f2->filters,
        ]);
    }

    /** @return Generator<string> */
    #[Ignore]
    public function getAttributeCodes(): Generator
    {
        foreach ($this->filters as $filter) {
            if ($filter instanceof self) {
                yield from $filter->getAttributeCodes();
            } else {
                yield $filter->header->code;
            }
        }
    }

    /** @return Generator<string> */
    #[Ignore]
    public function getLocales(): Generator
    {
        foreach ($this->filters as $filter) {
            if ($filter instanceof self) {
                yield from $filter->getLocales();
            } else {
                yield $filter->header->localeCode;
            }
        }
    }

    /** @return Generator<string> */
    #[Ignore]
    public function getScopes(): Generator
    {
        foreach ($this->filters as $filter) {
            if ($filter instanceof self) {
                yield from $filter->getScopes();
            } else {
                yield $filter->header->scopeCode;
            }
        }
    }

    #[Ignore]
    public function getSignature(): string
    {
        // sort signature before to avoid ksort from ArrUtils::sign
        $signatures = array_map(
            static fn(Filter|Filters $filters): string => $filters->getSignature(),
            $this->filters,
        );
        sort($signatures);

        return ArrUtils::sign([
            'operator' => $this->operator,
            'signatures' => $signatures,
        ]);
    }
}
