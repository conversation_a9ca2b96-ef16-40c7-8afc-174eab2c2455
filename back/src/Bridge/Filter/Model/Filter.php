<?php declare(strict_types=1);

namespace App\Bridge\Filter\Model;

use App\Bridge\Filter\Constraints\FilterStructure;
use App\Bridge\Filter\Enum\OperatorEnum;
use App\Contracts\Model\Header;
use App\Utils\ArrUtils;
use Symfony\Component\Serializer\Attribute\Ignore;
use Symfony\Component\Validator\Constraints as Assert;

#[FilterStructure]
final class Filter
{
    public const string TYPE = 'filter';

    public function __construct(
        #[Assert\NotNull]
        public ?Header $header = null,
        #[Assert\Choice(choices: OperatorEnum::ALL)]
        public ?string $operator = null,
        public mixed $value = null,
    ) {
        $this->operator = OperatorEnum::clean($this->operator);
    }

    #[Ignore]
    public function getSignature(): string
    {
        return ArrUtils::sign([
            'header' => $this->header->toString(),
            'operator' => $this->operator,
            'value' => $this->value,
        ]);
    }
}
