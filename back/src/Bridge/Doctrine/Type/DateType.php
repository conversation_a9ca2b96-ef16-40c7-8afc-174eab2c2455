<?php declare(strict_types=1);

namespace App\Bridge\Doctrine\Type;

use App\Utils\DateUtils;
use DateTime;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\ConversionException;
use Doctrine\DBAL\Types\DateType as BaseDateType;
use Doctrine\DBAL\Types\Types;

class DateType extends BaseDateType
{
    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if ($value instanceof DateTime) {
            $value->setTimezone(DateUtils::timezone());
        }

        return parent::convertToDatabaseValue($value, $platform);
    }

    public function convertToPHPValue($value, AbstractPlatform $platform): ?DateTime
    {
        if (null === $value) {
            return null;
        }
        if ($value instanceof DateTime) {
            $value->setTimezone(DateUtils::timezone());

            return $value;
        }

        $converted = DateUtils::new($value);
        if (null === $converted) {
            throw ConversionException::conversionFailedFormat($value, Types::DATE_MUTABLE, $platform->getDateTimeFormatString());
        }

        return $converted;
    }
}
