<?php declare(strict_types=1);

namespace App\Bridge\Doctrine\Type;

use App\Utils\DateUtils;
use DateTime;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\ConversionException;
use Doctrine\DBAL\Types\DateTimeType as BaseDateTimeType;
use Doctrine\DBAL\Types\Types;

class DateTimeType extends BaseDateTimeType
{
    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if ($value instanceof DateTime) {
            $value->setTimezone(DateUtils::timezone());
        }

        return parent::convertToDatabaseValue($value, $platform);
    }

    public function convertToPHPValue($value, AbstractPlatform $platform): ?DateTime
    {
        if (null === $value) {
            return null;
        }
        if ($value instanceof DateTime) {
            $value->setTimezone(DateUtils::timezone());

            return $value;
        }

        $converted = DateUtils::new($value, $platform->getDateTimeFormatString());
        if (null === $converted) {
            throw ConversionException::conversionFailedFormat($value, Types::DATETIME_MUTABLE, $platform->getDateTimeFormatString());
        }

        return $converted;
    }
}
