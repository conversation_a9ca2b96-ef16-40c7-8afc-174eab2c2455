<?php declare(strict_types=1);

namespace App\Bridge\Counter\Enum;

use App\Bridge\Counter\Entity as Counters;
use App\Contracts\Enum\ApiTypeEnum;

final readonly class CountersEnum
{
    public const array COUNTERS = [
        ApiTypeEnum::ATTRIBUTE => Counters\AttributeCounters::class,
        ApiTypeEnum::ATTRIBUTE_GROUP => Counters\AttributeGroupCounters::class,
        ApiTypeEnum::ATTRIBUTE_OPTION => Counters\AttributeOptionCounters::class,
        ApiTypeEnum::CATALOG => Counters\CatalogCounters::class,
        ApiTypeEnum::CATEGORY => Counters\CategoryCounters::class,
        ApiTypeEnum::CONTENT_FOLDER => Counters\ContentFolderCounters::class,
        ApiTypeEnum::DICTIONARY => Counters\DictionaryCounters::class,
        ApiTypeEnum::HUB_SOURCE => Counters\HubSourceCounters::class,
        ApiTypeEnum::MEDIA_FOLDER => Counters\MediaFolderCounters::class,
        ApiTypeEnum::RULE_GROUP => Counters\RuleGroupCounters::class,
        ApiTypeEnum::TEMPLATE => Counters\TemplateCounters::class,
        ApiTypeEnum::USER_GROUP => Counters\UserGroupCounters::class,
        ApiTypeEnum::WORKFLOW => Counters\WorkflowCounters::class,
        ApiTypeEnum::WORKFLOW_STEP => Counters\WorkflowStepCounters::class,
    ];
}
