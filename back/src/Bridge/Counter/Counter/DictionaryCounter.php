<?php declare(strict_types=1);

namespace App\Bridge\Counter\Counter;

use App\Bridge\Counter\Model\SqlCounterBuilder;
use App\Contracts\Enum\ApiTypeEnum;
use App\Contracts\Enum\TableEnum;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

final readonly class DictionaryCounter implements CounterInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private LoggerInterface $logger,
    ) {
    }

    public static function getType(): string
    {
        return ApiTypeEnum::DICTIONARY;
    }

    public function executeSync(array $identifiers = []): void
    {
        SqlCounterBuilder::create($this->em, $this->logger, TableEnum::DICTIONARY, 'code', $identifiers)
            ->add(
                targetColumn: 'dictionary_code',
                counterColumn: 'counters_mappings',
                counterTable: TableEnum::DICTIONARY_MAPPING,
            )
            ->execute();
    }
}
