<?php declare(strict_types=1);

namespace App\Bridge\Catalog\Solver;

use App\Bridge\Catalog\Solver\Model\SolverCatalog;
use App\Bridge\Catalog\Solver\Model\SolverCategory;
use App\Bridge\Filter\Enum\CombineOperatorEnum;
use App\Bridge\Filter\Enum\OperatorEnum;
use App\Bridge\Filter\Model\Filter;
use App\Bridge\Filter\Model\Filters;
use App\Contracts\Model\Header;
use App\Contracts\Serializer\OdmSerializer;
use App\Entity\Catalog;
use App\Entity\Category;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Uid\Uuid;

final class CatalogSolverCache
{
    /** @var SolverCatalog[]|null */
    private ?array $catalogsFilters = null;

    /** @var SolverCategory[][]|null */
    private ?array $categoriesFilters = null;

    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly OdmSerializer $odmSerializer,
    ) {
    }

    /** @return array<string, SolverCatalog> */
    public function getAllCatalogFilters(): array
    {
        $this->loadCatalogFilters();

        return $this->catalogsFilters;
    }

    public function getCatalogFilters(string $catalogCode): ?SolverCatalog
    {
        $this->loadCatalogFilters();

        return $this->catalogsFilters[$catalogCode] ?? null;
    }

    /** @return SolverCategory[] */
    public function getAllCategoryFilters(string $catalogCode): array
    {
        $this->loadCatalogFilters();
        $this->loadCategoriesFilters();

        return $this->categoriesFilters[$catalogCode] ?? [];
    }

    private function loadCatalogFilters(): void
    {
        if (null === $this->catalogsFilters) {
            $this->catalogsFilters = [];
            foreach ($this->em->getRepository(Catalog::class)->getCatalogWithFilters() as $row) {
                $filters = $this->odmSerializer->deserialize($row['filters'], Filters::class);

                $filters = new Filters([
                    new Filter(header: Header::property('type'), operator: OperatorEnum::EQ, value: $row['type']),
                    $filters,
                ], CombineOperatorEnum::AND);

                $this->catalogsFilters[$row['code']] = new SolverCatalog(
                    $row['code'],
                    $filters,
                );
            }
        }
    }

    private function loadCategoriesFilters(): void
    {
        if (null === $this->categoriesFilters) {
            $this->categoriesFilters = [];
            foreach ($this->em->getRepository(Category::class)->getCategoryWithFilters() as $row) {
                $catalogCode = $row['catalog_code'];

                $this->categoriesFilters[$catalogCode] ??= [];
                $this->categoriesFilters[$catalogCode][$row['uuid']] = new SolverCategory(
                    Uuid::fromBinary($row['uuid']),
                    $row['breadcrumb_ids'],
                    $this->odmSerializer->deserialize($row['filters'], Filters::class),
                );
            }
        }
    }
}
