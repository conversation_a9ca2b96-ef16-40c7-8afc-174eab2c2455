<?php declare(strict_types=1);

namespace App\Bridge\Catalog\Solver;

use App\Api\Model\Uuids;
use App\Bridge\Catalog\Solver\Model\SolverCategory;
use App\Bridge\Filter\Enum\CombineOperatorEnum;
use App\Bridge\Filter\Enum\FilterTypeEnum;
use App\Bridge\Filter\Enum\OperatorEnum;
use App\Bridge\Filter\Model\Filter;
use App\Bridge\Filter\Model\Filters;
use App\Bridge\Flat\Model\Values;
use App\Contracts\Enum\LoggerChannelEnum;
use App\Contracts\Type\Enum\AttributeTypeEnum;
use Generator;
use Monolog\Attribute\WithMonologChannel;
use Psr\Log\LoggerInterface;
use Symfony\Component\Uid\Uuid;
use Throwable;
use function count;
use function in_array;
use function is_array;
use function is_string;

#[WithMonologChannel(LoggerChannelEnum::CATALOG)]
final readonly class CatalogSolver
{
    public function __construct(
        private CatalogSolverCache $catalogSolverCache,
        private LoggerInterface $logger,
    ) {
    }

    public function getMatchingCatalogCodes(Values $values, array $documentProperties = []): array
    {
        $codes = [];
        foreach ($this->catalogSolverCache->getAllCatalogFilters() as $catalog) {
            if ($this->filtersAreMatching($values, $documentProperties, $catalog->filters)) {
                $codes[] = $catalog->code;
            }
        }

        return $codes;
    }

    public function getMatchingCategoriesUuids(array $catalogCodes, Values $values, array $documentProperties = []): Uuids
    {
        $uuids = new Uuids();
        foreach ($catalogCodes as $catalogCode) {
            if (null !== $catalog = $this->catalogSolverCache->getCatalogFilters($catalogCode)) {
                $categories = $this->catalogSolverCache->getAllCategoryFilters($catalogCode);
                foreach ($this->build($categories, $catalog->filters, "/{$catalogCode}/") as $id => $categoryFilters) {
                    if ($this->filtersAreMatching($values, $documentProperties, $categoryFilters)) {
                        $uuids->add(Uuid::fromRfc4122($id));
                    }
                }
            }
        }

        return $uuids;
    }

    /**
     * @param SolverCategory[] $categories
     * @return Generator<string, Filters>
     */
    private function build(array $categories, Filters $filters, string $trail): Generator
    {
        foreach ($categories as $category) {
            if ($category->trail === $trail) {
                $moreFilters = $filters->and($category->filters);

                yield $category->uuid->toRfc4122() => $moreFilters;

                yield from $this->build(
                    $categories,
                    $moreFilters,
                    "{$trail}{$category->uuid->toRfc4122()}/",
                );
            }
        }
    }

    private function filtersAreMatching(Values $values, array $documentProperties, Filters $filters): bool
    {
        foreach ($filters->filters as $filter) {
            if ($filter instanceof Filter) {
                if (!$this->filterIsMatching($values, $documentProperties, $filter)) {
                    return false;
                }
            } elseif ($filter instanceof Filters) {
                if (CombineOperatorEnum::AND === $filter->operator) {
                    if (!$this->filtersAreMatching($values, $documentProperties, $filter)) {
                        return false;
                    }
                } elseif (CombineOperatorEnum::OR === $filter->operator) {
                    if (!$this->oneFiltersIsMatching($values, $documentProperties, $filter)) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    private function oneFiltersIsMatching(Values $values, array $documentProperties, Filters $filters): bool
    {
        foreach ($filters->filters as $filter) {
            if ($filter instanceof Filter) {
                if ($this->filterIsMatching($values, $documentProperties, $filter)) {
                    return true;
                }
            } elseif ($filter instanceof Filters) {
                if (CombineOperatorEnum::AND === $filter->operator) {
                    if ($this->filtersAreMatching($values, $documentProperties, $filter)) {
                        return true;
                    }
                } elseif (CombineOperatorEnum::OR === $filter->operator) {
                    return $this->oneFiltersIsMatching($values, $documentProperties, $filter);
                }
            }
        }

        return false;
    }

    private function filterIsMatching(Values $values, array $documentProperties, Filter $filter): bool
    {
        if (FilterTypeEnum::ATTRIBUTE === $filter->header->type) {
            if (OperatorEnum::EXISTS === $filter->operator) {
                return $this->filterIsMatchingExists($values, $filter);
            }

            return $this->filterIsMatchingValues($values, $filter);
        } elseif (FilterTypeEnum::PROPERTY === $filter->header->type) {
            return $this->filterIsMatchingProperty($documentProperties, $filter);
        }

        return false;
    }

    private function filterIsMatchingExists(Values $values, Filter $filter): bool
    {
        if (false !== $filter->value) {
            return null !== $values->locate($filter->header);
        }

        return null === $values->locate($filter->header);
    }

    private function filterIsMatchingValues(Values $values, Filter $filter): bool
    {
        try {
            $data = $values->locate($filter->header);

            if (null === $data) {
                return false;
            }

            $isMatching = match ($filter->operator) {
                OperatorEnum::EQ => (string)$data->data === (string)$filter->value,
                OperatorEnum::NEQ => (string)$data->data !== (string)$filter->value,
                OperatorEnum::IN => (is_array($filter->value) && in_array($data->data, $filter->value, true)) || AttributeTypeEnum::MULTISELECT_ALL_VALUE === $data->data,
                OperatorEnum::INTERSECT_LEAST_ONE => (is_array($filter->value) && (0 < count(array_intersect($filter->value, $data->data)))) || AttributeTypeEnum::MULTISELECT_ALL_VALUE === $data->data,
                OperatorEnum::LIKE => is_string($data->data) && is_string($filter->value) && str_contains($data->data, $filter->value),
                OperatorEnum::STARTS_WITH => is_string($data->data) && is_string($filter->value) && str_starts_with($data->data, $filter->value),
                OperatorEnum::ENDS_WITH => is_string($data->data) && is_string($filter->value) && str_ends_with($data->data, $filter->value),
                OperatorEnum::GT => $data->data > $filter->value,
                OperatorEnum::GTE => $data->data >= $filter->value,
                OperatorEnum::LT => $data->data < $filter->value,
                OperatorEnum::LTE => $data->data <= $filter->value,
                OperatorEnum::CONTAINS => in_array($filter->value, $data->data, true) || in_array(AttributeTypeEnum::MULTISELECT_ALL_VALUE, $data->data, true),
                OperatorEnum::EXCLUDES => !in_array($filter->value, $data->data, true) && !in_array(AttributeTypeEnum::MULTISELECT_ALL_VALUE, $data->data, true),
                default => false,
            };

            if ($isMatching) {
                return true;
            }
        } catch (Throwable $e) {
            $this->logger->error('cannot guess if filters are matching', [$e]);
        }

        return false;
    }

    private function filterIsMatchingProperty(array $documentProperties, Filter $filter): bool
    {
        if (!isset($documentProperties[$filter->header->code])) {
            return false;
        }

        $value = $documentProperties[$filter->header->code];

        return match ($filter->operator) {
            OperatorEnum::EQ => (string)$value === (string)$filter->value,
            OperatorEnum::NEQ => (string)$value !== (string)$filter->value,
            OperatorEnum::IN => (is_array($filter->value) && in_array($value, $filter->value, true)) || AttributeTypeEnum::MULTISELECT_ALL_VALUE === $value,
            OperatorEnum::LIKE => is_string($value) && is_string($filter->value) && str_contains($value, $filter->value),
            OperatorEnum::STARTS_WITH => is_string($value) && is_string($filter->value) && str_starts_with($value, $filter->value),
            OperatorEnum::ENDS_WITH => is_string($value) && is_string($filter->value) && str_ends_with($value, $filter->value),
            OperatorEnum::GT => $value > $filter->value,
            OperatorEnum::GTE => $value >= $filter->value,
            OperatorEnum::LT => $value < $filter->value,
            OperatorEnum::LTE => $value <= $filter->value,
            OperatorEnum::CONTAINS => in_array($filter->value, $value, true) || in_array(AttributeTypeEnum::MULTISELECT_ALL_VALUE, $value, true),
            OperatorEnum::EXCLUDES => !in_array($filter->value, $value, true) && !in_array(AttributeTypeEnum::MULTISELECT_ALL_VALUE, $value, true),
            default => false,
        };
    }
}
