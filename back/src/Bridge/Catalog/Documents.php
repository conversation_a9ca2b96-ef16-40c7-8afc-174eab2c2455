<?php declare(strict_types=1);

namespace App\Bridge\Catalog;

use App\Api\Model\FilterHelper;
use App\Bridge\Elastic\Index\DocumentIndex;
use App\Bridge\Elastic\Index\ErrorIndex;
use App\Bridge\Elastic\Model\ElasticHits;
use App\Bridge\Filter\Enum\OperatorEnum;
use App\Bridge\Filter\Model\Filter;
use App\Bridge\Filter\Model\Filters;
use App\Contracts\Enum\ApiTypeEnum;
use App\Contracts\Model\Header;
use function in_array;

final readonly class Documents
{
    public function __construct(
        private DocumentIndex $documentIndex,
        private ErrorIndex $errorIndex,
    ) {
    }

    public function getQueryFilters(?string $type, FilterHelper $filter): Filters
    {
        $filters = new Filters();

        $type ??= $filter->get('type');
        if (null !== $type) {
            $filters->filters[] = new Filter(Header::property('type'), OperatorEnum::EQ, $type);
            if (in_array($type, [ApiTypeEnum::CONTENT, ApiTypeEnum::MEDIA], true)) {
                if (null !== $folderId = $filter->getId('folder')) {
                    $filters->filters[] = new Filter(Header::property('folderId'), OperatorEnum::EQ, $folderId);
                }
                if (null !== $folderExists = $filter->getExists('folder')) {
                    $filters->filters[] = new Filter(Header::property('folderId'), OperatorEnum::EXISTS, $folderExists);
                }
            }
            if (null !== $categoryUuid = $filter->getUuid('category')) {
                $filters->filters[] = new Filter(Header::property('categories'), OperatorEnum::CONTAINS, $categoryUuid->toRfc4122());
            }
        }
        if (null !== $q = $filter->get('q')) {
            $filters->filters[] = new Filter(Header::property('q'), OperatorEnum::LIKE, $q);
        }
        if (null !== $name = $filter->get('name')) {
            $filters->filters[] = new Filter(Header::property('name'), OperatorEnum::LIKE, $name);
        }
        if (null !== $status = $filter->getBool('status')) {
            $filters->filters[] = new Filter(Header::property('status'), OperatorEnum::EQ, $status);
        }
        if (null !== $catalogCodes = $filter->getArray('catalog')) {
            $filters->filters[] = new Filter(Header::property('catalogs'), OperatorEnum::IN, $catalogCodes);
        }
        if (null !== $catalogExists = $filter->getExists('catalog')) {
            $filters->filters[] = new Filter(Header::property('catalogs'), OperatorEnum::EXISTS, $catalogExists);
        }
        if (null !== $categoryExists = $filter->getExists('category')) {
            $filters->filters[] = new Filter(Header::property('categories'), OperatorEnum::EXISTS, $categoryExists);
        }
        if (null !== $uuids = $filter->getArray('uuid')) {
            $filters->filters[] = new Filter(Header::property('uuid'), OperatorEnum::IN, $uuids);
        }
        if (null !== $skus = $filter->getArray('sku')) {
            $filters->filters[] = new Filter(Header::property('sku'), OperatorEnum::IN, $skus);
        }

        return $filters;
    }

    public function findDocumentHits(
        Filters $filters,
        int $offset = 0,
        int $limit = 10_000, // @todo TO FIX OUR STUPIDNESS when solving cata/cate
        ?array $orders = null,
    ): ElasticHits {
        $builder = $this->documentIndex->createBuilder();

        $builder->handleFilters($filters); // @todo handle type and categories filters

        // order
        if (null !== $orders) {
            foreach ($orders as $field => $direction) {
                $builder->addOrder($field, $direction);
            }
        }

        // pagination
        $builder->addPagination($offset, $limit);

        return $builder->findHits();
    }

    public function findErrorHits(
        Filters $filters,
        int $offset,
        ?int $limit = null,
        ?array $orders = null,
        bool $withSource = false,
    ): ElasticHits {
        $builder = $this->errorIndex->createBuilder();

        // add custom filters
        $builder->handleFilters($filters);

        // order
        if (null !== $orders) {
            foreach ($orders as $field => $direction) {
                $builder->addOrder($field, $direction);
            }
        }

        // pagination
        if (null !== $limit) {
            $builder->addPagination($offset, $limit);
        }

        $builder->withSource($withSource);

        return $builder->findHits();
    }
}
