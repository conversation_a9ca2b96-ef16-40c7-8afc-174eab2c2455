<?php declare(strict_types=1);

namespace App\Bridge\Elastic\Index;

use App\Bridge\Elastic\Elastic;
use App\Bridge\Elastic\Index\Normalizer\Normalizers;
use App\Bridge\Elastic\Model\ElasticQueryBuilder;
use App\Bridge\Filter\Enum\OperatorEnum;
use App\Bridge\Filter\Model\Filter;
use App\Bridge\Filter\Model\Filters;
use App\Contracts\Model\Header;
use App\Module\Cache\DoctrineCache;
use Symfony\Component\Messenger\MessageBusInterface;
use function count;

abstract class AbstractIndex implements IndexInterface
{
    public function __construct(
        protected Elastic $elastic,
        protected Normalizers $normalizers,
        protected MessageBusInterface $bus,
        protected DoctrineCache $doctrineCache,
    ) {
    }

    public function getName(): string
    {
        return $this->elastic->prefixIndex($this->getType());
    }

    public function fixFieldName(string $field): string
    {
        return $field;
    }

    public function createBuilder(): ElasticQueryBuilder
    {
        return new ElasticQueryBuilder(
            $this,
            $this->doctrineCache,
            static::getMappings(),
        );
    }

    public function isProperty(string $name): bool
    {
        return isset(static::getMappings()['properties'][$name]);
    }

    public function updateMapping(array $mapping): bool
    {
        return $this->elastic->updateMapping($this->getName(), $mapping);
    }

    public function exists(): bool
    {
        return $this->elastic->existIndex($this->getName());
    }

    public function delete(): bool
    {
        return $this->elastic->deleteIndex($this->getName());
    }

    public function create(): bool
    {
        return $this->elastic->createIndex($this->getName(), static::getMappings());
    }

    public function countDocuments(array $query): int
    {
        return $this->elastic->count($this->getName(), $query);
    }

    public function researchDocuments(array $query): array
    {
        return $this->elastic->research($this->getName(), $query);
    }

    public function deleteDocumentsByQuery(array $query, bool $now = false): bool
    {
        return $this->elastic->deleteByQuery($this->getName(), $query, $now);
    }

    public function deleteDocuments(array $identifiers, bool $now = false): bool
    {
        if (0 < count($identifiers)) {
            return $this
                ->createBuilder()
                ->handleFilters(new Filters([
                    new Filter(
                        header: Header::property($this->getIdentifier()),
                        operator: OperatorEnum::IN,
                        value: $identifiers,
                    ),
                ]))
                ->deleteHits($now);
        }

        return true;
    }

    protected function createDocuments(array $documents, bool $now = false): void
    {
        $this->elastic->createDocuments($this->getName(), $documents, $now);
    }
}
