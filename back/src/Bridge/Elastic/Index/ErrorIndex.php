<?php declare(strict_types=1);

namespace App\Bridge\Elastic\Index;

use App\Api\Model\Uuids;
use App\Bridge\Elastic\Elastic;
use App\Bridge\Elastic\Enum\IndexEnum;
use App\Bridge\Elastic\Message\IndexErrorMessage;
use App\Bridge\Elastic\Model\ElasticError;
use App\Bridge\Filter\Enum\OperatorEnum;
use App\Bridge\Filter\Model\Filter;
use App\Bridge\Filter\Model\Filters;
use App\Contracts\Enum\ApiTypeEnum;
use App\Contracts\Model\Header;
use function array_map;

final class ErrorIndex extends AbstractIndex
{
    public function getType(): string
    {
        return IndexEnum::ERROR;
    }

    public function getIdentifier(): string
    {
        return 'id';
    }

    public function fixFieldName(string $field): string
    {
        return match ($field) {
            'uuid' => 'documentUuid',
            'sku' => 'documentSku',
            'name' => 'documentName',
            'identifier' => 'documentIdentifier',
            'folderId' => 'documentFolderId',
            'catalogScope' => 'catalogScopeCode',
            'catalog' => 'catalogCode',
            'scope' => 'scopeCode',
            'locale' => 'localeCode',
            'workflow' => 'workflowId',
            'workflowStep' => 'workflowStepId',
            'rule' => 'ruleCode',
            'ruleGroup' => 'ruleGroupCode',
            'level' => 'ruleLevel',
            default => $field,
        };
    }

    public function supports(string $type): bool
    {
        return ApiTypeEnum::PRODUCT_ERROR === $type;
    }

    public static function getMappings(): array
    {
        return [
            'dynamic' => false,
            'dynamic_date_formats' => [
                'strict_date_time_no_millis',
            ],
            'properties' => [
                'q' => [
                    'type' => 'text',
                    'analyzer' => Elastic::CUSTOM_ANALYSER_KEY,
                ],
                'id' => [
                    'type' => 'integer',
                ],
                'documentType' => [
                    'type' => 'keyword',
                ],
                'documentUuid' => [
                    'type' => 'keyword',
                    'copy_to' => 'q',
                ],
                'documentSku' => [
                    'type' => 'keyword',
                    'copy_to' => 'q',
                ],
                'documentName' => [
                    'type' => 'keyword',
                    'copy_to' => 'q',
                ],
                'documentFolderId' => [
                    'type' => 'integer',
                ],
                'catalogScopeCode' => [
                    'type' => 'keyword',
                ],
                'catalogCode' => [
                    'type' => 'keyword',
                ],
                'scopeCode' => [
                    'type' => 'keyword',
                ],
                'localeCode' => [
                    'type' => 'keyword',
                ],
                'workflowId' => [
                    'type' => 'integer',
                ],
                'workflowStepId' => [
                    'type' => 'integer',
                ],
                'ruleCode' => [
                    'type' => 'keyword',
                ],
                'ruleGroupCode' => [
                    'type' => 'keyword',
                ],
                'ruleLevel' => [
                    'type' => 'keyword',
                ],
                'type' => [
                    'type' => 'keyword',
                ],
                // @todo shall we rename this? bit confusing with "target" entity -> IN "ruleErrorTargets" maybe?! :D
                'errorTargets' => [
                    'type' => 'keyword',
                ],
                'createdAt' => [
                    'type' => 'date',
                    'format' => 'strict_date_time_no_millis||epoch_millis',
                ],
                'updatedAt' => [
                    'type' => 'date',
                    'format' => 'strict_date_time_no_millis||epoch_millis',
                ],
                'values' => DocumentIndex::getMappings()['properties']['values'],
            ],
        ];
    }

    public function index(bool $now = false): void
    {
        $this->bus->dispatch(new IndexErrorMessage()->now($now));
    }

    /**
     * @param ElasticError[] $errors
     */
    public function indexDocuments(array $errors, bool $now = false): void
    {
        $this->createDocuments(array_map(
            $this->normalizers->errorNormalizer->normalize(...),
            $errors,
        ), $now);
    }

    public function deleteAllErrorsLinkedToDocuments(Uuids $uuids, array $errorsToKeep, bool $now = false): bool
    {
        if ($uuids->isEmpty()) {
            return true;
        }

        return $this
            ->createBuilder()
            ->handleFilters(new Filters([
                new Filter(
                    header: Header::property('documentUuid'),
                    operator: OperatorEnum::IN,
                    value: $uuids->toRfc4122s(),
                ),
                new Filter(
                    header: Header::property('id'),
                    operator: OperatorEnum::NOT_IN,
                    value: $errorsToKeep,
                ),
            ]))
            ->deleteHits($now);
    }
}
