<?php declare(strict_types=1);

namespace App\Bridge\Elastic\Index\Synchronizer;

use App\Api\Model\Uuids;
use App\Bridge\Elastic\Index\ErrorIndex;
use App\Bridge\Elastic\Index\IndexInterface;
use App\Bridge\Elastic\Message\IndexErrorMessage;
use App\Bridge\Elastic\Model\ElasticError;
use App\Bridge\Filter\Enum\OperatorEnum;
use App\Bridge\Filter\Model\Filter;
use App\Bridge\Filter\Model\Filters;
use App\Bridge\Flat\Model\Values;
use App\Contracts\Enum\DocumentMapping;
use App\Contracts\Enum\TableEnum;
use App\Contracts\Model\Header;
use App\Contracts\Serializer\OdmSerializer;
use App\Contracts\SqlBuilderFactory;
use App\Utils\DateUtils;
use App\Utils\JsonUtils;
use App\Utils\SqlUtils;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Uid\Uuid;
use Throwable;
use function in_array;

final readonly class ErrorSynchronizer implements SynchronizerInterface
{
    public function __construct(
        private MessageBusInterface $bus,
        private SqlBuilderFactory $sqlBuilderFactory,
        private ErrorIndex $errorIndex,
        private EntityManagerInterface $em,
        protected OdmSerializer $odmSerializer,
        private LoggerInterface $logger,
    ) {
    }

    public function getIndex(): IndexInterface
    {
        return $this->errorIndex;
    }

    public function sync(
        ?string $type = null,
        ?Uuids $uuids = null,
        bool $now = false,
    ): void {
        foreach (DocumentMapping::TYPES as $expectedType => ['table' => $table]) {
            if (null === $type || $type === $expectedType) {
                if (null === $uuids) {
                    foreach ($this->sqlBuilderFactory->createSqlUuidsBuilder($table)->getUuidsByBulk() as $bulkedUuids) {
                        $this->bus->dispatch(new IndexErrorMessage(
                            type: $expectedType,
                            uuids: $bulkedUuids,
                        )->now($now));
                    }
                } else {
                    $this->index($expectedType, $uuids, $now);
                }
            }
        }
    }

    private function index(
        string $type,
        Uuids $uuids,
        bool $now = false,
    ): void {
        if ($uuids->isEmpty()) {
            return;
        }

        try {
            [
                'table' => $table,
                'sku_column' => $skuColumn,
                'name_column' => $nameColumn,
                'folder_id_column' => $folderIdColumn,
                'error_table' => $errorTable,
                'error_document_column' => $errorDocumentColumn,
            ] = DocumentMapping::TYPES[$type];

            $skuColumn = null === $skuColumn ? 'NULL' : "d.{$skuColumn}";
            $nameColumn = null === $nameColumn ? 'NULL' : "d.{$nameColumn}";
            $folderIdColumn = null === $folderIdColumn ? 'NULL' : "d.{$folderIdColumn}";

            $uuidsPlaceholder = SqlUtils::createPlaceholders($uuids->count());

            $tableCatalogScope = TableEnum::CATALOG_SCOPE;
            $tableRule = TableEnum::RULE;

            $currentIndexedErrors = $this->errorIndex->createBuilder()->handleFilters(
                new Filters([new Filter(
                    header: Header::property('documentUuid'),
                    operator: OperatorEnum::IN,
                    value: $uuids->toRfc4122s(),
                )])
            )->addPagination(0, 20_000)->findHits(); // @todo pagination is not very well done....

            $query = <<<SQL
            SELECT
                e.id,
                e.{$errorDocumentColumn} AS uuid,
                {$skuColumn} AS document_sku,
                {$nameColumn} AS document_name,
                {$folderIdColumn} AS document_folder_id,
                e.catalog_scope_code,
                cs.catalog_code,
                cs.scope_code,
                e.locale_code,
                cs.workflow_id,
                e.step_id,
                e.rule_code,
                r.rule_group_code,
                r.level AS rule_level,
                e.type,
                r.error_targets,
                e.created_at,
                e.updated_at,
                d.data_values
            FROM {$errorTable} e
            INNER JOIN {$table} d ON d.uuid = e.{$errorDocumentColumn}
            INNER JOIN {$tableCatalogScope} cs ON cs.code = e.catalog_scope_code
            INNER JOIN {$tableRule} r ON r.code = e.rule_code
            WHERE e.{$errorDocumentColumn} IN ({$uuidsPlaceholder})
            SQL;

            $errors = [];

            $results = $this->em->getConnection()->executeQuery($query, $uuids->toBinaries());
            $errorsToKeep = [];
            while (false !== $row = $results->fetchAssociative()) {
                if (in_array($row['id'], $currentIndexedErrors->identifiers)) {
                    $errorsToKeep[] = $row['id'];
                    continue;
                }

                $errors[] = new ElasticError(
                    id: $row['id'],
                    documentType: $type,
                    documentUuid: Uuid::fromBinary($row['uuid']),
                    documentSku: $row['document_sku'],
                    documentName: $row['document_name'],
                    documentFolderId: $row['document_folder_id'],
                    catalogScopeCode: $row['catalog_scope_code'],
                    catalogCode: $row['catalog_code'],
                    scopeCode: $row['scope_code'],
                    localeCode: $row['locale_code'],
                    workflowId: $row['workflow_id'],
                    workflowStepId: $row['step_id'],
                    ruleCode: $row['rule_code'],
                    ruleGroupCode: $row['rule_group_code'],
                    ruleLevel: $row['rule_level'],
                    type: $row['type'],
                    errorTargets: JsonUtils::decode($row['error_targets']),
                    createdAt: DateUtils::fromDatabase($row['created_at']),
                    updatedAt: DateUtils::fromDatabase($row['updated_at']),
                    values: $this->odmSerializer->deserialize($row['data_values'], Values::class),
                );
            }

            $this->errorIndex->deleteAllErrorsLinkedToDocuments($uuids, $errorsToKeep, $now);

            $this->errorIndex->indexDocuments($errors, $now);
        } catch (Throwable $e) {
            $this->logger->error('unable to index errors', [
                'type' => $type,
                $e,
            ]);
        }
    }
}
