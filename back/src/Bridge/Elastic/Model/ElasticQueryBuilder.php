<?php declare(strict_types=1);

namespace App\Bridge\Elastic\Model;

use App\Bridge\Elastic\Index\IndexInterface;
use App\Bridge\Elastic\Index\Normalizer\ValuesNormalizer;
use App\Bridge\Elastic\Utils\IndexUtils;
use App\Bridge\Filter\Enum\CombineOperatorEnum;
use App\Bridge\Filter\Enum\FilterTypeEnum;
use App\Bridge\Filter\Enum\OperatorEnum;
use App\Bridge\Filter\Model\Filter;
use App\Bridge\Filter\Model\Filters;
use App\Module\Cache\DoctrineCache;
use Generator;
use function count;
use function in_array;
use function is_array;
use function mb_strtolower;
use function str_starts_with;

final class ElasticQueryBuilder
{
    private const array DEFAULT_BODY = [
        // @todo that's really bad for performances!
        'track_total_hits' => true,
        '_source' => false,
    ];

    private ?int $offset = null;

    private ?int $itemPerPage = null;

    private array $query = [];

    private array $orders = [];

    private ?bool $source = null;

    public function __construct(
        private readonly IndexInterface $index,
        private readonly DoctrineCache $doctrineCache,
        private readonly array $mapping,
    ) {
    }

    public function addPagination(int $offset, int $itemPerPage): self
    {
        $this->offset = $offset;
        $this->itemPerPage = $itemPerPage;

        return $this;
    }

    public function addOrder(string $field, string $direction = 'asc'): self
    {
        $field = $this->index->fixFieldName($field);
        if (!$this->index->isProperty($field) && !str_starts_with($field, 'values.')) {
            $field = "values.{$field}";
        }

        $this->orders[] = [$field => $direction];

        return $this;
    }

    public function withSource(bool $source): self
    {
        $this->source = $source;

        return $this;
    }

    public function handleFilters(Filters $filters): self
    {
        $this->query = $this->filtersToQuery($filters);

        return $this;
    }

    public function findHits(): ElasticHits
    {
        return ElasticHits::fromArray($this->index->researchDocuments($this->toArray()));
    }

    public function countHits(): int
    {
        return $this->index->countDocuments($this->toArray([]));
    }

    public function deleteHits(bool $now = false): bool
    {
        return $this->index->deleteDocumentsByQuery($this->toArray(), $now);
    }

    private function addExistsFilter(Filter $filter): array
    {
        $occurrence = true === $filter->value ? 'must' : 'must_not';

        $nested = [];
        $nested['must'][] = ['exists' => ['field' => 'values.code']];
        $nested['must'][] = ['term' => ['values.code' => $filter->header->code]];

        return [
            $occurrence => [
                'nested' => [
                    'path' => 'values',
                    'score_mode' => 'none',
                    'query' => [
                        'bool' => $nested,
                    ],
                ],
            ],
        ];
    }

    private function addValuesFilter(
        string $type,
        Filter $filter,
    ): array {
        $nested = [];

        $nested['must'] = [];
        $nested['must'][] = ['exists' => ['field' => 'values.code']];
        $nested['must'][] = ['term' => ['values.code' => $filter->header->code]];

        if (null === $filter->header->localeCode) {
            $nested['must_not'] = [];
            $nested['must_not'][] = ['exists' => ['field' => 'values.locale']];
        } else {
            $nested['must'][] = ['exists' => ['field' => 'values.locale']];
            $nested['must'][] = ['term' => ['values.locale' => $filter->header->localeCode]];
        }

        if (null === $filter->header->scopeCode) {
            $nested['must_not'] = [];
            $nested['must_not'][] = ['exists' => ['field' => 'values.scope']];
        } else {
            $nested['must'][] = ['exists' => ['field' => 'values.scope']];
            $nested['must'][] = ['term' => ['values.scope' => $filter->header->scopeCode]];
        }

        // @todo product specific stuff should not be here
        $property = ValuesNormalizer::typeToProperty($type);
        foreach ($this->createFilters($filter, "values.{$property}") as $type => $filter) {
            $nested[$type] ??= [];
            $nested[$type][] = $filter;
        }

        return [
            'must' => [
                'nested' => [
                    'path' => 'values',
                    'score_mode' => 'none',
                    'query' => [
                        'bool' => $nested,
                    ],
                ],
            ],
        ];
    }

    private function createFilters(Filter $filter, ?string $property = null): Generator
    {
        $property ??= $filter->header->code;
        $property = $this->index->fixFieldName($property);

        $hasAnalyzer = false;
        $propertyAnalyzer = [];
        $propertyMapping = $this->mapping['properties'][$property] ?? null;
        if (is_array($propertyMapping) && isset($propertyMapping['analyzer'])) {
            $hasAnalyzer = true;
            $propertyAnalyzer = ['analyzer' => $propertyMapping['analyzer']];
        }

        // search everywhere
        if (OperatorEnum::EXISTS === $filter->operator) {
            yield $filter->value ? 'must' : 'must_not' => [
                'exists' => [
                    'field' => $property,
                ],
            ];
        } elseif (
            OperatorEnum::EQ === $filter->operator
            || OperatorEnum::NEQ === $filter->operator
        ) {
            yield OperatorEnum::NEQ === $filter->operator ? 'must_not' : 'must' => [
                'match' => [
                    $property => [
                        'query' => $filter->value,
                        ...$propertyAnalyzer,
                        'operator' => 'AND',
                    ],
                ],
            ];
        } elseif (in_array($filter->operator, [OperatorEnum::IN, OperatorEnum::INTERSECT_LEAST_ONE])) {
            yield 'must' => [
                'terms' => [
                    $property => $filter->value,
                ],
            ];
        } elseif (OperatorEnum::NOT_IN === $filter->operator) {
            yield 'must_not' => [
                'terms' => [
                    $property => $filter->value,
                ],
            ];
        } elseif (OperatorEnum::STARTS_WITH === $filter->operator) {
            if ($hasAnalyzer) {
                yield 'must' => [
                    'match_phrase_prefix' => [
                        $property => [
                            'query' => $filter->value,
                            ...$propertyAnalyzer,
                        ],
                    ],
                ];
            } else {
                yield 'must' => [
                    'prefix' => [
                        $property => [
                            'value' => $filter->value,
                            'case_insensitive' => true,
                        ],
                    ],
                ];
            }
        } elseif (OperatorEnum::ENDS_WITH === $filter->operator) {
            yield 'must' => [
                'wildcard' => [
                    $property => [
                        'value' => "*{$filter->value}",
                        'case_insensitive' => true,
                    ],
                ],
            ];
        } elseif (OperatorEnum::LIKE === $filter->operator) {
            // @todo apply $propertyAnalyzer here!
            // yield 'must' => [
            //     'match' => [
            //         $property => [
            //             'query' => $filter->value,
            //             ...$propertyAnalyzer,
            //             'operator' => 'AND',
            //         ],
            //     ],
            // ];
            foreach (IndexUtils::explodeTerm($filter->value) as $value) {
                yield 'must' => [
                    'wildcard' => [
                        $property => [
                            'value' => "*{$value}*",
                            'case_insensitive' => true,
                        ],
                    ],
                ];
            }
        } elseif (in_array($filter->operator, [
            OperatorEnum::LT,
            OperatorEnum::LTE,
            OperatorEnum::GT,
            OperatorEnum::GTE,
        ], true)) {
            yield 'must' => [
                'range' => [
                    $property => [
                        mb_strtolower($filter->operator) => (float)$filter->value,
                    ],
                ],
            ];
        } elseif (
            OperatorEnum::CONTAINS === $filter->operator
            || OperatorEnum::EXCLUDES === $filter->operator
        ) {
            yield OperatorEnum::EXCLUDES === $filter->operator ? 'must_not' : 'must' => [
                'match' => [
                    $property => [
                        'query' => $filter->value,
                        ...$propertyAnalyzer,
                        // 'operator' => 'AND',
                    ],
                ],
            ];
        }
    }

    private function filtersToQuery(Filters|Filter $f): array
    {
        $array = [];

        // combined
        if ($f instanceof Filters) {
            // AND
            if (CombineOperatorEnum::AND === $f->operator) {
                foreach ($f->filters as $filter) {
                    $array['bool'] ??= [];
                    $array['bool']['must'] ??= [];
                    $array['bool']['must'][] = $this->filtersToQuery($filter);
                }

                return $array;
            }

            // OR
            if (CombineOperatorEnum::OR === $f->operator) {
                foreach ($f->filters as $filter) {
                    $array['bool'] ??= [];
                    $array['bool']['should'] ??= [];
                    $array['bool']['should'][] = $this->filtersToQuery($filter);
                    $array['bool']['minimum_should_match'] = 1;
                }

                return $array;
            }

            return [];
        }

        // simple property
        if (FilterTypeEnum::PROPERTY === $f->header->type) {
            foreach ($this->createFilters($f, $f->header->code) as $type => $typeFilter) {
                $array['bool'][$type] ??= [];
                $array['bool'][$type][] = $typeFilter;
            }

            return $array;
        }

        // simple attribute
        if (FilterTypeEnum::ATTRIBUTE === $f->header->type) {
            if (null !== $attributeType = $this->doctrineCache->attributes->codeToType($f->header->code)) {
                if (OperatorEnum::EXISTS === $f->operator) {
                    foreach ($this->addExistsFilter($f) as $type => $typeFilter) {
                        $array['bool'][$type] ??= [];
                        $array['bool'][$type][] = $typeFilter;
                    }
                } else {
                    foreach ($this->addValuesFilter($attributeType, $f) as $type => $typeFilter) {
                        $array['bool'][$type] ??= [];
                        $array['bool'][$type][] = $typeFilter;
                    }
                }
            }
        }

        return $array;
    }

    private function toArray(array $body = self::DEFAULT_BODY): array
    {
        if (0 < count($this->orders)) {
            $body['sort'] = $this->orders;
        }

        if (null !== $this->offset && null !== $this->itemPerPage) {
            $body['from'] = $this->offset;
            $body['size'] = $this->itemPerPage;
        }

        if (null !== $this->source) {
            $body['_source'] = $this->source;
        }

        if (0 < count($this->query)) {
            $body['query'] = $this->query;
        }

        return $body;
    }
}
