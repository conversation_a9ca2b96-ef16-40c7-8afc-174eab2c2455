<?php declare(strict_types=1);

namespace App\Bridge\Eav\Api\Filter;

use ApiPlatform\Doctrine\Orm\Filter\AbstractFilter;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use App\Contracts\Model\Header;
use Doctrine\ORM\QueryBuilder;
use function is_string;

class AttributeOptionFilter extends AbstractFilter
{
    public function getDescription(string $resourceClass): array
    {
        return [
            'header' => [
                'property' => 'header',
                'type' => 'string',
                'required' => false,
            ],
        ];
    }

    protected function filterProperty(
        string $property,
        mixed $value,
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $resourceClass,
        ?Operation $operation = null,
        array $context = [],
    ): void {
        if ('header' === $property && is_string($value)) {
            $header = Header::from($value);
            if ($header->isAttribute()) {
                $root = $queryBuilder->getRootAliases()[0];
                $aliasAttribute = $queryNameGenerator->generateJoinAlias('attribute');
                $queryBuilder
                    ->innerJoin("{$root}.attribute", $aliasAttribute)
                    ->andWhere("{$aliasAttribute}.code = :attribute_code")
                    ->setParameter('attribute_code', $header->code);
            } else {
                $queryBuilder->andWhere('0 = 1');
            }
        }
    }
}
