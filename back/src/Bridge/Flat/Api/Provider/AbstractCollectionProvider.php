<?php declare(strict_types=1);

namespace App\Bridge\Flat\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Api\Model\FilterHelper;
use App\Api\Model\Uuids;
use App\Bridge\Catalog\Documents;
use App\Bridge\Filter\FiltersManager;
use App\Bridge\Filter\Model\Filters;
use App\Bridge\Paginator\Pagination;
use App\Bridge\Security\Security;
use App\Bridge\Workflow\Api\FakePaginator;
use ArrayIterator;
use Doctrine\ORM\EntityManagerInterface;
use function count;

abstract readonly class AbstractCollectionProvider implements ProviderInterface
{
    public function __construct(
        protected FiltersManager $filtersManager,
        protected Pagination $pagination,
        protected Documents $documents,
        protected EntityManagerInterface $em,
        protected Security $security,
    ) {
    }

    public function provide(
        Operation $operation,
        array $uriVariables = [],
        array $context = [],
    ): FakePaginator {
        $filter = new FilterHelper($context['filters'] ?? []);

        $filters = $this->documents->getQueryFilters($this->getType(), $filter);

        if (null !== $moreFilters = $this->filtersManager->getFilters()) {
            $filters->filters[] = $moreFilters;
        }

        $hits = $this->documents->findDocumentHits(
            $this->fixFilters($filters),
            $this->pagination->offset,
            $this->pagination->limit,
            $filter->getArray('order'),
        );

        if (0 < count($hits->identifiers)) {
            $ressources = $this
                ->em
                ->getRepository($this->getEntityClass())
                ->findBy(['uuid' => Uuids::fromRfc4122s($hits->identifiers)->toBinaries()], $filter->getArray('order'));
        }

        return new FakePaginator(
            new ArrayIterator($ressources ?? []),
            $this->pagination->page,
            $this->pagination->limit,
            $hits->count,
        );
    }

    abstract protected function getType(): string;

    /**
     * @phpstan-return class-string<object>
     */
    abstract protected function getEntityClass(): string;

    protected function fixFilters(Filters $filters): Filters
    {
        return $filters;
    }
}
