<?php declare(strict_types=1);

namespace App\Bridge\Flat\Api\Provider;

use ApiPlatform\Metadata\Operation;
use App\Bridge\Filter\Enum\CombineOperatorEnum;
use App\Bridge\Filter\Enum\OperatorEnum;
use App\Bridge\Filter\Model\Filter;
use App\Bridge\Filter\Model\Filters;
use App\Bridge\Flat\Entity\FlatProduct;
use App\Bridge\Workflow\Api\FakePaginator;
use App\Contracts\Enum\ApiTypeEnum;
use App\Contracts\Model\Header;
use ArrayIterator;
use function array_keys;
use function count;

final readonly class FlatProductCollectionProvider extends AbstractCollectionProvider
{
    public function provide(Operation $operation, array $uriVariables = [], array $context = []): FakePaginator
    {
        if (
            (null !== $catalogs = $this->security->user?->catalogs)
            && 0 === count($catalogs)
        ) {
            return new FakePaginator(new ArrayIterator([]), 1, 1, 0);
        }

        return parent::provide($operation, $uriVariables, $context);
    }

    protected function getType(): string
    {
        return ApiTypeEnum::PRODUCT;
    }

    protected function getEntityClass(): string
    {
        return FlatProduct::class;
    }

    protected function fixFilters(Filters $filters): Filters
    {
        // limit to user's view only
        if (null !== $catalogs = $this->security->user?->catalogs) {
            return new Filters([
                new Filter(header: Header::property('catalogs'), operator: OperatorEnum::IN, value: array_keys($catalogs)),
                ...$filters->filters,
            ], CombineOperatorEnum::AND);
        }

        return parent::fixFilters($filters);
    }
}
