<?php declare(strict_types=1);

namespace App\Bridge\Mirakl\Command;

use App\Bridge\Mirakl\MiraklFactory;
use App\Contracts\Enum\ChannelTypeEnum;
use App\Module\Channel\Entity\Channel;
use Doctrine\ORM\EntityManagerInterface;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use function in_array;

#[AsCommand('app:bridge:mirakl:attributes')]
final class MiraklAttributesCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly MiraklFactory $factory,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->addArgument('channel', InputArgument::REQUIRED, 'Channel code');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $channel = $this->em->getRepository(Channel::class)->find($input->getArgument('channel'));

        if (ChannelTypeEnum::MIRAKL !== $channel->type) {
            $output->writeln('<error>Invalid channel</error>');

            return self::FAILURE;
        }

        $mirakl = $this->factory->getMirakl($channel);

        $attributes = [];
        $attributeOptions = [];
        foreach ($mirakl->getProductAttributes() as $productAttribute) {
            if ($productAttribute->belongToMiraklConfig($channel->getParametersAsObject())) {
                $attributes[] = [
                    'code' => $productAttribute->code,
                    'category' => $productAttribute->codeCategory,
                    'names.fr_FR' => $productAttribute->names->fr,
                    'type' => $productAttribute->type,
                    'list_code' => $productAttribute->valuesList,
                ];

                if ($productAttribute->hasList()) {
                    $attributeOptions[$productAttribute->code] = [];

                    $options = $mirakl->getAttributeValues($productAttribute->valuesList)->get($productAttribute->valuesList);
                    foreach ($options->values as $option) {
                        $attributeOptions[$productAttribute->code][] = [
                            'attribute' => $productAttribute->code,
                            'list_code' => $options->code,
                            'option_code' => $option->code,
                            'option_name.fr_FR' => $option->names->fr,
                        ];
                    }
                }
            }
        }

        $spreadsheet = new Spreadsheet();

        $spreadsheet->getActiveSheet()->setCodeName('attributes');
        $spreadsheet->getActiveSheet()->setTitle('attributes');
        $spreadsheet->getActiveSheet()->fromArray([
            array_keys($attributes[0]),
            ...$attributes,
        ]);

        $knownList = [];
        foreach ($attributeOptions as $code => $options) {
            if (in_array($options[0]['list_code'], $knownList)) {
                continue;
            }
            $knownList[] = $options[0]['list_code'];
            $spreadsheet->addSheet(new Worksheet($spreadsheet, $options[0]['list_code'])->fromArray([
                array_values($options[0]),
                ...$options,
            ]));
        }

        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');

        return self::SUCCESS;
    }
}
