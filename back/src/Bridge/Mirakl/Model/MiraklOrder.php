<?php declare(strict_types=1);

namespace App\Bridge\Mirakl\Model;

use App\Module\Hub\Model\Address;
use App\Utils\DateUtils;
use DateTime;

class MiraklOrder
{
    public function __construct(
        public string $id,
        public string $commercialId,
        public ?DateTime $createdAt,
        public ?DateTime $updatedAt,
        public string $state,
        public string $currencyCode,
        public float $totalPrice,
        public float $taxPart,
        public float $shippingPart,
        public Address $billingAddress,
        public Address $shippingAddress,
        public array $items,
    ) {
    }

    public static function create(array $order): self
    {
        $taxPart = 0.0;
        foreach ($order['order_taxes'] as $tax) {
            $taxPart += $tax['total_amount'];
        }

        $mustAddTax = 'TAX_EXCLUDED' === $order['order_tax_mode'];

        $price = $order['total_price'];
        if ($mustAddTax) {
            $price += $taxPart;
        }

        return new self(
            id: $order['order_id'],
            commercialId: $order['commercial_id'],
            createdAt: DateUtils::new($order['created_date']),
            updatedAt: DateUtils::new($order['last_updated_date']),
            state: $order['order_state'],
            currencyCode: $order['currency_iso_code'],
            totalPrice: $price,
            taxPart: $taxPart,
            shippingPart: $order['shipping_price'],
            billingAddress: self::formatAddress($order['customer']['billing_address'], $order['customer']['email']),
            shippingAddress: self::formatAddress($order['customer']['shipping_address'], $order['customer']['email']),
            items: array_map(
                static fn(array $orderItem): MiraklOrderItem => MiraklOrderItem::create($orderItem, $mustAddTax),
                $order['order_lines'],
            ),
        );
    }

    private static function formatAddress(array $address, string $email): Address
    {
        return new Address(
            firstname: $address['firstname'],
            lastname: $address['lastname'],
            street: $address['street_1'],
            street2: $address['street_2'],
            city: $address['city'],
            postcode: $address['zip_code'],
            country: $address['country'],
            phone: $address['phone'] ?? null,
            email: $email,
        );
    }
}
