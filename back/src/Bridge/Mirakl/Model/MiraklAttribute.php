<?php declare(strict_types=1);

namespace App\Bridge\Mirakl\Model;

use App\Bridge\Mirakl\Enum\MiraklTypeMapping;
use App\Bridge\Mirakl\MiraklConfig;
use App\Bridge\Translation\Entity\Translations;
use App\Module\Channel\Model\Definition\MappingColumnDefinition;
use App\Module\Channel\Model\Definition\UiDefinition;
use App\Utils\UuidUtils;
use function in_array;

class MiraklAttribute
{
    public function __construct(
        public string $code,
        public ?string $codeCategory,
        public Translations $names,
        public Translations $descriptions,
        public string $requirementLevel,
        public string $type,
        public ?string $values,
        public ?string $valuesList,
        public array $typeParameters,
    ) {
    }

    public function belongToMiraklConfig(MiraklConfig $config): bool
    {
        // "global" attribute
        if (null === $this->codeCategory) {
            return true;
        }

        return in_array($this->codeCategory, $config->categories, true);
    }

    public static function create(array $attribute): self
    {
        return new self(
            $attribute['code'],
            '' === $attribute['hierarchy_code'] ? null : $attribute['hierarchy_code'],
            new Translations(
                fr: array_find($attribute['label_translations'] ?? [], static fn (array $translation) => 'fr' === $translation['locale'])['value'] ?? $attribute['label'],
                en: $attribute['label'],
            ),
            new Translations(
                fr: array_find($attribute['description_translations'] ?? [], static fn (array $translation) => 'fr' === $translation['locale'])['value'] ?? $attribute['label'],
                en: $attribute['description'],
            ),
            $attribute['requirement_level'],
            $attribute['type'],
            $attribute['values'],
            $attribute['values_list'],
            $attribute['type_parameters'] ?? [],
        );
    }

    public function toMappingColumnDefinition(?string $optionsIri): MappingColumnDefinition
    {
        return new MappingColumnDefinition(
            uuid: UuidUtils::new(),
            ui: new UiDefinition(isRequired: 'REQUIRED' === $this->requirementLevel, isReadonly: true),
            names: $this->names,
            descriptions: $this->descriptions,
            type: MiraklTypeMapping::toPlatformType($this->type),
            options: $optionsIri,
        );
    }

    public function hasList(): bool
    {
        return null !== $this->valuesList;
    }
}
