<?php declare(strict_types=1);

namespace App\Bridge\Workflow\Api\Provider;

use ApiPlatform\Metadata\Operation;
use App\Api\IriConverter;
use App\Api\Model\FilterHelper;
use App\Bridge\Catalog\Documents;
use App\Bridge\Filter\Enum\OperatorEnum;
use App\Bridge\Filter\FiltersManager;
use App\Bridge\Filter\Model\Filter;
use App\Bridge\Filter\Model\Filters;
use App\Bridge\Paginator\Pagination;
use App\Bridge\Security\Manager\AccessManager;
use App\Bridge\Security\Security;
use App\Bridge\Workflow\Api\FakePaginator;
use App\Bridge\Workflow\Entity\ContentError;
use App\Bridge\Workflow\Entity\MediaError;
use App\Bridge\Workflow\Entity\ProductError;
use App\Contracts\Enum\ApiTypeEnum;
use App\Contracts\Model\Header;
use App\Module\Cache\DoctrineCache;
use App\Utils\IriUtils;
use App\Utils\SecurityUtils;
use ArrayIterator;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use function count;
use function iterator_to_array;

final readonly class DocumentErrorSummaryCollectionProvider extends AbstractScopeProvider
{
    public function __construct(
        DoctrineCache $doctrineCache,
        EntityManagerInterface $em,
        IriConverter $iriConverter,
        NormalizerInterface $normalizer,
        AccessManager $accessManager,
        Security $security,
        private FiltersManager $filtersManager,
        private Pagination $pagination,
        private Documents $documents,
    ) {
        parent::__construct($doctrineCache, $em, $iriConverter, $normalizer, $accessManager, $security);
    }

    public function provide(
        Operation $operation,
        array $uriVariables = [],
        array $context = [],
    ): FakePaginator {
        $filters = $this->filtersManager->getFilters() ?? new Filters();

        // add raw ui filter
        $filter = new FilterHelper($context['filters'] ?? []);
        if (null !== $skus = $filter->getArray('sku')) {
            $filters->filters[] = new Filter(Header::property('documentSku'), OperatorEnum::IN, $skus);
        }
        if (null !== $level = $filter->get('level')) {
            $filters->filters[] = new Filter(Header::property('ruleLevel'), OperatorEnum::EQ, $level);
        }
        if (null !== $localeCode = $filter->get('locale')) {
            $localeCode = IriUtils::toCode($localeCode);
            $filters->filters[] = new Filter(Header::property('localeCode'), OperatorEnum::EQ, $localeCode);
        }
        if (null !== $catalogScopeCode = $filter->get('catalogScope')) {
            $catalogScopeCode = IriUtils::toCode($catalogScopeCode);
            $filters->filters[] = new Filter(Header::property('catalogScopeCode'), OperatorEnum::EQ, $catalogScopeCode);
        }
        if (null !== $scopeCode = $filter->get('scope')) {
            $scopeCode = IriUtils::toCode($scopeCode);
            $filters->filters[] = new Filter(Header::property('scopeCode'), OperatorEnum::EQ, $scopeCode);
        }
        if (null !== $ruleCode = $filter->get('rule')) {
            $ruleCode = IriUtils::toCode($ruleCode);
            $filters->filters[] = new Filter(Header::property('ruleCode'), OperatorEnum::LIKE, $ruleCode);
        }
        if (null !== $ruleGroupCode = $filter->get('ruleGroup')) {
            $ruleGroupCode = IriUtils::toCode($ruleGroupCode);
            $filters->filters[] = new Filter(Header::property('ruleGroupCode'), OperatorEnum::EQ, $ruleGroupCode);
        }
        if (null !== $type = $filter->get('type')) {
            $filters->filters[] = new Filter(Header::property('type'), OperatorEnum::EQ, $type);
        }

        if (null !== $catalogs = $this->security->user?->catalogs) {
            if (0 === count($catalogs)) {
                return new FakePaginator(
                    new ArrayIterator(),
                    $this->pagination->page,
                    $this->pagination->limit,
                    0,
                );
            }

            $writableAttributeGroups = SecurityUtils::catalogsToWritableAttributeGroupCodes($catalogs);

            $writableAttributes = [];
            foreach ($writableAttributeGroups as $id) {
                $writableAttributes = [...$writableAttributes, ...$this->doctrineCache->attributeGroups->codeToAttributeCodes($id)];
            }

            if (0 === count($writableAttributes)) {
                return new FakePaginator(
                    new ArrayIterator(),
                    $this->pagination->page,
                    $this->pagination->limit,
                    0,
                );
            }

            $filters->filters[] = new Filter(Header::property('catalogCode'), OperatorEnum::IN, array_keys($catalogs));
            $filters->filters[] = new Filter(Header::property('errorTargets'), OperatorEnum::IN, $writableAttributes);
        }

        $hits = $this->documents->findErrorHits(
            $filters,
            $this->pagination->offset,
            $this->pagination->limit,
            $filter->getArray('order'),
            true
        );

        $errors = [];
        foreach ($hits->sources as $id => $hit) {
            $errors = [
                ...$errors,
                ...iterator_to_array($this->findWorkflowFlowSummaries(
                    new FilterHelper(['id' => $id]),
                    $hit['documentType'],
                    match ($hit['documentType']) {
                        ApiTypeEnum::PRODUCT => ProductError::class,
                        ApiTypeEnum::MEDIA => MediaError::class,
                        ApiTypeEnum::CONTENT => ContentError::class,
                        default => null,
                    }
                )),
            ];
        }

        return new FakePaginator(
            new ArrayIterator($errors),
            $this->pagination->page,
            $this->pagination->limit,
            $hits->count,
        );
    }
}
