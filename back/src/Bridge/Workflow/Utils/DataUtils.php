<?php declare(strict_types=1);

namespace App\Bridge\Workflow\Utils;

use App\Contracts\Type\AttributeTypes;
use App\Utils\DateUtils;
use App\Utils\JsonUtils;
use DateTime;
use Symfony\Component\String\Slugger\AsciiSlugger;
use function in_array;
use function is_array;
use function is_bool;
use function is_float;
use function is_int;
use function is_numeric;
use function is_string;
use function preg_match;
use function str_starts_with;
use function Symfony\Component\String\u;

abstract class DataUtils
{
    public static function toText(mixed $value): ?string
    {
        if (is_string($value)) {
            return $value;
        }
        if (is_numeric($value)) {
            return (string)$value;
        }
        if (is_bool($value)) {
            return $value ? '1' : '0';
        }

        return null;
    }

    public static function toSlug(mixed $value): ?string
    {
        if (null === $value = self::toText($value)) {
            return null;
        }

        return u(new AsciiSlugger()->slug($value)->toString())->lower()->toString();
    }

    public static function toNumber(mixed $value): int|float|null
    {
        if (is_int($value)) {
            return $value;
        }
        if (is_float($value)) {
            return $value;
        }
        if (is_string($value)) {
            if (ctype_digit($value)) {
                return (int)$value;
            }
            if (preg_match('/^\d+[,.]\d+$/', $value)) {
                return (float)$value;
            }
        }

        return null;
    }

    // @todo never use floats with php!
    public static function getPrecision(mixed $value): int
    {
        // find a float in a string
        if (is_string($value) && !ctype_digit($value)) {
            $m = [];
            if (preg_match('/^\d+[,.](\d+)$/', $value, $m)) {
                return mb_strlen(rtrim($m[1], '0'));
            }
        }

        return 0;
    }

    public static function toBool(mixed $value): ?bool
    {
        if (is_bool($value)) {
            return $value;
        }
        if (is_int($value)) {
            if (0 === $value) {
                return false;
            }
            if (1 === $value) {
                return true;
            }
        }
        if (is_string($value)) {
            if (in_array(mb_strtolower($value), ['0', 'n', 'no', 'non'], true)) {
                return false;
            }
            if (in_array(mb_strtolower($value), ['1', 'y', 'yes', 'oui'], true)) {
                return true;
            }
        }

        return null;
    }

    public static function toDate(mixed $value): ?DateTime
    {
        if ($value instanceof DateTime) {
            return $value;
        }
        if (is_string($value)) {
            return DateUtils::new($value);
        }

        return null;
    }

    public static function toArray(mixed $value): ?array
    {
        if (is_array($value)) {
            return $value;
        }
        if (is_string($value)) {
            if (str_starts_with($value, '{')) {
                return JsonUtils::decode($value);
            }

            return AttributeTypes::scalarToArray($value);
        }

        return null;
    }
}
