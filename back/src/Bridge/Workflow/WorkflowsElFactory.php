<?php declare(strict_types=1);

namespace App\Bridge\Workflow;

use App\Bridge\Eav\Exception\MissingFamilyException;
use App\Bridge\Eav\Exception\MissingUnitException;
use App\Bridge\Eav\UnitConverter;
use App\Bridge\Flat\Entity\FlatMedia;
use App\Bridge\Flat\Model\Values;
use App\Bridge\Media\Files;
use App\Bridge\Shopify\Enum\ShopifyConfigurationEnum;
use App\Bridge\Shopify\RichTextConverter;
use App\Bridge\Workflow\Exception\InvalidValueException;
use App\Bridge\Workflow\Exception\MissingValueException;
use App\Bridge\Workflow\Utils\DataUtils;
use App\Contracts\Enum\ApiTypeEnum;
use App\Contracts\Enum\LangEnum;
use App\Contracts\Enum\TableEnum;
use App\Contracts\Model\Header;
use App\Contracts\Type\AttributeTypes;
use App\Module\Cache\DoctrineCache;
use App\Utils\DateUtils;
use App\Utils\JsonUtils;
use App\Utils\SqlUtils;
use App\Utils\UuidUtils;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\ExpressionLanguage\ExpressionFunction;
use Symfony\Component\ExpressionLanguage\ExpressionLanguage;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Uid\Uuid;
use Throwable;
use function array_key_exists;
use function count;
use function in_array;
use function is_array;
use function is_int;
use function is_object;
use function is_string;
use function str_contains;
use function str_replace;
use const JSON_UNESCAPED_UNICODE;
use const PHP_EOL;

final readonly class WorkflowsElFactory
{
    public function __construct(
        private PropertyAccessorInterface $propertyAccessor,
        private UnitConverter $unitConverter,
        private DoctrineCache $doctrineCache,
        private EntityManagerInterface $em,
        private AttributeTypes $attributeTypes,
        private UrlGeneratorInterface $urlGenerator,
        private Files $files,
    ) {
    }

    /**
     * @see https://symfony.com/doc/current/components/expression_language.html
     */
    public function createEl(?string $elLocale = null, ?string $elScope = null): ExpressionLanguage
    {
        $fnCompile = fn(string $str): string => '';

        $fnCompileDocument = function (
            object $variables,
            string $code,
            string $locale = '',
            string $scope = '',
            ?callable $cleaner = null,
        ) use ($elLocale, $elScope): mixed {
            // replace default values
            $locale = '' === $locale ? $elLocale : $locale;
            $scope = '' === $scope ? $elScope : $scope;

            $values = $this->access($variables, 'values');
            if (!$values instanceof Values) {
                throw new MissingValueException($code);
            }

            try {
                $value = $this->attributeTypes->toScalar(
                    UuidUtils::new(),
                    Header::attribute(code: $code, locale: $locale, scope: $scope),
                    $values,
                    true,
                );
                if (null !== $cleaner) {
                    $value = $cleaner($value);
                    if (null === $value) {
                        throw new InvalidValueException($code);
                    }
                }

                return $value;
            } catch (Throwable) {
                throw new MissingValueException($code);
            }
        };

        /**
         * @throws InvalidValueException
         * @throws MissingValueException
         */
        $fnValue = function (
            array $variables,
            string $code,
            string $locale = '',
            string $scope = '',
            ?callable $cleaner = null,
        ) use ($elLocale, $elScope, $fnCompileDocument): mixed {
            // replace default values
            $locale = '' === $locale ? $elLocale : $locale;
            $scope = '' === $scope ? $elScope : $scope;

            $values = $this->access($variables, 'values');
            $codes = $this->access($values, $code);
            if (!is_array($codes)) {
                if (isset($variables['document']) && is_object($variables['document'])) {
                    try {
                        return $fnCompileDocument(
                            $variables['document'],
                            $code,
                            $locale ?? '',
                            $scope ?? '',
                            $cleaner,
                        );
                    } catch (Throwable) {
                        // do nothing, fallback on current system
                    }
                }
                throw new MissingValueException($code);
            }

            // search for locale
            $isLocalizable = $this->doctrineCache->attributes->codeIsLocalizable($code);
            $isScopable = $this->doctrineCache->attributes->codeIsScopable($code);
            foreach ($codes as $codeValues) {
                $codeValueLocale = $this->access($codeValues, 'locale');
                $codeValueScope = $this->access($codeValues, 'scope');

                if (
                    (
                        ($isLocalizable && $locale === $codeValueLocale)
                        || (!$isLocalizable && null === $codeValueLocale)
                    )
                    && (
                        ($isScopable && $scope === $codeValueScope)
                        || (!$isScopable && null === $codeValueScope)
                    )
                ) {
                    $value = $this->access($codeValues, 'data');
                    if (null !== $cleaner) {
                        $value = $cleaner($value);
                        if (null === $value) {
                            throw new InvalidValueException($code);
                        }
                    }

                    return $value;
                }
            }

            throw new MissingValueException($code);
        };

        $el = new ExpressionLanguage();

        // accessors functions
        $accessors = [
            'value' => null,
            'value_text' => DataUtils::toText(...),
            'value_number' => DataUtils::toNumber(...),
            'value_bool' => DataUtils::toBool(...),
            'value_date' => DataUtils::toDate(...),
            'value_metric_unit' => $this->unitConverter->toMetricUnit(...),
            'value_metric_amount' => $this->unitConverter->toMetricAmount(...),
        ];
        foreach ($accessors as $name => $cleaner) {
            $el->addFunction(new ExpressionFunction(
                $name,
                $fnCompile,
                fn(array $variables, string $code, string $locale = '', string $scope = ''): mixed => $fnValue($variables, $code, $locale, $scope, $cleaner),
            ));
            $el->addFunction(new ExpressionFunction(
                str_replace('value', 'values', $name),
                $fnCompile,
                function (array $variables, array $codes, string $locale = '', string $scope = '') use ($fnValue, $cleaner): array {
                    $values = [];
                    foreach ($codes as $code) {
                        try {
                            $values[] = $fnValue($variables, $code, $locale, $scope, $cleaner);
                        } catch (Throwable) {
                        }
                    }

                    return $values;
                },
            ));
            $el->addFunction(new ExpressionFunction(
                "has_{$name}",
                $fnCompile,
                function (array $variables, string $code, string $locale = '', string $scope = '') use ($fnValue, $cleaner): bool {
                    try {
                        $fnValue($variables, $code, $locale, $scope, $cleaner);

                        return true;
                    } catch (MissingValueException) {
                    }

                    return false;
                },
            ));
        }
        $el->addFunction(new ExpressionFunction('count_existing_values', $fnCompile, function (array $variables, array $codes, string $locale = '', string $scope = '') use ($fnValue): int {
            $count = 0;
            foreach ($codes as $code) {
                try {
                    $fnValue($variables, $code, $locale, $scope);
                    $count++;
                } catch (MissingValueException) {
                }
            }

            return $count;
        }));

        $el->addFunction(new ExpressionFunction('option_name', $fnCompile, function (array $variables, string $code, $optionLocaleCode = LangEnum::DEFAULT, string $locale = '', string $scope = '') use ($fnValue): mixed {
            $value = $fnValue($variables, $code, $locale, $scope);

            return $this->doctrineCache->attributeOptions->translate($code, $value, $optionLocaleCode);
        }));

        $el->addFunction(new ExpressionFunction('dictionary_find', $fnCompile, function (array $variables, string $dictionaryCode, string $value): mixed {
            $dictionary = $this->doctrineCache->dictionaries->find($dictionaryCode);

            return $dictionary?->getDefinition($value);
        }));

        $el->addFunction(new ExpressionFunction('dictionary_exists', $fnCompile, fn (array $variables, string $dictionaryCode): bool => $this->doctrineCache->dictionaries->exists($dictionaryCode)));

        $el->addFunction(new ExpressionFunction('dictionary_contains', $fnCompile, function (array $variables, string $dictionaryCode, string $from): bool {
            $dictionary = $this->doctrineCache->dictionaries->find($dictionaryCode);

            return null !== $dictionary?->getDefinition($from);
        }));

        $el->addFunction(new ExpressionFunction('is_locale', $fnCompile, fn(array $variables, string $locale) => $locale === $elLocale));

        $el->addFunction(new ExpressionFunction('is_channel', $fnCompile, fn(array $variables, string $channel) => $channel === $elScope));

        // format tester functions
        $el->addFunction(ExpressionFunction::fromPhp('is_string'));
        $el->addFunction(ExpressionFunction::fromPhp('is_numeric'));
        $el->addFunction(ExpressionFunction::fromPhp('is_int', 'is_integer'));
        $el->addFunction(ExpressionFunction::fromPhp('is_float'));
        $el->addFunction(ExpressionFunction::fromPhp('is_bool'));
        $el->addFunction(ExpressionFunction::fromPhp('is_array'));

        // numeric functions
        $el->addFunction(ExpressionFunction::fromPhp('min'));
        $el->addFunction(ExpressionFunction::fromPhp('max'));
        $el->addFunction(ExpressionFunction::fromPhp('abs'));
        $el->addFunction(ExpressionFunction::fromPhp('round'));
        $el->addFunction(ExpressionFunction::fromPhp('ceil'));
        $el->addFunction(ExpressionFunction::fromPhp('floor'));
        $el->addFunction(new ExpressionFunction('string_number_precision', $fnCompile, fn(array $variables, mixed $value): int => DataUtils::getPrecision($value)));
        $el->addFunction(new ExpressionFunction('string_number_thresholds_are', $fnCompile, fn(array $variables, mixed $value, ?int $min = null, ?int $max = null, ?int $precision = null): bool => (
            (null !== $number = DataUtils::toNumber($value))
            && (null === $min || $number > $min)
            && (null === $max || $number < $max)
            && (null === $precision || DataUtils::getPrecision($value) <= $precision)
        )));

        // @todo find a better place of php.ini
        bcscale(10);
        $el->addFunction(ExpressionFunction::fromPhp('bcadd', 'string_numbers_add'));
        $el->addFunction(ExpressionFunction::fromPhp('bccomp', 'string_numbers_comp'));
        $el->addFunction(ExpressionFunction::fromPhp('bcdiv', 'string_numbers_div'));
        $el->addFunction(ExpressionFunction::fromPhp('bcmul', 'string_numbers_mul'));
        $el->addFunction(ExpressionFunction::fromPhp('bcsub', 'string_numbers_sub'));

        // string functions
        $el->addFunction(ExpressionFunction::fromPhp('strlen', 'string_length'));
        $el->addFunction(ExpressionFunction::fromPhp('strtolower', 'string_lowercase'));
        $el->addFunction(ExpressionFunction::fromPhp('strtoupper', 'string_uppercase'));
        $el->addFunction(ExpressionFunction::fromPhp('str_starts_with', 'string_starts_with'));
        $el->addFunction(ExpressionFunction::fromPhp('str_ends_with', 'string_ends_with'));
        $el->addFunction(ExpressionFunction::fromPhp('implode')); // @todo doc + better name ?
        $el->addFunction(new ExpressionFunction('string_to_number', $fnCompile, fn(array $variables, mixed $value): int|float => DataUtils::toNumber($value) ?? throw new InvalidValueException()));
        $el->addFunction(new ExpressionFunction('string_concat', $fnCompile, fn(array $variables, int|float|string ...$strings): string => implode('', $strings))); // @todo doc + better name ?
        $el->addFunction(new ExpressionFunction('string_slugify', $fnCompile, fn(array $variables, mixed $value): string => DataUtils::toSlug($value) ?? throw new InvalidValueException()));

        // date functions
        $el->addFunction(new ExpressionFunction('date_now', $fnCompile, fn(): DateTime => DateUtils::now()));
        $el->addFunction(new ExpressionFunction('in_days', $fnCompile, function (array $variables, string|DateTime $date): int {
            if (is_string($date)) {
                try {
                    $date = DateUtils::new($date);
                } catch (Throwable) {
                    throw new InvalidValueException();
                }
            }

            $interval = DateUtils::now()->diff($date);
            $days = $interval->days;

            if (false === $days) {
                throw new InvalidValueException();
            }

            if ($interval->invert) {
                $days = -$days;
            }

            return $days;
        }));

        // array
        $el->addFunction(ExpressionFunction::fromPhp('in_array', 'array_contains'));
        $el->addFunction(ExpressionFunction::fromPhp('array_unique', 'array_unique'));
        $el->addFunction(ExpressionFunction::fromPhp('count', 'array_count'));
        $el->addFunction(ExpressionFunction::fromPhp('array_column'));
        $toArray = fn(array $args, $value): array => is_array($value) ? $value : [$value];
        $el->addFunction(new ExpressionFunction('to_array', $fnCompile, $toArray));

        $el->addFunction(ExpressionFunction::fromPhp('preg_match'));

        // @todo either_one
        // @todo in_locale?
        // @todo in_scope?

        // metric function
        $el->addFunction(new ExpressionFunction('value_metric', $fnCompile, function (array $variables, string $code, string $targetUnit, string $locale = '', string $scope = '') use ($fnValue): string {
            $value = $fnValue($variables, $code, $locale, $scope);

            try {
                $metric = $this->unitConverter->toMetric($value);

                return $this
                    ->unitConverter
                    ->convert($code, $metric, $targetUnit)
                    ->amount;
            } catch (MissingUnitException $e) {
                throw new MissingValueException($e->unit);
            } catch (MissingFamilyException $e) {
                throw new MissingValueException($e->family);
            }
        }));
        $el->addFunction(new ExpressionFunction('value_metric_to_scalar', $fnCompile, function (array $variables, string $code, string $targetUnit, string $locale = '', string $scope = '') use ($fnValue): string {
            $value = $fnValue($variables, $code, $locale, $scope);

            try {
                $metric = $this->unitConverter->toMetric($value);

                return $this
                    ->unitConverter
                    ->convert($code, $metric, $targetUnit)
                    ->toString();
            } catch (MissingUnitException $e) {
                throw new MissingValueException($e->unit);
            } catch (MissingFamilyException $e) {
                throw new MissingValueException($e->family);
            }
        }));
        $el->addFunction(new ExpressionFunction('value_media_pick_url', $fnCompile, function (array $variables, string $code, ?string $filter = null, string $locale = '', string $scope = '') use ($fnValue): string {
            $url = $fnValue($variables, $code, $locale, $scope);
            if (is_array($url)) {
                $url = $url['url'] ?? null;
            }
            if (null !== $url) {
                if (null !== $filter && str_contains($url, '/binary')) {
                    return str_replace('/binary', "/thumbnail/{$filter}", $url);
                }

                return $url;
            }

            throw new MissingValueException($code);
        }));
        $el->addFunction(new ExpressionFunction('value_media_collection_pick_url', $fnCompile, function (array $variables, string $code, int $index, ?string $filter = null, string $locale = '', string $scope = '') use ($fnValue): string {
            if ($index >= 1) {
                $mediaCollection = $fnValue($variables, $code, $locale, $scope);
                $mediaCollection = DataUtils::toArray($mediaCollection);

                if (is_array($mediaCollection)) {
                    $media = $mediaCollection[$index - 1] ?? null;
                    if (null !== $media) {
                        $url = is_array($media) ? ($media['url'] ?? null) : $media;
                        if (null !== $filter && str_contains($url, '/binary')) {
                            return str_replace('/binary', "/thumbnail/{$filter}", $url);
                        }

                        return $url;
                    }
                }
            }

            throw new MissingValueException($code);
        }));

        $el->addFunction(new ExpressionFunction('value_media_collection_thumbnails', $fnCompile, function (array $variables, string $code, ?string $filter = null, string $locale = '', string $scope = '') use ($fnValue): array {
            $mediaCollection = $fnValue($variables, $code, $locale, $scope);
            $mediaCollection = DataUtils::toArray($mediaCollection);
            $medias = [];

            if (is_array($mediaCollection)) {
                foreach ($mediaCollection as $media) {
                    $url = is_array($media) ? ($media['url'] ?? null) : $media;
                    if (null !== $filter && str_contains($url, '/binary')) {
                        $medias[] = str_replace('/binary', "/thumbnail/{$filter}", $url);
                    }
                }
            }

            return $medias;
        }));

        // @todo je me respecte même plus.
        $el->addFunction(new ExpressionFunction('module_hub_get_stock', $fnCompile, function (array $variables): int {
            try {
                if (null !== $sku = $this->access($variables, 'sku')) {
                    $tableHubStock = TableEnum::HUB_STOCK;

                    $query = <<<SQL
                    SELECT SUM(quantity) AS stock
                    FROM {$tableHubStock}
                    WHERE product = ?
                    SQL;

                    return (int)$this->em->getConnection()->executeQuery($query, [$sku])->fetchOne();
                }
            } catch (Throwable) {
            }

            return 0;
        }));

        // @todo je me respecte plus non plus.
        $el->addFunction(new ExpressionFunction('shopify_html', $fnCompile, function (array $variables, string $value): ?string {
            try {
                return JsonUtils::encode(RichTextConverter::convertHtmlToRichText($value), JSON_UNESCAPED_UNICODE);
            } catch (Throwable) {
            }

            return null;
        }));

        $el->addFunction(new ExpressionFunction('shopify_products', $fnCompile, function (array $variables, string $linkedProducts, string $scope, ?string $attributeForOrder = null): ?string {
            $linkedProducts = explode(PHP_EOL, $linkedProducts);

            $shopifyUuid = [];
            $tableProductData = TableEnum::PRODUCT_DATA;
            $placeholder = SqlUtils::createPlaceholdersFor($linkedProducts);
            try {
                if (null === $attributeForOrder) {
                    $query = <<<SQL
                    SELECT value
                    FROM {$tableProductData}
                    WHERE product_uuid IN ({$placeholder})
                    AND attribute_code = ?
                    SQL;

                    $shopifyUuid = $this->em->getConnection()->executeQuery($query, [
                        ...array_map(static fn(string $uuid): string => Uuid::fromRfc4122($uuid)->toBinary(), $linkedProducts),
                        ShopifyConfigurationEnum::getGidAttribute($scope),
                    ])->fetchFirstColumn();
                } else {
                    $query = <<<SQL
                    SELECT pd.value as gid, HEX(pd.product_uuid) as product, pd2.value as title
                    FROM {$tableProductData} pd
                    LEFT JOIN {$tableProductData} pd2 on pd.product_uuid = pd2.product_uuid
                    WHERE pd.product_uuid IN ({$placeholder})
                    AND pd.attribute_code = ?
                    AND pd2.attribute_code = ?
                    ORDER BY title
                    SQL;

                    $shopifyUuid = $this->em->getConnection()->executeQuery($query, [
                        ...array_map(static fn(string $uuid): string => Uuid::fromRfc4122($uuid)->toBinary(), $linkedProducts),
                        ShopifyConfigurationEnum::getGidAttribute($scope),
                        $attributeForOrder,
                    ])->fetchFirstColumn();
                }
            } catch (Throwable) {
            }

            return JsonUtils::encode(array_unique($shopifyUuid));
        }));

        $el->addFunction(new ExpressionFunction('shopify_products_option', $fnCompile, function (array $variables, string $options, string $name): array {
            $options = explode(PHP_EOL, $options);

            return [
                'name' => $name,
                'position' => 1,
                'values' => array_map(static fn(string $value): array => ['name' => $value], $options),
            ];
        }));

        $el->addFunction(new ExpressionFunction('shopify_product_stock', $fnCompile, function (array $variables): array {
            try {
                if (null !== $sku = $this->access($variables, 'sku')) {
                    $tableHubStock = TableEnum::HUB_STOCK;

                    $query = <<<SQL
                    SELECT source_code, quantity
                    FROM {$tableHubStock}
                    WHERE product = ?
                    SQL;

                    return $this->em->getConnection()->executeQuery($query, [$sku])->fetchAllKeyValue();
                }
            } catch (Throwable) {
            }

            return [];
        }));

        $el->addFunction(new ExpressionFunction('map', $fnCompile, function (array $variables, mixed $data, mixed $map, mixed $default = null): mixed {
            if (!is_array($map)) {
                return $default;
            }
            if (is_array($data)) {
                return array_map(
                    fn(int|string $item) => $map[$item] ?? $default,
                    $data,
                );
            }
            if (is_string($data) || is_int($data)) {
                return $map[$data] ?? $default;
            }

            return null;
        }));

        $el->addFunction(new ExpressionFunction('media_url', $fnCompile, function (array $variables): string {
            if (null !== $uuid = $this->access($variables, 'uuid')) {
                return $this->urlGenerator->generate(FlatMedia::ROUTE_BINARY, ['uuid' => $uuid], UrlGeneratorInterface::ABSOLUTE_URL);
            }

            return '';
        }));

        $el->addFunction(new ExpressionFunction('magento_gallery', $fnCompile, function (array $variables, string ...$mediaAttributes) use ($fnValue): array {
            $medias = [];
            $knownUuids = [];

            $position = 1;
            // Expression régulière pour capturer un UUID
            $pattern = '/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i';
            foreach ($mediaAttributes as $mediaAttribute) {
                try {
                    $data = $fnValue($variables, $mediaAttribute);
                } catch (Throwable) {
                    continue;
                }

                if (!is_array($data)) {
                    if (str_contains($data, PHP_EOL)) {
                        $data = explode(PHP_EOL, $data);
                    } else {
                        $data = [$data];
                    }
                }

                foreach ($data as $item) {
                    if (
                        is_string($item)
                        && preg_match($pattern, $item, $matches)
                        && Uuid::isValid($uuid = $matches[0])
                    ) {
                        if (
                            null === ($localPath = $this->files->getLocalPath(ApiTypeEnum::MEDIA, Uuid::fromRfc4122($uuid)))
                            || false === ($fileContent = file_get_contents($localPath))
                            || in_array($uuid, $knownUuids, true)
                        ) {
                            continue;
                        }

                        $knownUuids[] = $uuid;

                        $base64 = base64_encode($fileContent);

                        $medias[] = [
                            'media_type' => 'image',
                            'label' => 'Product Image ' . $uuid,
                            'position' => $position++,
                            'disabled' => false,
                            'types' => 0 === count($medias) ? ['image', 'small_image', 'thumbnail'] : [],
                            'content' => [
                                'base64_encoded_data' => $base64,
                                'type' => mime_content_type($localPath),
                                'name' => $uuid,
                            ],
                        ];
                    }
                }
            }

            return $medias;
        }));

        $el->addFunction(ExpressionFunction::fromPhp('array_intersect'));

        return $el;
    }

    private function access(mixed $data, string $property): mixed
    {
        if (is_array($data)) {
            if (null !== ($data[$property] ?? null)) {
                return $data[$property];
            }

            if (array_key_exists('document', $data)) {
                if (is_array($data['document'])) {
                    return $data['document'][$property] ?? null;
                }
                if (is_object($data['document'])) {
                    return $this->access($data['document'], $property);
                }
            }
        }

        if (is_object($data)) {
            try {
                return $this->propertyAccessor->getValue($data, $property);
            } catch (Throwable) {
            }
        }

        return null;
    }
}
