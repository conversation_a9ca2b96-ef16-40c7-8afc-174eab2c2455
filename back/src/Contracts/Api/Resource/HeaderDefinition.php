<?php declare(strict_types=1);

namespace App\Contracts\Api\Resource;

use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation as OpenApiOperation;
use App\Api\OpenApi\OpenApiFactory;
use App\Bridge\Translation\Entity\Translations;
use App\Contracts\Api\Filter\HeaderDefinitionFilter;
use App\Contracts\Api\Provider\HeaderDefinitionCollectionProvider;
use App\Contracts\Model\Header;
use Symfony\Component\Serializer\Attribute\Ignore;

#[APM\ApiResource(openapi: new OpenApiOperation(tags: [OpenApiFactory::TAG_CONFIGURATION]))]
#[APM\GetCollection(provider: HeaderDefinitionCollectionProvider::class)]
#[APM\ApiFilter(HeaderDefinitionFilter::class)]
final class HeaderDefinition
{
    public function __construct(
        #[Ignore]
        public string $label, // well, this is strange but it holds a way to filter and sort data
        public Header $header,
        public Translations $names,
        public string $type,
        public bool $isSearchable,
        public bool $isLocalizable,
        public bool $isScopable,
    ) {
    }

    #[APM\ApiProperty(identifier: true)]
    public function getCode(): string
    {
        return base64_encode($this->header->toString());
    }
}
