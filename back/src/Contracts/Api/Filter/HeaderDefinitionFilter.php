<?php declare(strict_types=1);

namespace App\Contracts\Api\Filter;

use ApiPlatform\Metadata\FilterInterface;

class HeaderDefinitionFilter implements FilterInterface
{
    public function getDescription(string $resourceClass): array
    {
        return [
            'documentType' => [
                'property' => 'documentType',
                'type' => 'string',
                'required' => true,
                'strategy' => 'partial',
            ],
            'q' => [
                'property' => 'q',
                'type' => 'string',
                'required' => false,
                'strategy' => 'partial',
            ],
            'header' => [
                'property' => 'header',
                'type' => 'string',
                'required' => false,
                'strategy' => 'exact',
                'is_collection' => true,
            ],
            'type' => [
                'property' => 'type',
                'type' => 'string',
                'required' => false,
                'strategy' => 'partial',
            ],
            'isSearchable' => [
                'property' => 'isSearchable',
                'type' => 'bool',
                'required' => false,
            ],
            'isLocalizable' => [
                'property' => 'isLocalizable',
                'type' => 'bool',
                'required' => false,
            ],
            'isScopable' => [
                'property' => 'isScopable',
                'type' => 'bool',
                'required' => false,
            ],
        ];
    }
}
