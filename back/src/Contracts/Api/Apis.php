<?php declare(strict_types=1);

namespace App\Contracts\Api;

use ApiPlatform\Validator\Exception\ValidationException;
use ApiPlatform\Validator\ValidatorInterface;
use App\Api\Model\FilterHelper;
use App\Contracts\Enum\LoggerChannelEnum;
use Monolog\Attribute\WithMonologChannel;
use Psr\Log\LoggerInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Throwable;

#[WithMonologChannel(LoggerChannelEnum::CONTRACTS)]
final readonly class Apis
{
    public function __construct(
        private DenormalizerInterface $denormalizer,
        private ValidatorInterface $validator,
        private LoggerInterface $logger,
    ) {
    }

    /**
     * @template T
     * @param class-string<T> $class
     * @return T
     * @throws ValidationException
     */
    public function getFilter(string $class, array $context): mixed
    {
        // denormalize filter
        try {
            if (isset($context['filters']['isScopable'])) {
                $context['filters']['isScopable'] = FilterHelper::toBool($context['filters']['isScopable']);
            }

            if (isset($context['filters']['isLocalizable'])) {
                $context['filters']['isLocalizable'] = FilterHelper::toBool($context['filters']['isLocalizable']);
            }

            if (isset($context['filters']['isSearchable'])) {
                $context['filters']['isSearchable'] = FilterHelper::toBool($context['filters']['isSearchable']);
            }

            $filter = $this->denormalizer->denormalize($context['filters'] ?? [], $class);
        } catch (Throwable $e) {
            $this->logger->error('Unable to denormalize filters from API request', [
                'exception' => $e,
                'filters' => $context['filters'] ?? [],
            ]);

            throw new ValidationException();
        }

        // validate filter
        $this->validator->validate($filter);

        return $filter;
    }
}
