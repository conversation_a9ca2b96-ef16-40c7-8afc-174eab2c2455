<?php declare(strict_types=1);

namespace App\Contracts\Api\Model;

use App\Contracts\Enum\ApiTypeEnum;
use Symfony\Component\Validator\Constraints as Assert;

final readonly class HeaderDefinitionFilterModel
{
    public function __construct(
        #[Assert\NotNull]
        #[Assert\Choice(choices: ApiTypeEnum::DOCUMENTS)]
        public ?string $documentType = null,
        public ?string $q = null,
        public array $header = [],
        public ?string $type = null,
        public ?bool $isSearchable = null,
        public ?bool $isLocalizable = null,
        public ?bool $isScopable = null,
    ) {
    }
}
