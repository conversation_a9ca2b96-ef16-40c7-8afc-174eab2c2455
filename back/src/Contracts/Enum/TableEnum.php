<?php declare(strict_types=1);

namespace App\Contracts\Enum;

abstract class TableEnum
{
    public const string ATTRIBUTE = 'attribute';
    public const string ATTRIBUTE_GROUP = 'attribute_group';
    public const string ATTRIBUTE_OPTION = 'attribute_option';
    public const string CATALOG = 'catalog';
    public const string CATALOG_PRODUCT = 'catalog_product';
    public const string CATALOG_MEDIA = 'catalog_media';
    public const string CATEGORY_MEDIA = 'category_media';
    public const string CATALOG_CONTENT = 'catalog_content';
    public const string CATEGORY_CONTENT = 'category_content';
    public const string CATALOG_SCOPE = 'catalog_scope';
    public const string CATALOG_SCOPE_LOCALE = 'catalog_scope_locale';
    public const string CATEGORY = 'category';
    public const string CATEGORY_DATA = 'category_data';
    public const string CATEGORY_PRODUCT = 'category_product';
    public const string CATEGORY_ATTRIBUTE_GROUP = 'category_attribute_group';
    public const string COMPLETUDE = 'completude';
    public const string CONTENT = 'content';
    public const string CONTENT_FOLDER = 'content_folder';
    public const string CONTENT_DATA = 'content_data';
    public const string CONTENT_ATTRIBUTE_GROUP = 'content_attribute_group';
    public const string CURRENCY = 'currency';
    public const string DICTIONARY = 'dictionary';
    public const string DICTIONARY_MAPPING = 'dictionary_mapping';
    public const string EXPORT = 'export';
    public const string ELASTIC_VERSION = 'elastic_version';
    public const string IMPORT = 'import';
    public const string FLAT_CATEGORY = 'flat_category';
    public const string FLAT_CONTENT = 'flat_content';
    public const string FLAT_MEDIA = 'flat_media';
    public const string FLAT_PRODUCT = 'flat_product';
    public const string LOCALE = 'locale';
    public const string MEASURE_FAMILY = 'measure_family';
    public const string MEDIA = 'media';
    public const string MEDIA_FOLDER = 'media_folder';
    public const string MEDIA_DATA = 'media_data';
    public const string UNZIP_MEDIA = 'unzip_media';
    public const string MEDIA_ATTRIBUTE_GROUP = 'media_attribute_group';
    public const string PLATFORM_STATISTIC = 'platform_statistic';
    public const string PRODUCT = 'product';
    public const string PRODUCT_ATTRIBUTE_GROUP = 'product_attribute_group';
    public const string PRODUCT_COMPLETUDE_SCORE = 'product_completude_score';
    public const string CONTENT_COMPLETUDE_SCORE = 'content_completude_score';
    public const string MEDIA_COMPLETUDE_SCORE = 'media_completude_score';
    public const string PRODUCT_DATA = 'product_data';
    public const string PRODUCT_ERROR = 'product_error';
    public const string CONTENT_ERROR = 'content_error';
    public const string MEDIA_ERROR = 'media_error';
    public const string RULE = 'rule';
    public const string RULE_GROUP = 'rule_group';
    public const string SCHEDULE_LOG = 'scheduler_schedule_log';
    public const string SCOPE = 'scope';
    public const string UI_VARIANT = 'ui_variant';
    public const string UPLOAD = 'upload';
    public const string UPLOAD_IMPORT = 'upload_import';
    public const string UPLOAD_EXPORT = 'upload_export';
    public const string USER = 'user';
    public const string USER_GROUP = 'user_group';
    public const string USER_GROUP_CATALOG = 'user_group_catalog';
    public const string USER_GROUP_MEMBERSHIP = 'user_group_membership';
    public const string USER_GROUP_MEMBERSHIP_REQUEST = 'user_group_membership_request';
    public const string USER_GROUP_SCOPE = 'user_group_scope';
    public const string USER_LOGIN = 'user_login';
    public const string VIEW = 'view';
    public const string WORKFLOW = 'workflow';
    public const string WORKFLOW_STEP = 'workflow_step';
    public const string WORKFLOW_STEP_RULE_GROUP = 'workflow_step_rule_group';
    public const string TEMPLATE = 'template';
    public const string TEMPLATE_ATTRIBUTE_GROUP = 'template_attribute_group';
    public const string UNIT = 'unit';

    // HUB RELATED
    public const string HUB_ORDER = 'hub_sales_order';
    public const string HUB_ORDER_ITEM = 'hub_sales_order_item';
    public const string HUB_ORDER_LOG = 'hub_sales_order_log';

    public const string HUB_SHIPMENT = 'hub_shipment';
    public const string HUB_STOCK = 'hub_stock';
    public const string HUB_STOCK_HISTORY = 'hub_stock_history';
    public const string HUB_SOURCE = 'hub_source';
    public const string ZIP = 'zip';
    public const string CHANNEL_MAPPING = 'channel_mapping';

    public const string CHANNEL = 'channel';
    public const string CHANNEL_WORKFLOW_EXPORT = 'channel_workflow_export';

    // CONFIG RELATED
    public const string CONFIG = 'config';
}
