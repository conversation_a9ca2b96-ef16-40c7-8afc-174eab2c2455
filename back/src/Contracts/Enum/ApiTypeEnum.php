<?php declare(strict_types=1);

namespace App\Contracts\Enum;

abstract class ApiTypeEnum
{
    public const string ADDRESS = 'Address';
    public const string ATTRIBUTE = 'Attribute';
    public const string ATTRIBUTE_GROUP = 'AttributeGroup';
    public const string ATTRIBUTE_OPTION = 'AttributeOption';
    public const string AUTH_METHOD = 'AuthMethod';
    public const string BREADCRUMB = 'Breadcrumb';
    public const string CATALOG = 'Catalog';
    public const string CATALOG_SCOPE = 'CatalogScope';
    public const string CATALOG_SCOPE_SUMMARY = 'CatalogScopeSummary';
    public const string CATALOG_SCOPE_WORKFLOW_STAT = 'CatalogScopeWorkflowStat';
    public const string CATALOG_SCOPE_WORKFLOW_STATUS = 'CatalogScopeWorkflowStatus';
    public const string CATEGORY = 'Category';
    public const string CATEGORY_DATA = 'CategoryData';
    public const string CATEGORY_ERROR = 'CategoryError';
    public const string COLLECTION = 'Collection';
    public const string COLUMN = 'Column';
    public const string COLUMNS = 'Columns';
    public const string COMPLETUDE = 'Completude';
    public const string CONTENT = 'Content';
    public const string CONTENT_FOLDER = 'ContentFolder';
    public const string CONTENT_DATA = 'ContentData';
    public const string CONTENT_ERROR = 'ContentError';
    public const string COUNTERS = 'Counters';
    public const string CSV = 'Csv';
    public const string CURRENCY = 'Currency';
    public const string DICTIONARY = 'Dictionary';
    public const string DICTIONARY_MAPPING = 'DictionaryMapping';
    public const string FILE_EXTRACTOR = 'FileExtractor';
    public const string FILE_UNIQUE = 'FileUnique';
    public const string FILE_UNIQUENESS = 'FileUniqueness';
    public const string FILTERS = 'Filters';
    public const string FILTERS_RESOLVE = 'FiltersResolve';
    public const string HUB_ORDER = 'HubOrder';
    public const string HUB_ORDER_ITEM = 'HubOrderItem';
    public const string HUB_ORDER_SUMMARY = 'HubOrderSummary';
    public const string HUB_SHIPMENT = 'HubShipment';
    public const string HUB_STOCK = 'HubStock';
    public const string HUB_STOCK_HISTORY = 'HubStockHistory';
    public const string HUB_SOURCE = 'HubSource';
    public const string INTEGRATION = 'Integration';
    public const string LIGHT_CATALOG_SCOPE = 'LightCatalogScope';
    public const string LIGHT_LOCALE = 'LightLocale';
    public const string LIGHT_RULE = 'LightRule';
    public const string LIGHT_SCOPE = 'LightScope';
    public const string LOCALE = 'Locale';
    public const string MEASURE_FAMILY = 'MeasureFamily';
    public const string MEDIA = 'Media';
    public const string MEDIA_FOLDER = 'MediaFolder';
    public const string MEDIA_DATA = 'MediaData';
    public const string MEDIA_ERROR = 'MediaError';
    public const string ORDER = 'Order';
    public const string ORDERS = 'Orders';
    public const string ORDER_POSITION = 'OrderPosition';
    public const string PASSWORD_RESET = 'PasswordReset';
    public const string PASSWORD_RESET_START = 'PasswordResetStart';
    public const string PASSWORD_RESET_TOKEN = 'PasswordResetToken';
    public const string PASSWORD_UPDATE = 'PasswordUpdate';
    public const string PLATFORM_STATISTIC = 'PlatformStatistic';
    public const string PRODUCT = 'Product';
    public const string PRODUCT_DATA = 'ProductData';
    public const string PRODUCT_ERROR = 'ProductError';
    public const string PRODUCT_SCOPE = 'ProductScope';
    public const string REQUESTABLE_USER_GROUP = 'RequestableUserGroup';
    public const string RULE = 'Rule';
    public const string RULE_GROUP = 'RuleGroup';
    public const string RULE_TEST = 'RuleTest';
    public const string RULE_TEST_RESULT = 'RuleTestResult';
    public const string SCOPE = 'Scope';
    public const string SETTINGS = 'Settings';
    public const string TEMPLATE = 'Template';
    public const string THUMBNAIL_FORMAT = 'ThumbnailFormat';
    public const string TOKEN = 'Token';
    public const string TOKEN_RENEW = 'TokenRenew';
    public const string TRANSLATION = 'Translation';
    public const string TRANSLATIONS = 'Translations';
    public const string UNIT = 'Unit';
    public const string UPLOAD = 'Upload';
    public const string UPLOAD_EXPORT = 'UploadExport';
    public const string USER = 'User';
    public const string USER_GROUP = 'UserGroup';
    public const string USER_GROUP_MEMBERSHIP = 'UserGroupMembership';
    public const string USER_GROUP_MEMBERSHIP_REQUEST = 'UserGroupMembershipRequest';
    public const string USER_GROUP_MEMBERSHIP_REQUEST_HANDLE = 'UserGroupMembershipRequestHandle';
    public const string USER_GROUP_MEMBERSHIP_REQUEST_SUMMARY = 'UserGroupMembershipRequestSummary';
    public const string USER_GROUP_SCOPE = 'UserGroupScope';
    public const string USER_LOGIN = 'UserLogin';
    public const string VALUE = 'Value';
    public const string VALUES = 'Values';
    public const string VALUE_DATA = 'ValueData';
    public const string VIEW = 'View';
    public const string WORKFLOW = 'Workflow';
    public const string WORKFLOW_BUILDER = 'WorkflowBuilder';
    public const string WORKFLOW_GLOBAL_STAT = 'WorkflowGlobalStat';
    public const string WORKFLOW_RULE_RESULT = 'WorkflowRuleResult';
    public const string WORKFLOW_SPECIFIC_STAT = 'WorkflowSpecificStat';
    public const string WORKFLOW_SPECIFIC_STAT_MODEL = 'WorkflowSpecificStatModel';
    public const string WORKFLOW_STEP = 'WorkflowStep';
    public const string WORKFLOW_STEP_RULE_GROUP = 'WorkflowStepRuleGroup';
    public const string WORKFLOW_STEP_SUMMARY = 'WorkflowStepSummary';
    public const string WORKFLOW_TEST = 'WorkflowTest';
    public const string WORKFLOW_TEST_RESULT = 'WorkflowTestResult';
    public const array ALL = [
        self::ADDRESS,
        self::ATTRIBUTE,
        self::ATTRIBUTE_GROUP,
        self::ATTRIBUTE_OPTION,
        self::AUTH_METHOD,
        self::BREADCRUMB,
        self::CATALOG,
        self::CATALOG_SCOPE,
        self::CATALOG_SCOPE_SUMMARY,
        self::CATALOG_SCOPE_WORKFLOW_STAT,
        self::CATALOG_SCOPE_WORKFLOW_STATUS,
        self::CATEGORY,
        self::CATEGORY_DATA,
        self::CATEGORY_ERROR,
        self::COLLECTION,
        self::COLUMN,
        self::COLUMNS,
        self::CONTENT,
        self::CONTENT_FOLDER,
        self::CONTENT_DATA,
        self::CONTENT_ERROR,
        self::COUNTERS,
        self::CSV,
        self::CURRENCY,
        self::FILE_EXTRACTOR,
        self::FILE_UNIQUE,
        self::FILE_UNIQUENESS,
        self::FILTERS,
        self::FILTERS_RESOLVE,
        self::HUB_ORDER,
        self::HUB_ORDER_ITEM,
        self::HUB_ORDER_SUMMARY,
        self::HUB_SHIPMENT,
        self::HUB_STOCK,
        self::HUB_STOCK_HISTORY,
        self::HUB_SOURCE,
        self::LIGHT_CATALOG_SCOPE,
        self::LIGHT_LOCALE,
        self::LIGHT_RULE,
        self::LIGHT_SCOPE,
        self::LOCALE,
        self::MEASURE_FAMILY,
        self::MEDIA,
        self::MEDIA_FOLDER,
        self::MEDIA_DATA,
        self::MEDIA_ERROR,
        self::ORDER,
        self::ORDERS,
        self::ORDER_POSITION,
        self::PASSWORD_RESET,
        self::PASSWORD_RESET_START,
        self::PASSWORD_RESET_TOKEN,
        self::PASSWORD_UPDATE,
        self::PLATFORM_STATISTIC,
        self::PRODUCT,
        self::PRODUCT_DATA,
        self::PRODUCT_ERROR,
        self::PRODUCT_SCOPE,
        self::REQUESTABLE_USER_GROUP,
        self::RULE,
        self::RULE_GROUP,
        self::RULE_TEST,
        self::RULE_TEST_RESULT,
        self::SCOPE,
        self::SETTINGS,
        self::TEMPLATE,
        self::THUMBNAIL_FORMAT,
        self::TOKEN,
        self::TOKEN_RENEW,
        self::TRANSLATION,
        self::TRANSLATIONS,
        self::UNIT,
        self::UPLOAD,
        self::UPLOAD_EXPORT,
        self::USER,
        self::USER_GROUP,
        self::USER_GROUP_MEMBERSHIP,
        self::USER_GROUP_MEMBERSHIP_REQUEST,
        self::USER_GROUP_MEMBERSHIP_REQUEST_HANDLE,
        self::USER_GROUP_MEMBERSHIP_REQUEST_SUMMARY,
        self::USER_GROUP_SCOPE,
        self::USER_LOGIN,
        self::VALUE,
        self::VALUES,
        self::VALUE_DATA,
        self::VIEW,
        self::WORKFLOW,
        self::WORKFLOW_BUILDER,
        self::WORKFLOW_GLOBAL_STAT,
        self::WORKFLOW_RULE_RESULT,
        self::WORKFLOW_SPECIFIC_STAT,
        self::WORKFLOW_SPECIFIC_STAT_MODEL,
        self::WORKFLOW_STEP,
        self::WORKFLOW_STEP_RULE_GROUP,
        self::WORKFLOW_STEP_SUMMARY,
        self::WORKFLOW_TEST,
        self::WORKFLOW_TEST_RESULT,
    ];
    public const array DOCUMENTS = [
        self::PRODUCT,
        self::CONTENT,
        self::CATEGORY,
        self::MEDIA,
    ];
}
