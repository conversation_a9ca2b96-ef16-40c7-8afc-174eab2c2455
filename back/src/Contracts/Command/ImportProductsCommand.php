<?php declare(strict_types=1);

namespace App\Contracts\Command;

use App\Contracts\Enum\ApiTypeEnum;
use App\Contracts\Event\DocumentsImportEvent;
use App\Module\Cache\DoctrineCache;
use App\Utils\ArrUtils;
use App\Utils\DateUtils;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Throwable;

#[AsCommand('app:contracts:import:products')]
final class ImportProductsCommand extends Command
{
    public function __construct(
        private readonly DoctrineCache $doctrineCache,
        private readonly EventDispatcherInterface $dispatcher,
    ) {
        parent::__construct();
    }

    public function configure(): void
    {
        $this
            ->addOption('since', null, InputOption::VALUE_OPTIONAL, 'Valid php datetime format.')
            ->addArgument('skus', InputArgument::IS_ARRAY, 'Product skus to import.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>importing</info>');

        if (null !== $since = $input->getOption('since')) {
            try {
                $since = DateUtils::new($since);
                $output->writeln("\tsince: {$since->format('Y-m-d H:i:s')}");
            } catch (Throwable) {
                $since = null;
            }
        }

        $uuids = $this->doctrineCache->products->getUuids(ArrUtils::vfu($input->getArgument('skus')));

        $this->dispatcher->dispatch(new DocumentsImportEvent(
            type: ApiTypeEnum::PRODUCT,
            uuids: $uuids->isEmpty() ? null : $uuids,
            since: $since ?? null,
        ));

        return self::SUCCESS;
    }
}
