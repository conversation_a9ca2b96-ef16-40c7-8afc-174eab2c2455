<?php declare(strict_types=1);

namespace App\Contracts;

use App\Bridge\Platform\Environment;
use App\Bridge\Security\Security;
use App\Bridge\Translation\Entity\Translations;
use App\Contracts\Api\Model\HeaderDefinitionFilterModel;
use App\Contracts\Api\Resource\HeaderDefinition;
use App\Contracts\Enum\ApiTypeEnum;
use App\Contracts\Enum\LangEnum;
use App\Contracts\Enum\TableEnum;
use App\Contracts\Model\Header;
use App\Contracts\Type\Enum\AttributeTypeEnum;
use App\Module\Cache\DoctrineCache;
use App\Utils\StrUtils;
use Doctrine\ORM\EntityManagerInterface;
use Generator;
use Symfony\Contracts\Translation\TranslatorInterface;
use Throwable;
use function count;
use function in_array;

/**
 * @todo cache
 */
final readonly class HeaderDefinitions
{
    public function __construct(
        private EntityManagerInterface $em,
        private Security $security,
        private Environment $environment,
        private TranslatorInterface $translator,
        private DoctrineCache $doctrineCache,
    ) {
    }

    /**
     * @return Generator<HeaderDefinition>
     */
    public function getPropertyDefinitions(HeaderDefinitionFilterModel $filter): Generator
    {
        $default = function (
            string $property,
            string $type = AttributeTypeEnum::TEXT,
            bool $isSearchable = true,
        ) use ($filter): HeaderDefinition {
            $translations = Translations::fromArray([
                LangEnum::FR => $this->translator->trans("items.{$filter->documentType}.{$property}", locale: LangEnum::FR),
                LangEnum::EN => $this->translator->trans("items.{$filter->documentType}.{$property}", locale: LangEnum::EN),
            ]);

            return new HeaderDefinition(
                label: $this->trans($translations),
                header: Header::from($property),
                names: $translations,
                type: $type,
                isSearchable: $isSearchable,
                isLocalizable: false,
                isScopable: false,
            );
        };

        $definitions = match ($filter->documentType) {
            ApiTypeEnum::PRODUCT => [
                $default('uuid'),
                $default('sku', isSearchable: false),
                $default('status', isSearchable: false),
                $default('createdAt', type: AttributeTypeEnum::DATE, isSearchable: false),
                $default('updatedAt', type: AttributeTypeEnum::DATE, isSearchable: false),
            ],
            // @todo other types
            // @todo croiser les problématiques avec le listing
            // @todo croiser les problématiques avec le formulaire
            default => [],
        };

        foreach ($definitions as $definition) {
            if ($this->match($definition, $filter)) {
                yield $definition;
            }
        }
    }

    /**
     * @return Generator<HeaderDefinition>
     */
    public function getAttributeDefinitions(HeaderDefinitionFilterModel $filter): Generator
    {
        try {
            $tableAttribute = TableEnum::ATTRIBUTE;
            $tableAttributeGroup = TableEnum::ATTRIBUTE_GROUP;

            $query = <<<SQL
            SELECT
                a.code,
                a.names_fr,
                a.names_en,
                a.type,
                a.is_searchable,
                a.is_localizable,
                a.is_scopable
            FROM {$tableAttribute} a
            LEFT JOIN {$tableAttributeGroup} ag ON ag.code = a.attribute_group_code
            WHERE (
                ag.type = ?
                OR ag.type IS NULL
            )
            SQL;

            $results = $this->em->getConnection()->executeQuery($query, [$filter->documentType]);
            while (false !== $row = $results->fetchAssociative()) {
                $translations = Translations::fromArray([
                    LangEnum::FR => $row['names_fr'],
                    LangEnum::EN => $row['names_en'],
                ]);

                $definition = new HeaderDefinition(
                    label: $this->trans($translations),
                    header: Header::attribute($row['code']),
                    names: $translations,
                    type: $row['type'],
                    isSearchable: (bool)$row['is_searchable'],
                    isLocalizable: (bool)$row['is_localizable'],
                    isScopable: (bool)$row['is_scopable'],
                );

                yield from $this->getScopedLocalizedDef($definition, $filter);
            }
        } catch (Throwable) {
        }
    }

    /**
     * @return Generator<HeaderDefinition>
     * @todo
     */
    public function getWorkflowDefinitions(HeaderDefinitionFilterModel $filter): Generator
    {
        yield from [];
    }

    /**
     * @return Generator<HeaderDefinition>
     * @todo
     */
    public function getCompletudeDefinitions(HeaderDefinitionFilterModel $filter): Generator
    {
        yield from [];
    }

    private function trans(Translations $names): string
    {
        $lang = $this->security->user?->preferences->lang;
        $lang ??= $this->environment->getDefaultLang();

        return $this->environment->trans($names, $lang);
    }

    private function match(HeaderDefinition $definition, HeaderDefinitionFilterModel $filter): bool
    {
        if (
            null !== $filter->q
            && !(StrUtils::matchQ($definition->label, $filter->q) || StrUtils::matchQ($definition->header->toString(), $filter->q))
        ) {
            return false;
        }
        if (0 !== count($filter->code) && !in_array($definition->getCode(), $filter->code, true)) {
            return false;
        }
        if (null !== $filter->type && $filter->type !== $definition->type) {
            return false;
        }
        if (null !== $filter->isSearchable && $filter->isSearchable !== $definition->isSearchable) {
            return false;
        }
        if (null !== $filter->isLocalizable && $filter->isLocalizable !== $definition->isLocalizable) {
            return false;
        }
        if (null !== $filter->isScopable && $filter->isScopable !== $definition->isScopable) {
            return false;
        }

        return true;
    }

    private function getScopedLocalizedDef(HeaderDefinition $definition, HeaderDefinitionFilterModel $filter): Generator
    {
        $scopes = [null];
        $locales = [null];

        if ($definition->isLocalizable) {
            $locales = $this->doctrineCache->locales->getCodes();
        }

        if ($definition->isScopable) {
            $scopes = $this->doctrineCache->scopes->getCodes();
        }

        foreach ($locales as $locale) {
            foreach ($scopes as $scope) {
                $def = clone $definition;
                $def->header = Header::attribute(
                    code: $definition->header->code,
                    locale: $locale,
                    scope: $scope,
                );

                if ($this->match($def, $filter)) {
                    yield $def;
                }
            }
        }
    }
}
