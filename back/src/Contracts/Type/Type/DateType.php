<?php declare(strict_types=1);

namespace App\Contracts\Type\Type;

use App\Bridge\Flat\Model\Value;
use App\Bridge\Flat\Model\ValueData;
use App\Contracts\Model\Data;
use App\Contracts\Model\Header;
use App\Contracts\Type\Enum\AttributeTypeEnum;
use App\Contracts\Type\Traits\NumberOperatorsTrait;
use App\Module\Injector\Model\DataImportItem;
use App\Utils\DateUtils;
use Generator;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use function is_string;

class DateType extends AbstractType
{
    use NumberOperatorsTrait;

    public static function getType(): string
    {
        return AttributeTypeEnum::DATE;
    }

    public function isSearchable(): bool
    {
        return true;
    }

    public function fromScalar(Header $header, string $string): Generator
    {
        // @todo debug
        if (null !== $date = DateUtils::new($string)) {
            yield new DataImportItem(value: $date);
        }
    }

    public function toScalar(Uuid $uuid, Header $header, Value $value): Generator
    {
        foreach ($value->data as $data) {
            if (is_string($data->data)) {
                if (null !== $date = DateUtils::new($data->data)) {
                    yield $date->format('Y-m-d');
                }
            }
        }
    }

    public function toValueData(Data $data): ?ValueData
    {
        return ValueData::fromData($data);
    }

    public function getFlatConstraints(string $code): Generator
    {
        yield new Assert\NotBlank();
        yield new Assert\Callback(function (mixed $object, ExecutionContextInterface $context, mixed $payload): void {
            if (is_string($payload) && null === DateUtils::new($payload)) {
                $context->addViolation('date.invalid_format');
            }
        });
    }
}
