<?php declare(strict_types=1);

namespace App\Contracts\Type;

use App\Bridge\Filter\Enum\OperatorEnum;
use App\Bridge\Flat\Model\Value;
use App\Bridge\Flat\Model\ValueData;
use App\Bridge\Flat\Model\Values;
use App\Contracts\Enum\LoggerChannelEnum;
use App\Contracts\Model\Data;
use App\Contracts\Model\Header;
use App\Contracts\Type\Enum\AttributeTypeEnum;
use App\Contracts\Type\Type\TypeDataInterface;
use App\Contracts\Type\Type\TypeInterface;
use App\Module\Cache\DoctrineCache;
use App\Module\Injector\Model\DataImport;
use App\Utils\DateUtils;
use App\Utils\StrUtils;
use Generator;
use Monolog\Attribute\WithMonologChannel;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Component\DependencyInjection\Attribute\TaggedLocator;
use Symfony\Component\DependencyInjection\ServiceLocator;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Validator\Constraint;
use Throwable;
use function array_filter;
use function array_values;
use function count;
use function explode;
use function implode;
use function is_array;
use function is_bool;
use function is_float;
use function is_int;
use function is_numeric;
use function is_string;
use function iterator_to_array;
use function var_export;
use const PHP_EOL;

#[WithMonologChannel(LoggerChannelEnum::TYPE)]
final readonly class AttributeTypes implements TypeDataInterface
{
    /**
     * @param ServiceLocator<TypeInterface> $types
     */
    public function __construct(
        #[TaggedLocator(TypeInterface::class, defaultIndexMethod: 'getType')]
        private ServiceLocator $types,
        private DoctrineCache $doctrineCache,
        private LoggerInterface $logger,
    ) {
    }

    public static function scalarToArray(string $value): array
    {
        $array = [];
        foreach (explode(TypeInterface::COLLECTION_SEPARATOR, $value) as $item) {
            if (null !== $item = StrUtils::nullOrString($item)) {
                $array[] = $item;
            }
        }

        return $array;
    }

    public function arrayToScalar(?array $values): string
    {
        // sometimes xml connectors are sending multidimentional arrays
        try {
            if (null === $values) {
                return '';
            }

            if (array_any($values, static fn($value): bool => !is_string($value))) {
                throw new RuntimeException('trying to convert an array with non string data');
            }

            return implode(
                TypeInterface::COLLECTION_SEPARATOR,
                $values,
            );
        } catch (Throwable $e) {
            $this->logger->error('impossible array to scalar conversion', [
                'values' => $values,
                $e,
            ]);
        }

        return '';
    }

    /**
     * @todo log error?
     * @phpstan-return Generator<DataImport>
     */
    public function fromScalar(Uuid $uuid, Header $header, int|float|string|array|null $string): Generator
    {
        $string ??= '';

        if (is_array($string)) {
            $string = $this->arrayToScalar($string);
        }

        if (is_int($string) || is_float($string)) {
            $string = (string)$string;
        }

        if (null === $type = $this->doctrineCache->attributes->codeToType($header->code)) {
            return;
        }

        try {
            yield DataImport::fromHeader(
                $uuid,
                $header,
                iterator_to_array($this->types->get($type)->fromScalar($header, $string), false),
            );
        } catch (Throwable $e) {
            $this->logger->error('unable to generate dataImport from scalar', [
                'exception' => $e,
                'uuid' => $uuid->toRfc4122(),
                'header' => $header,
            ]);
        }
    }

    /**
     * @throws Throwable
     * @throws RuntimeException
     * @todo uuid seems really useless.
     */
    public function toScalar(Uuid $uuid, Header $header, Values $values, bool $throwOnMissing = false): string
    {
        // @todo log error
        $scalars = [];
        if (
            (null !== $value = $values->find($header->code))
            && (null !== $type = $this->doctrineCache->attributes->codeToType($value->code))
        ) {
            // filter only values for this locale and this scope
            // and create a new `Value` with it
            $localeAndScopeOnlyData = array_values(array_filter(
                $value->data,
                static fn(ValueData $valueData): bool => $valueData->match($header->localeCode, $header->scopeCode)),
            );

            $localeAndScopeOnly = new Value($value->code, $localeAndScopeOnlyData);

            try {
                foreach ($this->types->get($type)->toScalar($uuid, $header, $localeAndScopeOnly) as $scalar) {
                    $scalars[] = $scalar;
                }
            } catch (Throwable $e) {
                if ($throwOnMissing) {
                    throw $e;
                }

                $this->logger->error('unable to return scalar from values', [
                    'exception' => $e,
                    'uuid' => $uuid->toRfc4122(),
                    'header' => $header,
                ]);
            }
        }

        if ($throwOnMissing && 0 === count($scalars)) {
            throw new RuntimeException('unable to return scalar from values');
        }

        return implode(PHP_EOL, $scalars);
    }

    public function toValueData(Data $data): ?ValueData
    {
        // @todo log error
        if (null !== $type = $this->doctrineCache->attributes->codeToType($data->attributeCode)) {
            try {
                return $this->types->get($type)->toValueData($data);
            } catch (Throwable $e) {
                $this->logger->error('unable to generate valueData from data', [
                    'exception' => $e,
                    'data' => $data,
                ]);
            }
        }

        return null;
    }

    public function generateData(Uuid $uuid, Header $header, ValueData $data): DataImport
    {
        if (null !== $type = $this->doctrineCache->attributes->codeToType($header->code)) {
            try {
                return $this->types->get($type)->generateData($uuid, $header, $data);
            } catch (Throwable $e) {
                $this->logger->error('unable to generate data from value', [
                    $e,
                    'uuid' => $uuid->toRfc4122(),
                    'header' => $header->toString(),
                    'data' => var_export($data, true),
                ]);
            }
        }

        return DataImport::fromHeader($uuid, $header)->killMe();
    }

    /** @return Generator<Constraint> */
    public function getFlatConstraints(string $code, string $type): Generator
    {
        try {
            yield from $this->types->get($type)->getFlatConstraints($code);
        } catch (Throwable $e) {
            $this->logger->error('unable to generate flat constraints', [
                'exception' => $e,
                'code' => $code,
                'type' => $type,
            ]);
        }
    }

    public function isMultiple(string $type): bool
    {
        try {
            return $this->types->get($type)->isMultiple();
        } catch (Throwable $e) {
            $this->logger->error('unable to check if type is multiple', [
                'exception' => $e,
                'type' => $type,
            ]);
        }

        return false;
    }

    public function hasOption(string $type): bool
    {
        try {
            return $this->types->get($type)->hasOption();
        } catch (Throwable $e) {
            $this->logger->error('unable to check if type has option', [
                'exception' => $e,
                'type' => $type,
            ]);
        }

        return false;
    }

    public function isSearchable(string $type): bool
    {
        try {
            return $this->types->get($type)->isSearchable();
        } catch (Throwable $e) {
            $this->logger->error('unable to check if type is searchable', [
                'exception' => $e,
                'type' => $type,
            ]);
        }

        return false;
    }

    public function getOperatorsFor(string $type): array
    {
        try {
            return $this->types->get($type)->getOperators();
        } catch (Throwable $e) {
            $this->logger->error('unable to get operators for type', [
                'exception' => $e,
                'type' => $type,
            ]);
        }

        return [];
    }

    /**
     * @todo MIGRATE to type services
     */
    public static function supports(string $type, string $operator, mixed $data): bool
    {
        $isArray = function (mixed $data, callable $fn): bool {
            if (!is_array($data)) {
                return false;
            }

            foreach ($data as $item) {
                if (!$fn($item)) {
                    return false;
                }
            }

            return true;
        };

        $isNumeric = fn(mixed $data): bool => is_numeric($data) || (is_string($data) && ctype_digit($data));

        $isDate = function (mixed $data): bool {
            try {
                if (is_string($data)) {
                    // @todo would be better to do
                    // false !== DateTime::createFromFormat('c', $data)
                    // but it generates an error
                    // https://bugs.php.net/bug.php?id=51950
                    DateUtils::new($data);

                    return true;
                }
            } catch (Throwable) {
            }

            return false;
        };

        // two operator without values or with a boolean
        if (OperatorEnum::EXISTS === $operator) {
            return is_bool($data);
        }

        // @todo we can improve this for dates/bool/...
        return match ($type) {
            AttributeTypeEnum::TEXT,
            AttributeTypeEnum::TEXTAREA,
            AttributeTypeEnum::MAIL,
            AttributeTypeEnum::LINK,
            AttributeTypeEnum::COLOR,
            AttributeTypeEnum::SELECT => match ($operator) {
                OperatorEnum::IN => $isArray($data, is_string(...)),
                default => is_string($data),
            },
            AttributeTypeEnum::NUMBER,
            AttributeTypeEnum::DECIMAL => match ($operator) {
                OperatorEnum::IN => $isArray($data, $isNumeric),
                default => $isNumeric($data),
            },
            AttributeTypeEnum::DATE => $isDate($data),
            AttributeTypeEnum::SWITCH => is_bool($data),
            AttributeTypeEnum::MULTISELECT => match ($operator) {
                OperatorEnum::INTERSECT_LEAST_ONE => $isArray($data, is_string(...)),
                default => is_string($data),
            },
            default => false,
        };
    }
}
