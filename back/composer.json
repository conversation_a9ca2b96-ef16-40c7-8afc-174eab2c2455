{"type": "project", "license": "proprietary", "require": {"php": "^8.4", "ext-bcmath": "*", "ext-ctype": "*", "ext-curl": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-ftp": "*", "ext-gd": "*", "ext-iconv": "*", "ext-imap": "*", "ext-intl": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-ssh2": "*", "ext-tidy": "*", "ext-zip": "*", "api-platform/core": "3.2.*", "beberlei/doctrineextensions": "^1.3", "composer/package-versions-deprecated": "1.11.99.*", "doctrine/annotations": "^1.0", "doctrine/cache": "1.12.*", "doctrine/doctrine-bundle": "^2.4", "doctrine/doctrine-migrations-bundle": "^3.1", "doctrine/orm": "^2.12", "dompdf/dompdf": "^3.1", "dragonmantank/cron-expression": "^3.3", "dunglas/doctrine-json-odm": "^1.3", "elasticsearch/elasticsearch": "^8.12", "guzzlehttp/guzzle": "^7", "guzzlehttp/oauth-subscriber": "^0.5.0", "knpuniversity/oauth2-client-bundle": "^2.10", "league/flysystem-aws-s3-v3": "^3.28", "league/flysystem-azure-blob-storage": "^3.0", "league/flysystem-ftp": "^3.0", "league/flysystem-path-prefixing": "*", "league/oauth2-google": "^4.0", "nelmio/cors-bundle": "^2.3", "phpdocumentor/reflection-docblock": "^5.2", "phpoffice/phpspreadsheet": "^2.0", "phpstan/phpdoc-parser": "^1.24", "scienta/doctrine-json-functions": "^6.1", "sentry/sentry-symfony": "^5.0", "symfony/asset": "6.4.*", "symfony/cache": "6.4.*", "symfony/console": "6.4.*", "symfony/dependency-injection": "6.4.*", "symfony/doctrine-messenger": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/expression-language": "6.4.*", "symfony/filesystem": "6.4.*", "symfony/flex": "^1.3.1", "symfony/framework-bundle": "6.4.*", "symfony/http-client": "6.4.*", "symfony/http-foundation": "6.4.*", "symfony/http-kernel": "6.4.*", "symfony/intl": "6.4.*", "symfony/lock": "6.4.*", "symfony/mailchimp-mailer": "6.4.*", "symfony/mailer": "6.4.*", "symfony/messenger": "6.4.*", "symfony/mime": "6.4.*", "symfony/monolog-bundle": "^3.6", "symfony/process": "6.4.*", "symfony/property-access": "6.4.*", "symfony/property-info": "6.4.*", "symfony/proxy-manager-bridge": "6.4.*", "symfony/rate-limiter": "6.4.*", "symfony/redis-messenger": "6.4.*", "symfony/runtime": "6.4.*", "symfony/scheduler": "6.4.*", "symfony/security-bundle": "6.4.*", "symfony/serializer": "6.4.*", "symfony/string": "6.4.*", "symfony/translation": "6.4.*", "symfony/translation-contracts": "^3.5", "symfony/twig-bundle": "6.4.*", "symfony/uid": "6.4.*", "symfony/validator": "6.4.*", "symfony/web-link": "6.4.*", "symfony/workflow": "6.4.*", "symfony/yaml": "6.4.*", "twig/cssinliner-extra": "^3.8", "twig/extra-bundle": "^3.8", "twig/intl-extra": "^3.20", "twig/markdown-extra": "^3.8", "twig/twig": "^2.12|^3.0"}, "require-dev": {"dama/doctrine-test-bundle": "*", "doctrine/doctrine-fixtures-bundle": "^3.6", "hautelook/alice-bundle": "^2.13", "phpstan/phpstan": "^2.0", "phpstan/phpstan-doctrine": "^2.0", "phpstan/phpstan-symfony": "^2.0", "justinrainbow/json-schema": "^6.0", "phpstan/extension-installer": "^1.3", "phpunit/phpunit": "^9.5", "symfony/browser-kit": "6.4.*", "symfony/css-selector": "6.4.*", "symfony/debug-bundle": "6.4.*", "symfony/maker-bundle": "^1.61", "symfony/phpunit-bridge": "^7.0", "symfony/stopwatch": "6.4.*", "symfony/web-profiler-bundle": "6.4.*", "zenstruck/foundry": "^2.1"}, "config": {"preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"symfony/flex": true, "composer/package-versions-deprecated": true, "symfony/runtime": true, "endroid/installer": true, "phpstan/extension-installer": true, "php-http/discovery": true}}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-intl-grapheme": "*", "symfony/polyfill-intl-idn": "*", "symfony/polyfill-intl-normalizer": "*", "symfony/polyfill-mbstring": "*", "symfony/polyfill-php83": "*", "symfony/polyfill-php82": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": true, "require": "6.4.*"}}, "minimum-stability": "dev", "prefer-stable": true}