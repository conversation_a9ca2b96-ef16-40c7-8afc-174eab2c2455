attribute:
  dont_exist: Cet attribut n'existe pas (%code%).
locale:
  dont_exist: Cette locale n'existe pas (%code%).
scope:
  dont_exist: Ce scope n'existe pas (%code%).
attribute_group:
  dont_exist: Ce groupe d'attributs n'existe pas (%code%).
  type_unmapped_template_type: Le type "%ag_type%" ne peut être appliqué. Ce groupe est lié au modèle "%template_code%" de type "%template_type%".
attribute_option:
  not_valid_attribute: L'attribut sélectionné ne permet pas de lui associer des options.
channel:
  does_not_exists: Le channel "%code%" n'existe pas.
catalog:
  does_not_exists: Le catalogue "%code%" n'existe pas.
  no_permission: "Vous n'avez pas les autorisations nécessaires."
category:
  dont_exist: Cette catégorie n'existe pas (%uuid%)
cron:
  expression_invalid: Ce cron n'est pas valide.
name:
  chars:
    authorized: Le nom ne doit pas contenir de "%symbol%".
code:
  chars:
    authorized: Le code ne doit contenir que des lettres minuscules ou majuscules, des chiffres et des tirets milieu/bas.
    unique: Le code doit être unique.
hub:
  source:
    dont_exist: Cette source n'existe pas (%code%).
  shipment:
    order_related: L'expédition doit être liée à une commande.
    sku_related: L'expédition doit contenir uniquement des skus liés à la commande.
    too_much_quantity: L'expédition ne peut contenir plus d'articles que commandés.
    must_be_full: L'expédition doit contenir autant d'articles que commandés.
    must_finish: L'expédition doit contenir tous les articles restants.
    must_be_partial: Une expédition partielle ne doit pas contenir tous les articles restants.
user:
  not_found: Utilisateur non trouvé.
  email:
    already_used: L'adresse e-mail est déjà utilisée.
  password:
    length: Votre mot de passe doit comporter au moins 8 caractères.
    uppercase: Votre mot de passe doit contenir une lettre majuscule.
    lowercase: Votre mot de passe doit contenir une lettre minuscule.
    number: Votre mot de passe doit contenir un chiffre.
    special: Votre mot de passe doit contenir un caractère spécial.
sku:
  chars:
    authorized: Le SKU ne doit contenir que des lettres minuscules ou majuscules, des chiffres et des tirets milieu/bas.
content:
  dont_exist: Ce Contenu n'existe pas (%uuid%).
media:
  dont_exist: Ce Media n'existe pas (%uuid%).
  unzip_media: Ce média ne peut pas être décompressé
product:
  dont_exist: Ce Produit n'existe pas (%uuid%).
template:
  match_type: Le groupe d'attributs "%code%" ne convient pas à ce modèle car il est de type "%type%".
measure_family:
  default: 'L''unité par défaut doit être l''une des suivantes : %codes%'
  dont_exist: Cette famille de mesures n'existe pas (%code%).
filter_structure:
  header: Le filtre doit avoir un header pour être valide.
  localizable: La propriété est localisable, vous devez fournir une locale.
  not_localizable: La propriété n'est pas localisable, vous ne pouvez pas fournir de locale.
  unsupported_type: Le type d'attribut n'est pas pris en charge.
  unsupported_operator: L'opérateur n'est pas pris en charge pour ce type d'attribut.
  unsupported_operator_value: L'opérateur/La valeur n'est pas pris en charge pour ce type d'attribut.
upload:
  formats: 'Format de fichier invalide (formats pris en charge : %formats%).'
  size: 'Taille de fichier invalide %size% (taille maximale : %max_size%).'
upload_import:
  upload: 'Upload doit être renseigné'
user_groups:
  parent:
    unmapped_attribute_group: 'Le groupe d''attribut "%wanted%" n''est pas lié dans le parent (lié: %mapped%)'
    unwritable_attribute_group: 'Le groupe d''attribut "%wanted%" n''est pas écrivable dans le parent (écrivable: %mapped%)'
    unmapped_catalog: 'Le catalogue "%wanted%" n''est pas lié dans le parent (lié: %mapped%)'
media_folders:
  name:
    unauthorized_chars: 'Le nom ne peut pas contenir de "/", de "\\" ou de ".".'
self_parent: Cannot be set as its own parent.
translations_messages:
  invalid: Format de messages invalide.
  unsupported: 'Langue non supportée.'
  default_lang: 'La traduction par défaut est nécessaire.'
  max_length: 'La valeur ne doit pas dépasser {{ length }} caractères.'
  min_length: 'La valeur doit comporter au moins {{ length }} caractères'
workflow:
  dont_exist: Ce workflow n'existe pas (%id%).
  rule: "Cette expression n'est pas une règle valide."
order:
  unsupported_type: "Type non pris en charge."
values:
  localizable_need_locale: "L'attribut localisable doit avoir une locale."
  no_localizable_no_locale: "L'attribut non localisable ne peut pas avoir de locale."
  invalid_locale: "La locale fournie ({{ locale }}) n'est pas valide."
  scopable_need_scope: "L'attribut scopable doit avoir un scope."
  no_scopable_no_scope: "L'attribut non scopable ne peut pas avoir de scope."
  invalid_scope: "Le scope fourni ({{ scope }}) n'est pas valide."
mapping:
  no_code: "Le mapping ne doit pas avoir de code."
  need_mapping: "Le mapping doit avoir un mapping."
  uuid_required: "Un mapping pour \"uuid\" est requis (ou \"sku\" dans le cas d’un produit)."
date:
  invalid_format: "Format de date invalide."
header:
  invalid_format: "Format d’en-tête invalide."
numeric:
  as_string: "Doit être numérique."
breadcrumb:
  path:
    exist: Le chemin "%path%" existe déjà.
  rule_group:
    dont_exist: Ce groupe de règle n'existe pas (%code%)
