attribute:
  dont_exist: This attribute does not exist (%code%).
locale:
  dont_exist: This locale does not exist (%code%).
scope:
  dont_exist: This scope does not exist (%code%).
attribute_group:
  dont_exist: This attribute group does not exist (%code%).
  type_unmapped_template_type: Type "%ag_type%" can't be set because this group belongs to template "%template_code%" of type "%template_type%".
attribute_option:
  not_valid_attribute: This attribute cannot be linked to options.
channel:
  does_not_exists: The "%code%" channel does not exists.
catalog:
  does_not_exists: The "%code%" catalog does not exists.
  no_permission: "You have no permission."
category:
  dont_exist: The "%uuid%" category does not exists.
cron:
  expression_invalid: Invalid cron expression.
name:
  chars:
    authorized: The name cannot contain a "%symbol%".
code:
  chars:
    authorized: Code should contain only lowercase or uppercase letters, numbers, hyphens and underscores.
    unique: Code must be unique.
hub:
  source:
    dont_exist: This source does not exist (%code%).
  shipment:
    order_related: The shipment must be related to an order.
    sku_related: The shipment must contain only SKUs related to the order.
    too_much_quantity: The shipment cannot contain more items than ordered.
    must_be_full: The shipment must contain as many items as ordered.
    must_finish: The shipment must contain all remaining items.
    must_be_partial: A partial shipment must not contain all remaining items.
user:
  not_found: User not found.
  email:
    already_used: Email already used.
  password:
    length: Your password must be at least 8 characters long.
    uppercase: Your password must contain an uppercase character.
    lowercase: Your password must contain a lowercase character.
    number: Your password must contain a number.
    special: Your password must contain a special character.
sku:
  chars:
    authorized: SKU should contain only lowercase or uppercase letters, numbers, hyphens and underscores.
content:
  dont_exist: This Content does not exist (%uuid%).
media:
  dont_exist: This Media does not exist (%uuid%).
  unzip_media: This Media can't be unzip 
product:
  dont_exist: This Product does not exist (%uuid%).
template:
  match_type: The attribute group "%code%" is not suitable for this model because it is of type "%type%".
measure_family:
  default: 'Default unit must be one of: %codes%'
  dont_exist: This measure family does not exist (%code%).
filter_structure:
  string: Filter property must be a string.
  type: Filter type must be a string.
  localizable: Property is localizable, you must provide a locale.
  not_localizable: Property is not localizable, you cannot provide a locale.
  unsupported_type: Attribute type is not supported.
  unsupported_operator: Operator is not supported for this attribute type.
  unsupported_operator_value: Operator/Value is not supported for this attribute type.
upload:
  formats: 'Invalid file format (supported files: %formats%).'
  size: 'Invalid file size %size% (max size: %max_size%).'
upload_import:
  upload: 'Upload must be set'
user_groups:
  parent:
    unmapped_attribute_group: 'Attribute group "%wanted%" is unmapped in parent group (mapped: %mapped%)'
    unwritable_attribute_group: 'Attribute group "%wanted%" is unwritable in parent group (writable: %mapped%)'
    unmapped_catalog: 'Catalog "%wanted%" is unmapped in parent group (mapped: %mapped%)'
media_folders:
  name:
    unauthorized_chars: 'Name cannot contain a "/", a "\\" or a ".".'
self_parent: Cannot be set as its own parent.
translations_messages:
  invalid: Invalid messages format.
  unsupported: 'Unsupported lang.'
  default_lang: 'The default translation is required.'
  max_length: 'Value must not exceed {{ length }} characters.'
  min_length: 'Value must be at least {{ length }} characters long.'
workflow:
  dont_exist: This workflow does not exist (%id%).
  rule: 'This expression is not a valid rule.'
order:
  unsupported_type: "Unsupported type."
values:
  localizable_need_locale: "Localizable attribute must have a locale."
  no_localizable_no_locale: "Non localizable attribute cannot have a locale."
  invalid_locale: "The locale provided ({{ locale }}) is not valid."
  scopable_need_scope: "Scopable attribute must have a scope."
  no_scopable_no_scope: "Non scopable attribute cannot have a scope."
  invalid_scope: "The scope provided ({{ scope }}) is not valid."
mapping:
  no_code: "embedded mapping should not have a code"
  need_mapping: "embedded mapping should have a mapping"
  uuid_required: "A mapping for \"uuid\" is required (or \"sku\" in case of a product)."
date:
  invalid_format: "Invalid date format."
header:
  invalid_format: "Invalid header format."
numeric:
  as_string: "Must be numeric"
breadcrumb:
  path:
    exist: Path "%path%" already exist.
rule_group:
  dont_exist: This rule group does not exist (%uuid%).
