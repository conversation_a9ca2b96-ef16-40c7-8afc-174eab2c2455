actions:
  accept: Accept
  activate: Show
  add: Add
  addColumn: 'Add column'
  add_all: 'Add all'
  add_condition: 'Add condition'
  add_file: 'Add file'
  add_image: 'Add image'
  add_name: 'Add name'
  add_not_found: 'Search not found: add "{{search}}" in {{identifier}}'
  add_reference: 'Add text then press Enter'
  add_sub_reference: 'Add sub-reference'
  add_to_preferences: 'Add to preferences'
  apply: Apply
  cancel: Cancel
  close: Close
  confirm: Confirm
  copied: Copied
  copy: Copy
  create: Create
  create_female: 'Create a'
  create_male: 'Create a'
  created: Created
  created_at: 'Created at'
  deactivate: Hide
  decline: Decline
  delete: Delete
  delete_all: 'Delete all'
  delete_permanently: 'Delete permanently?'
  delete_permanently_text: 'Are you sure you want to delete {{name}}?'
  delete_selection: 'Delete selection'
  deleted: Deleted
  download: Download
  drop_files: 'Drag and drop a file'
  duplicate: Duplicate
  edit: Update
  ended_at: Ended at
  erased_all: Clear
  exit: Exit
  export: Export
  export_all: 'Export all'
  export_selection: 'Export selection'
  import: Import
  imported: imported
  importing: importing
  insert: Insert
  launchSync: 'Are you sure you want to synchronize now?'
  loading: Loading
  logout: Logout
  moveDown: 'Move down'
  moveUp: 'Move up'
  open: Open
  openSettings: 'Open settings'
  reloadPage: Reload
  remove: Remove
  removePermText: 'Are you sure you want to remove {{name}}?'
  remove_all: 'Remove all'
  rename: Rename
  return: 'Go back'
  save: Save
  save_password: 'Save password'
  search: Search
  see: Show
  seeAll: 'Show all'
  seeLess: 'Show less'
  seeMore: 'Show more'
  select: Select
  select_columns: 'Select columns'
  select_images: 'Select images'
  select_redirection: 'Select marker target'
  send: Send
  ship: Ship
  sortBy: 'Sort by'
  sync: Synchronize
  syncLaunched: 'Synchronization launched!'
  sync_categories: 'Synchronize categories'
  sync_products: 'Synchronize products'
  task_detail: 'Task ended, see details'
  test: Test
  unzip: "Unzip file"
  unzip_text: "Unzip your file to replicate its folder structure in the Sinfin DAM"
  update: Update
  updateColor: 'Update color'
  updated: Updated
  updated_at: Updated At
  upload: Upload
  upload_file: 'Upload file'
  validate: Submit
common:
  account: Account
  actions: Actions
  active: Active
  all: All
  alt_filled: 'Alt text: "{{value}}".'
  alt_missing: 'Alt text missing.'
  alt_placeholder: 'Alternative text'
  alt_title: 'Alternative(s) text(s)'
  amount: Amount
  atLeast: 'At least'
  autre: other
  by: by
  card: Card
  categories: Categories
  characters: characters
  code: Code
  coming_soon: 'Coming soon...'
  created_at: 'Created at'
  delete: Delete
  error: 'An error has occurred'
  errorProducts: 'Products error!'
  error_tryLater: 'An error has occurred, please try again later'
  errors: Errors
  file: file
  files: files
  filters: Filters
  filters_subtitle: 'Filter results according to your needs'
  folders: Folder(s)
  from: from
  general: General
  global: Global
  hideEmptyAttributes: 'Hide Empty Attributes'
  hideEmptyScopes: 'Hide Empty Scopes'
  hideColumn: 'Hide column'
  image: image
  images: images
  informations: Details
  informations_commande: 'Order details'
  itemNotEditable: 'This item belongs to {{owner}} and cannot be deleted from the interface. This form only contains editable attributes.'
  lang: Language
  languages: Languages
  localizable: Localizable
  manage_columns_search_placeholder: 'Search an attribut'
  manage_columns_title: 'Manage columns'
  manual: manual
  marker: marker
  marker_helper: 'Click on the left preview where you want to add a marker'
  markers: markers
  more: more
  name: Name
  never: Never
  next: Next
  'no': 'No'
  noCatalog: 'No catalog'
  no_result: 'No result'
  no_result_subtitle: 'No result found for this search'
  noResult: N/A
  none: None
  notFound: 'Not found'
  notSpecified: 'Not specified'
  operator: Operator
  or: Or
  other: other
  others: others
  pim: Pim
  placeholder_filter_value: Value
  points: ':'
  preview: Preview
  redirection: Redirection
  reference: Reference
  requests: Requests
  reset: Reset
  rowsPerPage: 'Rows per page'
  scopable: Scopable
  searchTitle: Search
  searchUser: 'Search an user'
  searchable: Searchable
  size: Size
  sku: SKU
  skus: SKU(S)
  sort: 'Sort'
  status: Status
  stepCompleted: 'Step completed!'
  subRef: Sub-reference
  tags: tags
  theList: 'the list'
  title: Title
  tree: 'Tree view'
  type: Type
  updated_at: 'Updated at'
  usage: Usage
  uuid: UUID
  virtual_tour: 'Virtual Tour'
  warning: Warning
  warning_close_form: 'Are you sure you want to close the form? All unsaved changes will be lost.'
  warning_close_form_filter: 'It looks like one of your filters is empty, he will be deleted. Are you sure you want to close the form?'
  when: When
  words: words
  write: Write
  'yes': 'Yes'
  zip: Zip
components:
  ContentPicker:
    folders: Folders
    label: 'Select contents'
    labelSingular: 'Select a content'
    selectedContent: 'Selected content'
    selectedContents: 'Selected contents'
  ContentsAttributeGroupsSelection:
    maxFiles: 'Max {{max}} files'
    text: 'Drag & drop your files here or click to select files'
    title: 'Select attribute groups'
  ContentsTemplateSelection:
    select_template: 'Select this template'
    title: 'Select a template'
  DataTable:
    columns:
      _: Columns
      hide_column: 'Hide column'
      manage_columns_search_placeholder: 'Search an attribut'
      manage_columns_title: 'Manage columns'
    density:
      _: Density
      compact: 'Compact'
      comfortable: 'Comfortable'
      standard: 'Standard'
    sort:
      _: Sort
      asc: 'Ascending'
      desc: 'Descending'
      unsort: 'Unsort'
  DateRangePicker:
    currentMonth: 'Current month'
    lastWeek: 'Last week'
    last7Days: 'Last 7 days'
    lastMonth: 'Last month'
    nextMonth: 'Next month'
    thisWeek: 'This week'
    thisYear: 'This year'
    today: 'Today'
    placeholder: 'Dates'
  Dropzone:
    formats: 'Formats : {{ formats }}'
    maxFiles: 'Max. files : {{ max }}'
    text: 'Drop files here or click to upload'
  FormCategory:
    filters: Filters
    main: Main
  TableColumnsPanel:
    reset: Reset
    show_hide_all: Show/Hide all
  MediaPicker:
    file: File
    files: Files
    folders: Folders
    label: 'Select medias'
    labelSingular: 'Select a media'
    selectedMedia: 'Selected media'
    selectedMedias: 'Selected medias'
  ProductPicker:
    label: 'Select products'
    labelSingular: 'Select a product'
    selectedProduct: 'Selected product'
    selectedProducts: 'Selected products'
  AccordionDangerZone:
    title: Danger zone
    message: You are about to permanently delete this item. This action cannot be undone.
  ScheduleCustom:
    choose_recurrence: 'Choose precisely how often you want to execute this task.'
    cron_expression: 'Cron expression'
    every: 'Every'
    of: 'of'
    reset: 'Reset'
    base_times:
      month: 'Month'
      week: 'Week'
      day: 'Day'
      hour: 'Hour'
    options:
      month:
        '*': 'Every month'
        0: 'January'
        1: 'February'
        2: 'March'
        3: 'April'
        4: 'May'
        5: 'June'
        6: 'July'
        7: 'August'
        8: 'September'
        9: 'October'
        10: 'November'
        11: 'December'
      week:
        '*': 'Every week'
        0: 'First'
        1: 'Second'
        2: 'Third'
      day:
        '*': 'Every day'
        1: 'Monday'
        2: 'Tuesday'
        3: 'Wednesday'
        4: 'Thursday'
        5: 'Friday'
        6: 'Saturday'
        0: 'Sunday'
      hour:
        '*': 'Every hour'
        0: 'Midnight'
        1: '1h'
        2: '2h'
        3: '3h'
        4: '4h'
        5: '5h'
        6: '6h'
        7: '7h'
        8: '8h'
        9: '9h'
        10: '10h'
        11: '11h'
        12: '12h'
        13: '13h'
        14: '14h'
        15: '15h'
        16: '16h'
        17: '17h'
        18: '18h'
        19: '19h'
        20: '20h'
        21: '21h'
        22: '22h'
        23: '23h'
      minute:
        0: 'One minute'
        15: '15 minutes'
        30: '30 minutes'
drawer:
  centralisation: Centralization
  close: 'Close the menu'
  configuration: Configuration
  dashboard: Dashboard
  diffusion: Diffusion
  documentation: Documentation
  home: Home
  hub: Hub
  layout: Layout
  open: 'Open the menu'
  optimisation: Optimization
  other: other
  overview: Overview
  preferences: Preferences
  settings: Settings
  settings_helps: Help
  settings_manage_accesses: 'Manage accesses'
enums:
  CHANNEL_SCHEDULE_TYPES:
    1_hour: 1 hour
    10_minutes: 10 minutes
    custom: Custom
  MODULES:
    channel_exports: Channel Export
    channel_imports: Channel Import
    contents: Contents
    export: CSV Export
    dam: DAM
    optimization: Optimization
    hub: HUB
  LANGS:
    fr_FR: French
    en_GB: English
  ATTRIBUTE_PARAMETERS:
    allowedDomains: 'Allowed domains'
    allowedDomains_help: 'Specify the domains allowed to be used in this attribute'
    allowedDomains_placeholder: 'Allowed domains'
    max: 'Maximum items'
    maxHeight: 'Maximum height'
    maxHeight_help: 'Specify the maximum height of the image in pixels'
    maxHeight_placeholder: 'Maximum height'
    maxLength: 'Maximum characters'
    maxLength_help: 'Specify the maximum number of characters'
    maxLength_placeholder: 'Maximum characters'
    maxSize: 'Maximum size'
    maxSize_help: 'Specify the maximum size of the file in megabytes'
    maxSize_placeholder: 'Maximum size'
    maxWidth: 'Maximum width'
    maxWidth_help: 'Specify the maximum width of the image in pixels'
    maxWidth_placeholder: 'Maximum width'
    max_help: 'Specify the maximum number of items to be selected'
    max_placeholder: 'Maximum items'
    min: 'Minimum items'
    minLength: 'Minimum characters'
    minLength_help: 'Specify the minimum number of characters'
    minLength_placeholder: 'Minimum characters'
    min_help: 'Specify the minimum number of items to be selected'
    min_placeholder: 'Minimum items'
    precision: Precision
    precision_help: 'Specify the number of digits after the decimal point'
    precision_placeholder: Precision
    types: Types
    types_help: 'Specify accepted formats'
    types_placeholder: Types
  ATTRIBUTE_PROPERTIES:
    _: "Attribute properties"
    _singular: "Attribute property"
    isSearchable: Searchable
    isLocalizable: Localizable
    isScopable: Scopable
  ATTRIBUTE_TYPES:
    _: 'Attribute types'
    _singular: 'Attribute type'
    asset_collection: 'Asset collection'
    color: Color
    content: Content
    content_collection: 'Content collection'
    date: Date
    decimal: Decimal
    file: File
    html: HTML
    image: Image
    image_collection: 'Image collection'
    json: JSON
    link: Link
    mail: Email
    media: Media
    media_collection: Media collection
    metric: Metric
    multiselect: Multi-select
    number: Number
    price: Price
    price_collection: Price collection
    product: Product
    product_collection: Product collection
    reference: Reference
    select: Select
    switch: Switch
    table: Table
    text: Text
    text_collection: Text collection
    text_help: 'Min. characters: {{ minLength }} - Max. characters: {{ maxLength }}'
    textarea: Long text
  CHANNEL_ACCESSOR_TYPES:
    ExAttributeAccessor: Attribute
    ExFormulaAccessor: Formula
    ExFormulaAccessor_import_help: "Each line/tag in your import is identified by the variable named `document`. To access the `id` property you must specify `document.id`. In the case of CSV, the properties are named using the header of each column. If your header contains spaces or special characters, you can use the alternative notation `document[\"key with spaces\"]`.\n"
    empty: "Not defined"
    ExPropertyAccessor: Property
    ExRawAccessor: 'Raw value'
  CHANNEL_ADAPTER_TYPES:
    api: 'API (HTTP)'
    ftp: FTP
    local: 'Sinfin CDN'
    ssh2_ftp: SSH2 FTP
  CHANNEL_FORMAT_TYPES:
    csv: CSV
    json: JSON
    xml: XML
  CHANNEL_SOURCE_TYPES:
    Content: Contents
    Media: Medias
    Product: Products
  CHANNEL_TYPE:
    magento: Magento
    magento_orders: Magento Orders
    mirakl: Mirakl
    'null': No type
  DOCUMENT_TYPES:
    Category: Category
    Content: Content
    Media: Media
    Product: Product
    _: Types
    _singular: Type
    'null': All
  EAV_TYPES:
    _: 'EAV types'
    _singular: 'EAV type'
    attribute: Attribute
    attribute_group: 'Attribute group'
    category: Category
    content: Content
    media: Media
    product: Product
  EXPORT_ACCESSOR_TYPES:
    attribute: Attribute
    completness: Completness
    formula: Formula
    property: Property
    properties: Properties
    raw: 'Raw value'
  EXPORT_ADAPTER_TYPES:
    ftp: FTP
    local: 'Local (download)'
  EXPORT_FORMATS:
    csv_comma: 'CSV (,)'
    csv_pipe: 'CSV (|)'
    csv_semicolon: 'CSV (;)'
    csv_tab: 'CSV (tabulation)'
    xlsx: 'Excel XLSX'
  EXPORT_FORMAT_TYPES:
    csv: CSV
    json: JSON
  HUB_ORIGIN_FILTERS:
    owner: Origin
    origin: Seller
  HUB_PRODUCT_FILTERS:
    price: 'Price'
    quantity: 'Quantity'
  HUB_SOURCE_TYPES:
    warehouse: 'Warehouse'
  JSON_OPTIONS:
    _: 'JSON options'
    _singular: 'JSON option'
    default: Default
    materials: Materials
    references_groups: 'References groups'
    virtual_tour: 'Virtual tour'
    reverse_image_search: 'Reverse image search'
  MEDIA_EXPORTS_PRESETS:
    FACEBOOK: 'Publication Facebook'
    INSTAGRAM_POST: 'Publication instagram'
    INSTAGRAM_REEL: 'Reel Story'
    INSTAGRAM_STORY: 'Instagram Story'
    LINKEDIN_BANNER: 'LinkedIn article banner'
    LINKEDIN_IMAGE: 'LinkedIn featured image'
    LINKEDIN_POST_PORTRAIT: 'Post LinkedIn (portrait)'
    LINKEDIN_POST_SQUARE: 'Post LinkedIn (carré)'
    WHATSAPP: 'Status Whatsapp'
  OPERATORS:
    _: 'Operators'
    _singular: 'Operator'
    AND: 'And'
    OR: 'Or'
    CONTAINS: Contains
    ENDS_WITH: Ends with
    EQ: Equals
    EXCLUDES: Excludes
    GT: Is greater than
    GTE: Is greater than or equal
    IN: Is in
    LIKE: Contains
    LT: Is less than
    LTE: Is less than or equal
    NEQ: Is not equal to
    STARTS_WITH: Starts with
    EXISTS: Exists
    INTERSECT_LEAST_ONE: Intersect at least 1 item of
  ORDERS_STATES:
    ACCEPTED: Accepted
    AVOIR: Avoir
    CANCELED: Canceled
    DECLINED: Declined
    PARTIALLY_SHIPPED: 'Partially Shipped'
    PENDING_FOR_VALIDATION: 'Pending for validation'
    pending: 'Pending for validation'
    SHIPPED: Shipped
    _: 'Order states'
    _notFound: 'Order state not found'
    _singular: 'Order state'
  ORDER_TRANSITIONS:
    accept: 'Order accepted!'
    cancel: 'Order canceled!'
    decline: 'Order declined!'
  ROLES:
    ROLE_SUPER_ADMIN: 'Super admin'
    ROLE_TECH: Tech
    ROLE_USER: User
    _: Roles
    _singular: Role
  RULE_LEVELS:
    _: 'Rule levels'
    _singular: 'Rule level'
  RULE_TYPES:
    _: 'Rule types'
    _singular: 'Rule type'
errors:
  400: 'Bad Request'
  401: Unauthorized
  402: 'Payment Required'
  403: Forbidden
  425: 'Too Early'
  429: 'Too Many Requests'
  500: 'Internal Server Error'
  accept_condition: 'You must accept the terms and conditions'
  bad_login: 'Please check your login credentials'
  create: 'Error during creation'
  default: 'An error occurred'
  delete: 'Error during deletion'
  loading: 'Error during loading'
  maxLength: 'This field must contain at most {{max}} character(s)'
  maxNumbers: 'This field must contain at most {{max}} number(s)'
  max_select: "You can't select up to {{max}} elements"
  media_wrong: 'Cannot set a media here'
  minArrayLength: 'This field must contain at least {{min}} item(s)'
  minLength: 'This field must contain at least {{min}} character(s)'
  minLowercase: 'This field must contain at least {{min}} lowercase letter(s)'
  minNumbers: 'This field must contain at least {{min}} number(s)'
  minSymbols: 'This field must contain at least {{min}} special character(s)'
  minUppercase: 'This field must contain at least {{min}} uppercase letter(s)'
  must_be_email: 'This field must be an email'
  must_be_number: 'This field must be a number'
  no_changes: 'No changes detected'
  passwords_must_match: 'Passwords must match'
  product_not_found: 'Product not found, go back to'
  required_field: 'This field is required'
  save: 'Error during saving'
  selection: 'Error during selection'
  update: 'Error during update'
export:
  search: 'Search an attribute'
  search_property: 'Search a property'
  HubOrder: Accounting export
  HubOrderItem: Orders export
filters:
  from: From
  preferences:
    choose_name: 'Choose a name'
    choose_name_error: 'This name is already used'
    empty_list: "You don't have any preferences yet"
    empty_search: 'No results found'
    filters_preferences: 'Filters preferences'
    title: Preferences
  to: To
helper:
  code: 'The code must be unique and contain only letters, numbers and underscores (_).'
  hub:
    avoir: "In the case of 'Avoir' entries, all prices should be entered as positive values, with the exception of the 'Total Amount', which should be negative."
  hub_item:
    avoir: "In the case of 'Avoir' entries, all prices should be entered as positive values."
homepage:
  appbar:
    profileManagement: 'Profile management'
    yourPlan: 'Your plan'
  pop_sync_msg: 'Synchronization allows you to retrieve information from your connectors (locales, attribute groups, attributes, options, etc.). Due to the workload on our servers, this process may take some time. Please note that you can request synchronization only once every 15 minutes.'
  stats:
    catalog_scope_count: Connectors
    product_errors: 'Product errors'
    scope_count: Scopes
    stats_at: 'Stats are cached for 60 minutes, last update at {{date}}'
    total_products: Products
  title: Dashboard
hubDashboard:
  _: "Dashboard"
  averageOrder: 'Average order value'
  caByOrigin: 'Revenue by origin'
  dashboard: 'Dashboard of'
  orderNumber: 'Order number'
  orderReports: 'Order reports'
  orders: Orders
  overview: Overview
  revenue: 'Total sales (€ TTC)'
  currentWeek: 'Current week'
  lastWeek: 'Last week'
  byDay: 'Per day'
  byWeek: 'Per week'
  byMonth: 'Per month'
  clientsStatTitle: New and old clients
  topProducts: 'Top products'
  loadingData: 'Loading data...'
  oldClients: 'Old clients'
  newClients: 'New clients'
  noData: 'No data available'
  salesReports: 'Sales reports'
  stockNumber: 'Stock number'
hubSalesReports:
  CAByOrigin: 'CA by origin'
  byOrigin: 'By origin'
  bySubOrigin: 'By sub origin'
hubOrdersReports:
  delay: Average shipping time
  delayDelivery: Average delivery time
hubStocksReports:
  state: 'Stock state'
  count_units: 'Number of units'
  stock_value: 'Stock value'
  stock_sold: 'Stock sold'
hubSources:
  address: 'Address'
  zipCode: 'Zip code'
  phone: 'Phone'
  count_ref: 'Number of references'
  count_units: 'Number of units'
  stock_value: 'Stock value'
inputs:
  accountsList:
    label: 'Accounts List'
  all_priorities: 'All priorities'
  application:
    label: Application
  attribute: Attribute
  attributes: Attributes
  attributes_group: 'Attribute groups'
  catalog: Catalog
  catalog_scope: Connector
  catalogs: Catalogs
  categories: Categories
  categoriesList:
    label: 'Categories List'
  channels: Channels
  chose_attributes_to_export: 'Choose attributes to export'
  chose_properties_to_export: 'Choose properties to export'
  code: Code
  color: Color
  count: Count
  created_at: 'Created at'
  description: Description
  destination: Destination
  detected: Detected
  disabled: Disabled
  discountCode:
    label: 'Discount Code'
  email: 'Email address'
  enabled: Enabled
  error_email: 'Please enter a valid email address'
  errors: Errors
  eshopList:
    label: 'E-shop List'
  files: Files
  finalStatus: 'Final status'
  firstname: 'First name'
  helpers:
    code: 'The code must be unique and contain only letters, numbers and underscores (_).'
    selectChip: 'Use the Enter key to confirm values if you want to add them manually.'
  image: Image
  isBlocking: 'Is blocking'
  isSuperAdmin: Super-admin
  labels:
    confirm-password: 'Confirm Password'
    password: Password
    submit: Save
  lastname: 'Last name'
  level: Level
  locale: Locale
  locales: Locales
  method:
    label: Method
  name: Name
  new_password: 'New password'
  offerValue:
    label: 'Offer Value'
  old_password: 'Current password'
  password: Password
  passwordConfirmation: 'Confirm password'
  password_helper: 'The password must consist of at least 8 characters, including one digit, one lowercase letter, one uppercase letter and one special character'
  period: Period
  priority: Priority
  products: Products
  rule: Rule
  rules: Rules
  rules_group: 'Rules groups'
  rules_groups: 'Rules groups'
  scope: Scope
  searchBy: 'Search by {{fields}}'
  select_value: 'Select a value'
  separator: Format
  source: Source
  status: Status
  steps: Steps
  success_email: 'If your email address exists, you will receive an email to reset your password'
  text_collection:
    placeholder: 'Write value and press Enter'
  toast:
    errors:
      fill: 'Please fill in the fields'
      same-password: 'Passwords do not match'
  triggerCondition: Condition
  updated_at: 'Updated at'
  user_groups: 'Users groups'
  users: Users
  value: Value
  with_images: With images
  workflow: Workflow
items:
  Address:
    _: Addresses
    _create: Create
    _singular: Address
  Attribute:
    _: Attributes
    _create: Create
    _created: 'Attribute created successfully!'
    _delete: 'Are you sure you want to delete the attribute "{{label}}"?'
    _deleted: 'The "{{label}}" attribute has been successfully removed.'
    _notFound: 'Attribute not found'
    _position_saved: 'Positions saved successfully!'
    _saved: 'Attribute saved successfully!'
    _singular: Attribute
    code: Code
    counters:
      options: Options
      uses: Usage
    group: Group
    isLocalizable: Localizable
    isRequired: Required
    isScopable: Scopable
    isSearchable: Searchable
    names: Name
    status: Enabled
    type: Type
  AttributeGroup:
    _: 'Attribute groups'
    _create: Create
    _created: 'Attribute group created successfully!'
    _delete: 'Are you sure you want to delete the attribute group "{{label}}"?'
    _deleted: 'The "{{label}}" attribute group has been successfully removed.'
    _notFound: 'No attribute group found'
    _saved: 'Attribute group saved successfully!'
    _singular: 'Attribute group'
    code: Code
    counters:
      attributes: Attributes
      options: Options
      uses: Usage
    names: Name
    status: Enabled
  AttributeOption:
    _: Options
    _create: Create
    _created: 'Attribute option created successfully!'
    _delete: 'Are you sure you want to delete the attribute option "{{label}}"?'
    _deleted: 'The "{{label}}" attribute option has been successfully removed.'
    _notFound: 'Attribute option not found'
    _saved: 'Attribute option saved successfully!'
    _singular: Option
    attribute: Attribut
    code: Code
    names: Name
  Catalog:
    _: Catalogs
    _create: Create
    _created: 'Catalog created successfully!'
    _delete: 'Are you sure you want to delete the catalog "{{label}}"?'
    _deleted: 'The {{label}} catalog has been successfully removed.'
    _notFound: 'Catalog not found'
    _saved: 'Catalog saved successfully!'
    _singular: Catalog
    categories: Categories
    code: Code
    descriptions: Description
    document_type: Type
    filters: Filters
    main: Information
    names: Name
    products: Products
    status: Enabled
    upload: 'Media'
  CatalogScope:
    _: Channels
    _create: Create
    _created: 'Channel created successfully!'
    _deleted: 'The "{{label}}" channel has been successfully removed.'
    _notFound: 'No result'
    _saved: 'Channel saved successfully!'
    _singular: Channel
    catalog: Catalog
    code: Code
    createdAt: Created at
    magento_config_error: 'It seems that the Magento connection parameters are not correctly configured for this channel.'
    hub_order: Orders
    hub_order_waiting_for_validation: 'Waiting for validation'
    order_waiting: 'Orders waiting for validation'
    infos: Details
    locales: Locales
    locale: Locale
    last_sync_state: State of the last sync.
    last_synced_products: Last synced products
    mapping: Mapping
    attribute_mapping: Attribute mapping
    names: Name
    settings: Settings
    scope: Scope
    status: Enabled
    type: Type
    updatedAt: Updated at
    synchro: Synchros
    workflow: Workflow
  Category:
    _: Categories
    _create: Create
    _create_button: 'Create category'
    _create_category: 'Create category'
    _created: 'Category created successfully!'
    _delete: 'Are you sure you want to delete the category "{{label}}"?'
    _deleted: 'The {{label}} category has been successfully removed.'
    _notFound: 'Category not found'
    _rename: 'Rename category'
    _saved: 'Category saved successfully!'
    _singular: Category
    _type: Category
    color: Color
    name: Name
    parent: Parent
    status: Enabled
  CategoryData:
    _: 'Category Data'
  Channel:
    _: Channels
    _singular: Channel
    type: Type
    mapping: Mapping
  ChannelAdapter:
    _singular: Adapter
    apiKey: API Key
    data: Parameters
    data_help: 'One key value pair per line separated with one colon (:).'
    headers: 'HTTP Headers'
    headers_help: 'One key value pair per line separated with one colon (:).'
    method: Method
    url: URL
    consumerKey: Consumer Key
    consumerSecret: Consumer Secret
    accessToken: Access Token
    accessTokenSecret: Access Token Consumer
    magento_settings: Magento settings
    mirakl_settings: Mirakl settings
    signatureMethod: Signature Method
    storeCode: Store Code
  ChannelSchedule:
    _singular: Schedule
    hourly: 'Every hour.'
    description: 'Choose the hours you want to automatically trigger this export.'
  ChannelMapping:
    _singular: Mapping
  ChannelFormat:
    _singular: Format
  ChannelSource:
    _singular: Source
  ChannelFilter:
    _singular: Filter
    addRow: 'Add a condition'
    addGroup: 'Add a condition group'
  ChannelWorkflowExport:
    _: 'Workflow exports'
    selectedCategories: 'Selected categories'
    recommended: Recommended
    required: Required
  Communication:
    _: Communications
  Completude:
    _: Completenesses
    _create: Create
    _created: 'Completeness created successfully!'
    _delete: 'Are you sure you want to delete the Completeness {{label}}?'
    _deleted: 'The {{label}} Completeness has been successfully removed.'
    _saved: 'Completeness saved successfully !'
    _singular: Completeness
    _updated: 'Completeness saved successfully !'
    attributes: Attributes
    code: Code
    missing: Missing
    missing_attributes: 'Following attributes are not associated with this product:'
    names: Name
    not_exist_attributes: 'Following attributes do not exist:'
  Config:
      _: Configurations
      _add: 'Create configuration'
      _create: Create
      _created: 'Configuration created successfully!'
      _notFound: 'Configuration not found'
      _saved: 'Configuration saved successfully!'
      _singular: Configuration
      key: Key
      _types:
        pdf:
          _: HUB/ Order receipt header configuration
          additionalText: Additional text
          documentTitle: Document title
          email: Email
          logo: Logo
          name: Name
          siren: SIREN
          siret: SIRET
          vat: VAT Number
          address:
            _: Address
            city: City
            company: Company
            country: Country
            firstname: Firstname
            lastname: Lastname
            mobile: Mobile
            phone: Phone
            postcode: Postcode
            region: Region
            street: Street
            street2: Street2
        pdf_shipping:
          _: HUB/ Shipping receipt header configuration
          additionalText: Additional text
          additionalHeaderText: Additional header text
          documentTitle: Document title
          email: Email
          logo: Logo
          name: Name
          siren: SIREN
          siret: SIRET
          vat: VAT Number
          weightAttribute: Weight attribute (decimal in kilogram)
          address:
            _: Address
            city: City
            company: Company
            country: Country
            firstname: Firstname
            lastname: Lastname
            mobile: Mobile
            phone: Phone
            postcode: Postcode
            region: Region
            street: Street
            street2: Street2
        environment:
          _: TECH/ Environment configuration
          defaultLang: Default lang
          langs: Langs
          modules: Modules
  Content:
    _: Contents
    _add: 'Create content'
    _create: Create
    _created: 'Content created successfully!'
    _delete: 'Are you sure you want to delete the content "{{label}}"?'
    _deleted: 'The {{label}} content has been successfully removed.'
    _notFound: 'Content not found'
    _saved: 'Content saved successfully!'
    _singular: Content
    _type: Content
    name: Name
    path: Path
    picker: 'Content picker'
    pickers: 'Contents picker'
    select: 'Select content'
    selects: 'Select contents'
    status: Enabled
    upload: 'Media'
  ContentData:
    _: 'Content Data'
  ContentFolder:
    _: Folders
    _create: 'Create folder'
    _created: 'Folder created successfully!'
    _delete: 'Are you sure you want to delete the folder "{{label}}"?'
    _deleted: 'The {{label}} folder has been successfully removed.'
    _notFound: 'Folder not found'
    _rename: 'Rename folder'
    _saved: 'Folder saved successfully!'
    _singular: Folder
    name: Name
  Currency:
    _: Currencies
    _create: Create
    _created: 'Currency created successfully!'
    _delete: 'Are you sure you want to delete the currency "{{label}}"?'
    _deleted: 'The {{label}} currency has been successfully removed.'
    _notFound: 'Currency not found'
    _saved: 'Currency saved successfully !'
    _singular: Currency
    _updated: 'Currency saved successfully !'
    code: Code
    names: Name
    symbol: Symbol
  Dashboard:
    Catalog: Catalogs
    Locale: Locales
    last30Days: 'Last 30 days'
    AttributeGroup: 'Attribute groups'
    Attribute: Attributes
    AttributeOption: Options
    Scope: Scopes
    CatalogScope: Channels
    ProductError: Errors
    Rule: Rules
    RuleGroup: 'Rule Groups'
    Workflow: Workflows
    User: Users
    UserGroup: 'User groups'
  Dictionary:
    _: Dictionaries
    code: Code
    names: Name
    _create: Create
    _created: 'Dictionary created successfully!'
    _delete: 'Are you sure you want to delete the dictionary "{{label}}"?'
    _deleted: 'The "{{label}}" dictionary has been successfully removed.'
    _notFound: 'Dictionary not found'
    _saved: 'Dictionary saved successfully!'
    _singular: Dictionary
    _updated: 'Dictionary saved successfully!'
  DictionaryMapping:
    _: 'Dictionary mappings'
    _create: Create
    _created: 'Mapping created successfully!'
    _delete: 'Are you sure you want to delete the mapping "{{label}}"?'
    _deleted: 'The "{{label}}" mapping has been successfully removed.'
    _notFound: 'Mapping not found'
    _saved: 'Mapping saved successfully!'
    _singular: Mapping
    from: From
    to: To
    dictionary: Dictionary
  Export:
    Mapping:
      column: Column
      in: Sinfin
      name: Column
      name_help: 'Name of your CSV column or JSON property in your export.'
      out: Export
      root: 'Root column'
      root_help: "If your JSON or XML feed contains a more complex structure, you can indicate here the path to the node containing the list of documents to import (example: `key1.key2.key3`). Sinfin must be able to retrieve a list of documents to browse them.\n"
      type_choose: 'Choose which type of content holds your cell'
    MappingColumn:
      code: Header
      code_help: "It's either the CSV column name or the JSON key."
      requiresColumns: 'Some columns are required to complete the configuration.'
      willGoTo: 'Will be exported in…'
    _: Exports
    _confirm_singular: 'Are you sure you want to launch this export?'
    _create: Create
    _created: 'Export created successfully!'
    _delete: 'Are you sure you want to delete the export "{{label}}"?'
    _deleted: 'The "{{label}}" export has been successfully removed.'
    _error: 'Export failed!'
    _launch: Launch
    _none: 'No columns'
    _notFound: 'Export not found'
    _saved: 'Export saved successfully!'
    _singular: Export
    _success: 'Export done!'
    accessor: 'Value type'
    adapter: Destination
    all: 'Export all'
    attribute: Attribute
    connected: Connected
    cron: Cron
    currentView: 'Export current view'
    filename: Filename
    format: Format
    formula: Formula
    helperAdapter: 'Choose where the file will be sent.'
    helperCron: 'Choose the times at which you want to trigger this export automatically.'
    helperMapping: 'Select how the data is handled in the column.'
    host: Host
    lastRun: 'Last run'
    mapping: Mapping
    names: Name
    nextRun: 'Next run'
    notConnected: 'Not connected'
    password: Password
    port: Port
    property: Property
    raw: Constant
    scheduler: Scheduling
    source: Type
    username: Username
    uuid: UUID
    warningMaxColumns: 'The maximum number of columns is 256.'
  HubInvoice:
    _: Invoices
    _singular: Invoice
  HubOrder:
    AVOIR: Avoir
    CANCELED: Canceled
    DECLINED: Declined
    PARTIALLY_SHIPPED: 'Partially Shipped'
    PENDING_FOR_VALIDATION: 'Pending for validation'
    SHIPPED: Shipped
    VALIDATED: Validated
    _: Orders
    _create: Create
    _created: 'Order created successfully!'
    _deleted: 'Order deleted successfully!'
    _refresh: 'Refresh order'
    _refreshed: 'Order refreshed successfully!'
    _singular: Order
    _singular_avoir: Credit
    _updated: 'Order updated successfully!'
    accept: Are you sure you want to accept this order?
    acceptedAt: 'Order accepted at'
    cancel: Are you sure you want to cancel this order?
    commentary: Commentary
    decline: Are you sure you want to decline this order?
    declinedAt: 'Order declined at'
    address: Address
    amount: Amount
    billing: Billing
    billing_informations: 'Billing informations'
    ca: 'Total sales'
    company: Company
    createdAt: 'Created at'
    createAt: 'Order created at'
    customer: Customer
    city: City
    discount_amount: 'Discount amount'
    email: Email
    excluding_tax: excl. tax
    forms:
      backProducts: 'Back to Products List'
      contact: Contact
      identification: Identification
      price: 'Price (excl. tax)'
      selectedProducts: 'Selected Products'
      showProducts: 'Show Selected Products'
      toggle: 'Billing address is the same as shipping address'
      total: Total
      totalPrice: 'Products total'
      tva: 'vat (%)'
    history: History
    inputs:
      additionalNote: 'Additional note'
      city: City
      company: Company
      country: Country
      currencyCode: 'Currency code'
      discountAmount: 'Discount amount'
      email: Email
      firstname: Firstname
      importedAt: 'Imported at'
      lastname: Lastname
      marketplace: Marketplace
      mobile: Mobile
      owner: Owner
      phone: Phone
      postcode: Postcode
      products: Products
      reference: Reference
      region: Region
      shippingAmount: 'Shipping amount'
      state: State
      street: Street
      street2: Street2
      subTotal: Subtotal
      taxAmount: 'Tax amount'
      totalAmount: 'Total amount'
    name: Name
    order_average: 'Average order'
    order_count: 'Order count'
    order_number: 'Order number'
    ordered_products: 'Ordered products'
    origin: Origin
    owner: Owner
    ownerIdentifier: Reference
    phone: Phone
    postcode: Postcode
    price: Total price (incl. tax)
    product_name: 'Product name'
    provider: Provider
    quantity: Quantity
    seeInvoice: 'See pdf'
    quantitySold: Quantity sold
    sentBy: 'Sent by'
    sentTime: 'Sent at'
    ship: Are you sure you want to ship this order?
    shipments: Shipments
    shipment: Shipment
    shipping: Shipping
    shipping_amount: 'Shipping amount'
    shipping_date: 'Shipping date'
    shipping_informations: 'Shipping informations'
    sku: SKU
    status: Status
    subTotal: subtotal
    tabs:
      informations: Informations
      moreInformations: 'Additional information'
      products: Products
      tax: Taxes
    tax: incl. tax
    taxPart: With tax
    toggleInformations: 'Billing address is the same as shipping address'
    total: Total (incl. tax)
    total_excl_tax: 'Total (excl. tax)'
    total_amount: 'Total amount'
    total_price: Total
    track: 'Track package'
    tracking: Tracking
    trackingNumber: 'Tracking number'
    unit_price: 'Unit price (incl. tax)'
    unit_price_info: 'tax excluded or not'
    updatedAt: 'Updated at'
  HubOrderItem:
    _: 'Products (order)'
    _singular: 'Product (order)'
    _create: Create
  HubShipment:
    _: Shipments
    _create: Create
    carrier: Carrier
    trackingLink: Tracking link
    trackingNumber: Tracking number
  HubStock:
    _: Stocks
    _create: Create
    _created: 'Stock created successfully!'
    _deleted: 'Stock deleted successfully!'
    _saved: 'Stock saved successfully!'
    _singular: Stock
    _updated: 'Stock updated successfully!'
    createdAt: 'Created at'
    gte: 'Quantity greater than or equal to'
    id: ID
    lte: 'Quantity lower than or equal to'
    product: SKU
    productName: Product
    quantity: Quantity
    updatedAt: 'Updated at'
    source: Source
  HubSource:
    _: Sources
    _create: Create
    _created: 'Source created successfully!'
    _delete: 'Are you sure you want to delete the source "{{label}}"?'
    _deleted: 'Source "{{label}}" deleted successfully!'
    _saved: 'Source saved successfully!'
    _singular: Source
    _updated: 'Source updated successfully!'
    code: Code
    names: Name
    status: Enabled
    type: Type
    upload: 'Media'
  HubOrigin:
    _: Per origin
  Import:
    Mapping:
      column: Column
      in: Import
      name: Column
      name_help: 'Name of your CSV column or JSON property in your export.'
      out: Sinfin
      root: 'Root column'
      root_help: "You must provide a list of items to our module \"import\" for it to work. However, the source files sometimes contain more complex structures with nested objects. In this situation, you can tell us how to find the table in your structure by specifying its access path (eg: `key1.key2.key3`).\n"
      type_choose: 'Choose which type of content holds your cell'
    MappingColumn:
      code: Header
      code_help: "It's either the CSV column name or the JSON key."
      requiresColumns: 'Some columns are required to complete the configuration.'
      willGoTo: 'Will be imported in…'
    _: Imports
    _confirm_singular: 'Are you sure you want to launch this import?'
    _create: Create
    _created: 'Import created successfully!'
    _delete: 'Are you sure you want to delete the import "{{label}}"?'
    _deleted: 'The "{{label}}" import has been successfully removed.'
    _error: 'Import failed!'
    _launch: Launch
    _none: 'No columns'
    _notFound: 'Import not found'
    _saved: 'Import saved successfully!'
    _singular: Import
    _success: 'Import done!'
    accessor: 'Value type'
    adapter: Source
    attribute: Attribute
    connected: Connected
    cron: Cron
    filename: Filename
    format: Format
    formula: Formula
    helperAdapter: 'Choose where the file will be downloaded.'
    helperCron: 'Choose the times at which you want to trigger this export automatically.'
    helperMapping: 'Select how the data is handled in the column.'
    host: Host
    lastRun: 'Last run'
    mapping: Mapping
    names: Name
    nextRun: 'Next run'
    notConnected: 'Not connected'
    password: Password
    port: Port
    property: Property
    raw: Constant
    scheduler: Scheduling
    source: Type
    username: Username
    uuid: UUID
  Layout:
    _: Layout
    _saved: 'Layout saved successfully!'
    attribute: Attribute
    configure: Configure
    content_detail: 'Content detail'
    content_listing: 'Content listing'
    general_attributes: 'General attributes'
    image: Image
    media_detail: 'Media detail'
    media_listing: 'Media listing'
    none_visible_attributes: 'None visible attributes'
    product_detail: 'Product detail'
    product_listing: 'Product listing'
    product_listing_settings: 'Product listing configuration'
    search: 'Search on Sinfin™ platform ...'
    top_attributes: 'Top attributes'
    visible_attributes: 'Visible attributes'
  Locale:
    _: Locales
    _create: Create
    _created: 'Locale created successfully!'
    _delete: 'Are you sure you want to delete the locale "{{label}}"?'
    _deleted: 'The {{label}} locale has been successfully removed.'
    _notFound: 'Locale not found'
    _saved: 'Locale saved successfully!'
    _singular: Locale
    code: Code
    names: Name
    status: Enabled
  Mapping:
    column: Column
    name: Column
    name_help: 'Name of your CSV column or JSON property in your export.'
    type_choose: 'Choose which type of content holds your cell'
  MappingColumn:
    _: Columns
    _create: Create
    availableOptions: 'Available options'
  MappingColumnDefinition:
    _: Columns definition
  MeasureFamily:
    _: 'Measure families'
    _singular: 'Measure family'
    code: Code
    default: Default
    names: Name
    units: Units
  Media:
    _: Medias
    _add: 'Add media'
    _create: Create
    _created: 'Media created successfully!'
    _delete: 'Are you sure you want to delete the media "{{label}}"?'
    _deleted: 'The {{label}} media has been successfully removed.'
    _notFound: 'Media not found'
    _saved: 'Media saved successfully!'
    _singular: Media
    _type: DAM
    _unzip: "Unzip in progress"
    exports: 'Exports presets'
    format: Format
    formats: Formats
    name: Name
    path: Path
    picker: 'Media picker'
    pickers: 'Medias picker'
    size: Size
    source: Source
    status: Enabled
    type: Type
    upload: 'Media'
    uploaded: 'File uploaded'
  MediaData:
    _: 'Media Data'
  MediaFolder:
    _: Folders
    _create: 'Create folder'
    _created: 'Folder created successfully!'
    _delete: 'Are you sure you want to delete the folder "{{label}}"?'
    _deleted: 'The {{label}} folder has been successfully removed.'
    _notFound: 'Folder not found'
    _rename: 'Rename folder'
    _saved: 'Folder saved successfully!'
    _singular: Folder
    name: Name
  PasswordUpdate:
    _created: 'Password updated successfully!'
  Product:
    _: Products
    _create: Create
    _created: 'Product created successfully!'
    _deleted: 'The product has been successfully removed.'
    _refresh: 'Refresh product'
    _refreshed: 'Product refreshed successfully!'
    _saved: 'Product saved successfully!'
    _singular: Product
    _type: PIM
    picker: 'Product picker'
    pickers: 'Products picker'
    price: 'Price'
    select: 'Select product'
    select_attributes: 'Select the attributes to display'
    selects: 'Select products'
    sku: SKU
    status: Enabled
    upload: 'Media'
    uuid: Product unique identifier
    createdAt: 'Created at'
    updatedAt: 'Updated at'
  ProductData:
    _: 'Product Data'
  DocumentErrorSummary:
    _: Errors
    _singular: Error
    catalogScope: Connector
    identifier: Doc. Id.
    level: Level
    locale: Locale
    rule: Rule
    sku: SKU
    type: Type
    errorTargets: 'Error targets'
  ProductSync:
    _confirm: 'Are you sure you want to synchronize the products {{skus}}?'
    _confirm_singular: 'Are you sure you want to synchronize the product {{skus}}?'
    _error: 'An error occurred during product synchronization'
    _success: 'Synchronisation request acknowledged!'
  Rule:
    _: Rules
    _create: Create
    _created: 'Rule created successfully!'
    _deleted: 'The "{{label}}" rule has been successfully removed.'
    _description: 'Rule description'
    _error: 'When the rule is not valid'
    _rule: 'Condition and Rule'
    _saved: 'Rule saved successfully!'
    _singular: Rule
    attribute: Attribute
    attributes: Attributes
    code: Code
    create: 'Create a rule'
    descriptions: Description
    errorTargets: 'Where should we store the error?'
    isBlocking: 'is blocking'
    isBlocking_title: 'Should we stop here?'
    level: Level
    level_title: 'What error level should we raise?'
    names: Name
    rule: Rule
    triggerCondition: Condition
    type: Type
  RuleGroup:
    _: 'Rule Groups'
    _create: Create
    _created: 'Rule group created successfully!'
    _deleted: 'The "{{label}}" rule group has been successfully removed.'
    _notFound: 'No result found'
    _saved: 'Rule group saved successfully!'
    _singular: 'Rule Group'
    code: Code
    counters:
      rules: Rules
    create: 'Create a rule group'
    names: Name
    steps: Steps
    triggerCondition: Condition
  Scope:
    _: Scopes
    _create: Create
    _created: 'Scope created successfully!'
    _delete: 'Are you sure you want to delete "{{label}}" scope?'
    _deleted: 'The "{{label}}" scope has been successfully removed.'
    _notFound: 'No result found'
    _saved: 'Scope saved successfully!'
    _singular: Scope
    code: Code
    names: Name
  Template:
    _: Templates
    _create: Create
    _created: 'Template created successfully!'
    _delete: 'Are you sure you want to delete the template "{{label}}"?'
    _deleted: 'The {{label}} template has been successfully removed.'
    _notFound: 'Template not found'
    _saved: 'Template updated successfully!'
    _seeAttributeGroups: 'See attribute groups'
    _singular: Template
    attributeGroups: 'Attribute groups'
    code: Code
    eavTypes: 'Document types'
    eavTypes_help: 'Limit the use of this template to document types.'
    names: Name
    search_attribute_group: 'Search an attribute group'
    status: Status
  Unit:
    _: 'Units measure'
    _create: Create
    _created: 'Unit measure created successfully!'
    _delete: 'Are you sure you want to delete the unit measure "{{label}}"?'
    _deleted: 'The {{label}} unit measure has been successfully removed.'
    _saved: 'Unit measure saved successfully!'
    _singular: 'Unit measure'
    code: Code
    convertFromStandard: 'Convert from standard'
    convertToStandard: 'Convert to standard'
    names: Names
    symbol: Symbol
  UploadImport:
    _: 'Import history'
    _notFound: 'Import history not found'
    errors: "Errors"
    details: 'Import details'
    row: 'Row'
    rowStatus:
      _imported: 'Number of rows imported'
      _succeeded: 'Number of rows successfully imported'
      _failed: 'Number of rows containing errors'
    userEmail: "User email"
  UploadExport:
    _: Export history
    _error: 'Export failed!'
    _success: 'Export done!'
    _start: 'Export started'
    userEmail: "User email"
  User:
    _: Users
    _create: Create
    _created: 'User created successfully!'
    _deleted: '"{{label}}" user has been successfully removed.'
    _membershipsAreUseless: 'This user is a super administrator ; there is no need to manage their groups.'
    _saved: 'User saved successfully!'
    _singular: User
    avatar: Avatar
    lastLogin: 'Last login'
    names: Name
    sso: SSO
    status: Enabled
    userGroups: 'User groups'
  UserGroup:
    _: 'User groups'
    _attribute_groups_saved: 'Attribute groups saved successfully !'
    _create: Create
    _created: 'Usergroup created successfully!'
    _delete: 'Are you sure you want to delete the group "{{label}}"?'
    _deleted: 'The user group {{label}} has been successfully removed.'
    _notFound: 'No user group available'
    _route: Group
    _saved: 'Usergroup saved successfully!'
    _singular: 'User group'
    _users_saved: 'Users saved successfully !'
    none: 'No user group'
  UserGroupMembership:
    _: 'User requests'
    _create: Create
    _created: 'Request created successfully!'
    _singular: 'User request'
  UserGroupMembershipRequest:
    _: 'User requests'
    _add: 'Join a group'
    _create: Create
    _created: 'Request created successfully!'
    _singular: 'User request'
    accept: 'I accept the terms & conditions'
    avatar: Avatar
    createdAt: 'Created at'
    email: Email
    group: Group
    lastname: Name
    message: Message
    message_help: 'Provide the group administrators with all the necessary information to validate your request, such as your identity, the team you belong to, your role in the company, etc.'
    userGroup: 'User group'
  UserGroupMembershipRequestSummary:
    _: 'User requests'
    _singular: 'User request'
  UserLogin:
    _: 'User stats'
    count: 'Login count'
    email: Email
    from: From
    role: Role
    to: to
  Workflow:
    _: Workflows
    _create: Create
    _created: 'Workflow created successfully!'
    _delete: 'Are you sure you want to delete the workflow "{{label}}"?'
    _deleted: 'The {{label}} workflow has been successfully removed.'
    _notFound: 'Workflow not found'
    _saved: 'Workflow saved successfully!'
    _singular: Workflow
    descriptions: Description
    initial_status: 'Initial status :'
    names: Name
    reached_status: 'Reached status :'
    rules: Rules
    statuses: Name
  WorkflowStep:
    _: Steps
    _create: Create
    _deleted: 'The step has been successfully deleted.'
    _singular: Step
    add: 'Create step'
    add_rule: 'Create a rule'
    add_ruleGroup: 'Add a rule group'
    manageRuleGroups: 'Manage rule groups'
    names: Name
    ruleGroupsInWorkflow: 'Rule groups in the workflow'
    ruleGroupsNotInWorkflow: 'Rule groups'
    statuses: Name
    subtitle: ''
  WorkflowTest:
    _: Tests
    _singular: Test
    status:
      _error: Erreur
      _failed: Échec
      _invalid: Invalide
      _missing: Manquant
      _skipped: Ignoré
      _succeeded: Succès
login:
  email: 'Proceed with your email'
  error: 'An error occurred while retrieving data'
  forget_password: 'Forgot password? Click'
  forget_password_button: 'Get reset link'
  forget_password_text: 'Please enter your email to search for your account.'
  forget_password_title: 'Forgot password'
  here: here
  login: Login
  note: 'You will not be able to use this form to log in if you have an SSO account.'
  password-changed: 'Your password has been changed successfully'
  reset: 'Reset Password'
  reset-tagline: 'Enter the new password for your SINFIN account'
  sso: 'Proceed with'
  sso_login: 'Login with your SSO'
  title: 'Login to your SINFIN workspace'
  token_expired: 'Your token has expired'
  welcome: 'Welcome to SINFIN'
  welcome_back: 'Welcome to your workspace'
lvmh:
  dash:
    scoped_not_scoped: 'This attribute is scopable; however, the associated statistics are not scoped, which may result in inconsistent or misleading values.'
    status:
      attribute_error: 'Mandatory attributes - errors'
      blocker_product: 'Products - blocker errors'
      empty_attribute: 'Mandatory attributes - empty'
      product_without_blocker: 'Valid products - with non blocker errors'
      valid_attribute: 'OK mandatory attributes'
      valid_product: 'Valid products'
    title:
      attributes_by_status: 'Number of attributes by status'
      attributes_by_typologie: 'Missing mandatory attributes by typologie: '
      products_by_completion: 'Number of products by completion'
onboarding:
  settings: Settings
  page: Page
  card: Card
  configure_page: Configure page
  configure_card: Configure card
profile:
  manage:
    password: 'My password'
    title: 'My profile'
rule_editor:
  _: 'Rule editor'
  _see_options: 'See options'
  _write_rule_editor: 'Write into the rule editor'
rules:
  deleteConfirmation: 'Delete rule'
  deleteConfirmationMessage: 'Are you sure about deleting this rule?'
  form:
    open_rule_editor: 'Open editor'
    rule_placeholder: 'Write your rule here'
    rule_preview: 'Test rule'
    rule_preview_action: Test
    rule_preview_placeholder: SKU
    see_rules: 'See the rules'
    stop_here_if_error: '{{name}} is blocking, stop here if an error occured'
    tabs:
      groupInformations: 'Rule informations'
      ruleGroup: 'Rule group'
  rule: Rule
  status:
    blocker: Blocker
    high: High
    low: Low
    medium: Medium
  types:
    error: Error
    missing: Missing
settings:
  insufficient_rights: 'You do not have the necessary permissions to modify this information.'
  userGroups:
    AttributesInGroup: 'Attribute groups associated with the user group'
    AttributesNotInGroup: 'Attribute groups'
    UsersInGroup: 'Users in the group'
    UsersNotInGroup: 'Users not in the group'
    add: 'Add group'
    addGroupFormSubitle: 'Please complete the fields below to create your group.'
    addGroupFormTitle: Group
    admin: Admin
    catalogs: Catalogs
    color: Color
    delete:
      confirmText: 'If you delete this group, it will no longer be accessible. If you do not wish to delete it, cancel the deletion by clicking on cancel.'
      confirmTitle: 'Are you sure you want to delete this group?'
      success: 'User group deleted successfully'
    editGroupFormTitle: 'Edit group'
    form:
      groupInformations:
        attributeGroups: 'Attribute group'
        catalogs: Catalogs
        color: Color
        name: 'Group name'
        users: Users
      tabs:
        attributesGroups: 'Attribute groups'
        catalogs: Catalogs
        groupInformations: 'Group informations'
        users: Users
    manageGroup: 'Manage group'
    manageUser: 'Manage users'
    manager: Manager
    member: Member
    name: Name
    noResult: 'No user group found'
    role: Role
    scopes: 'Attribute groups'
    seeGroups: Groups
    seeUser: User
    seeUsers: Users
    subTitle: 'Manage your groups (creation / modification / deletion)'
    title: 'User groups'
    userGroup: 'User group'
    users: Users
  users:
    form:
      inputs:
        userGroup: 'User Group'
        userGroups: 'User Groups'
        user_group: 'User Group'
    user: User
status:
  active: Active
  all: All
  inactive: Inactive
welcome:
  message: "It appears that your account is not yet set up, which prevents access to the platform's data. To resolve this, you can request to join a group. Your request will then be sent to the group administrators for approval."
  pending_request_message: 'You requested to join <strong>{{group}}</strong> on <strong>{{date}}</strong>. Your request has been successfully sent to the administrators. You will receive a confirmation email once it has been accepted.'
  pending_requests_title: 'Pending requests ({{count}})'
  title: 'Welcome to Sinfin!'
