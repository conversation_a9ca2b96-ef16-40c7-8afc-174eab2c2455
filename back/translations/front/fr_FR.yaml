actions:
  accept: Accepter
  activate: Montrer
  add: Ajouter
  addColumn: 'Ajouter colonne'
  add_all: 'Ajouter tout'
  add_condition: 'Ajouter une condition'
  add_file: 'Ajouter un fichier'
  add_image: 'Ajouter une image'
  add_name: 'Ajouter un nom'
  add_not_found: 'Recherche non trouvée: ajouter "{{search}}" dans les {{identifier}}'
  add_reference: 'Saisissez une référence puis appuyez sur Entrée'
  add_sub_reference: 'Ajouter une sous-référence'
  add_to_preferences: 'Ajouter aux préférences'
  apply: Appliquer
  cancel: Annuler
  close: Fermer
  confirm: Confirmer
  copied: Copié
  copy: Copier
  create: Ajouter
  create_female: 'Ajouter une'
  create_male: 'Ajouter un'
  created: Crée
  created_at: 'Créé le'
  deactivate: Masquer
  decline: Refuser
  delete: Supprimer
  delete_all: 'Tout supprimer'
  delete_permanently: 'Supprimer définitivement ?'
  delete_permanently_text: 'Êtes-vous sûr de vouloir supprimer {{name}} ?'
  delete_selection: 'Supprimer la sélection'
  deleted: Supprimé
  download: Télécharger
  drop_files: 'Glisser déposer un fichier'
  duplicate: Dupliquer
  edit: Modifier
  ended_at: 'Terminé le'
  erased_all: 'Tout effacer'
  exit: Quitter
  export: Export
  export_all: 'Tout exporter'
  export_selection: 'Exporter la sélection'
  import: Import
  imported: importé
  importing: "en cours d'import"
  insert: Insérer
  launchSync: 'Êtes-vous sûr de vouloir synchroniser maintenant ?'
  loading: Chargement
  logout: 'Se déconnecter'
  manual: manuelle
  moveDown: Descendre
  moveUp: Monter
  open: Ouvrir
  openSettings: 'Ouvrir les paramètres'
  reloadPage: Actualiser
  remove: Retirer
  removePermText: 'Êtes-vous sûr de vouloir retirer {{name}} ?'
  remove_all: 'Retirer tout'
  rename: Renommer
  return: Retour
  save: Enregistrer
  save_password: 'Enregister le mot de passe'
  search: Rechercher
  see: Voir
  seeAll: 'Voir tout'
  seeLess: 'Voir moins'
  seeMore: 'Voir plus'
  select: Sélectionner
  select_columns: 'Sélectionner les colonnes'
  select_images: 'Sélectionner des images'
  select_redirection: 'Sélectionner la cible du marqueur'
  send: Envoyer
  ship: Expédier
  sortBy: 'Trier par'
  sync: Synchroniser
  syncLaunched: 'Synchronisation lancée !'
  sync_categories: 'Synchroniser les catégories'
  sync_products: 'Synchroniser les produits'
  task_detail: 'Tâche terminée, voir le détail'
  test: Tester
  unzip: "Décompresser le fichier"
  unzip_text: "Décompresser votre fichier pour répliquer son arborescence dans le DAM Sinfin"
  update: Modifier
  updateColor: 'Modifier la couleur'
  updated: 'Mis à jour'
  updated_at: 'Mis à jour le'
  upload: Télécharger
  upload_file: 'Importer un fichier'
  validate: Valider
common:
  account: Compte
  actions: Actions
  active: Actif
  all: Tous
  alt_filled: 'Alt texte : "{{value}}".'
  alt_missing: 'Alt texte manquant.'
  alt_placeholder: 'Texte alternatif'
  alt_title: 'Texte alternatif(s)'
  amount: Montant
  atLeast: 'Au moins'
  autre: autre
  by: via
  card: Carte
  categories: catégories
  characters: caractères
  code: Code
  coming_soon: 'En cours de développement...'
  created_at: 'Créé le'
  delete: Supprimer
  error: 'Une erreur est survenue'
  errorProducts: 'Produits en erreur !'
  error_tryLater: 'Une erreur est survenue, veuillez réessayer plus tard'
  errors: Erreurs
  file: fichier
  files: fichiers
  filters: Filtres
  filters_subtitle: 'Filtrez les résultats en fonction de vos besoins'
  folders: Dossier(s)
  from: depuis
  general: Général
  global: Global
  hideEmptyAttributes: 'Cacher les attributs vides'
  hideEmptyScopes: 'Cacher les scopes vides'
  hideColumn: 'Masquer la colonne'
  image: image
  images: images
  informations: Informations
  informations_commande: 'Informations commande'
  itemNotEditable: "Cet élément appartient à {{owner}} et n'est pas supprimable depuis l'interface. Ce formulaire ne contient que les attributs modifiables."
  lang: Langue
  languages: Langues
  localizable: Localisable
  manage_columns_search_placeholder: 'Rechercher un attribut'
  manage_columns_title: 'Gestion des colonnes'
  marker: marqueur
  marker_helper: "Cliquer sur l'aperçu à gauche pour ajouter un marqueur"
  markers: marqueurs
  more: de plus
  name: Nom
  never: Jamais
  next: Suivant
  'no': Non
  noCatalog: 'Sans catalogue'
  no_result: 'Aucun résultat'
  no_result_subtitle: 'Aucun résultat pour cette recherche'
  none: Aucun
  noResult: N/A
  notFound: Introuvable
  notSpecified: 'Non spécifié'
  operator: Opérateur
  or: Ou
  other: autre
  others: autres
  pim: Pim
  placeholder_filter_value: Valeur
  points: ' :'
  preview: Aperçu
  redirection: Redirection
  reference: Référence
  requests: Demandes
  reset: Réinitialiser
  rowsPerPage: 'Lignes par page'
  scopable: Scopable
  searchTitle: Recherche
  searchUser: 'Rechercher un utilisateur'
  searchable: Recherchable
  size: Taille
  sku: SKU
  skus: SKU(S)
  sort: 'Trier'
  status: Statut
  stepCompleted: 'Étape validée !'
  subRef: Sous-référence
  tags: tags
  theList: 'la liste'
  title: Titre
  tree: Arborescence
  type: Type
  updated_at: 'Modifié le'
  usage: Usage
  uuid: UUID
  virtual_tour: 'Visite Virtuelle'
  warning: Attention
  warning_close_form: 'Les informations saisies seront perdues, êtes-vous sûr de vouloir fermer le formulaire ?'
  warning_close_form_filter: "Un filtre n'a pas de valeur et va être supprimé, êtes-vous sûr de vouloir fermer le formulaire ?"
  when: Lorsque
  words: mots
  write: Écriture
  'yes': Oui
  zip: Compresser
components:
  ContentPicker:
    folders: Dossiers
    label: 'Sélectionner un/des contenu(s)'
    labelSingular: 'Sélectionner un contenu'
    selectedContent: 'Contenu sélectionné'
    selectedContents: 'Contenus sélectionnés'
  ContentsAttributeGroupsSelection:
    maxFiles: '{{max}} fichiers max.'
    text: 'Déposez vos fichiers ici ou cliquez pour les téléverser'
    title: "Sélectionnez un ou plusieurs groupes d'attributs"
  ContentsTemplateSelection:
    select_template: 'Sélectionner ce template'
    title: 'Sélectionnez un template'
  DataTable:
    columns:
      _: Colonnes
      hide_column: 'Masquer la colonne'
      manage_columns_search_placeholder: 'Rechercher un attribut'
      manage_columns_title: 'Gestion des colonnes'
    density:
      _: Densité
      compact: 'Compact'
      comfortable: 'Espacé'
      standard: 'Standard'
    sort:
      _: Trier
      asc: 'Croissant'
      desc: 'Décroissant'
      unsort: 'Désordonné'
  DateRangePicker:
    currentMonth: 'Ce mois'
    lastWeek: 'La semaine dernière'
    last7Days: 'Les 7 derniers jours'
    lastMonth: 'Le mois dernier'
    nextMonth: 'Le mois prochain'
    thisWeek: 'Cette semaine'
    thisYear: 'Cette année'
    today: "Aujourd'hui"
    placeholder: 'Dates'
  Dropzone:
    formats: 'Formats: {{ formats }}'
    maxFiles: 'Fichiers max.: {{ max }}'
    text: 'Déposez vos fichiers ici ou cliquez pour les télécharger.'
  FormCategory:
    filters: Filtres
    main: Général
  TableColumnsPanel:
    reset: Réinitialiser
    show_hide_all: Afficher/Masquer tout
  MediaPicker:
    file: Fichier
    files: Fichiers
    folders: Dossiers
    label: 'Sélectionner des médias'
    labelSingular: 'Sélectionner un média'
    selectedMedia: 'Media sélectionné'
    selectedMedias: 'Médias sélectionnés'
  ProductPicker:
    label: 'Sélectionner un/des produit(s)'
    labelSingular: 'Sélectionner un produit'
    selectedProduct: 'Produit sélectionné'
    selectedProducts: 'Produits sélectionnés'
  AccordionDangerZone:
    title: Zone dangereuse
    message: Vous êtes sur le point de supprimer définitivement cet élément. Cette action n'est pas réversible.
  ScheduleCustom:
    choose_recurrence: 'Choisissez précisément la fréquence à laquelle vous souhaitez exécuter cette tâche.'
    cron_expression: 'Expression cron'
    every: 'Tou(te)s les'
    of: 'de'
    reset: 'Réinitialiser'
    base_times:
      month: 'Mois'
      week: 'Semaines'
      day: 'Jours'
      hour: 'Heures'
    options:
      month:
        '*': 'Chaque mois'
        0: 'Janvier'
        1: 'Février'
        2: 'Mars'
        3: 'Avril'
        4: 'Mai'
        5: 'Juin'
        6: 'Juillet'
        7: 'Août'
        8: 'Septembre'
        9: 'Octobre'
        10: 'Novembre'
        11: 'Décembre'
      week:
        '*': 'Chaque semaine'
        0: 'Première'
        1: 'Deuxième'
        2: 'Troisième'
      day:
        '*': 'Chaque jour'
        1: 'Lundi'
        2: 'Mardi'
        3: 'Mercredi'
        4: 'Jeudi'
        5: 'Vendredi'
        6: 'Samedi'
        0: 'Dimanche'
      hour:
        '*': 'Chaque heure'
        0: 'Minuit'
        1: '1h'
        2: '2h'
        3: '3h'
        4: '4h'
        5: '5h'
        6: '6h'
        7: '7h'
        8: '8h'
        9: '9h'
        10: '10h'
        11: '11h'
        12: '12h'
        13: '13h'
        14: '14h'
        15: '15h'
        16: '16h'
        17: '17h'
        18: '18h'
        19: '19h'
        20: '20h'
        21: '21h'
        22: '22h'
        23: '23h'
      minute:
        0: 'Une minute'
        15: '15 minutes'
        30: '30 minutes'
drawer:
  centralisation: Centralisation
  close: 'Réduire le menu'
  configuration: Configuration
  dashboard: Dashboard
  diffusion: Diffusion
  documentation: Documentation
  home: Accueil
  hub: Hub
  layout: Affichage
  open: 'Ouvrir le menu'
  optimisation: Optimisation
  other: Autres
  overview: "Vue d'ensemble"
  preferences: Préférences
  settings: Paramètres
  settings_helps: Aide
  settings_manage_accesses: 'Gestion des accès'
enums:
  CHANNEL_SCHEDULE_TYPES:
    1_hour: 1 heure
    10_minutes: 10 minutes
    custom: 'Personnalisé'
  MODULES:
    channel_exports: Channel Export
    channel_imports: Channel Import
    contents: Contents
    export: CSV Export
    dam: DAM
    optimization: Optimization
    hub: HUB
  LANGS:
    fr_FR: Français
    en_GB: Anglais
  ATTRIBUTE_PARAMETERS:
    allowedDomains: 'Domaines autorisés'
    allowedDomains_help: 'Spécifier les domaines autorisés'
    allowedDomains_placeholder: 'Domaines autorisés'
    max: 'Éléments maximum'
    maxHeight: 'Hauteur maximum'
    maxHeight_help: 'Spécifier la hauteur maximum en pixels'
    maxHeight_placeholder: 'Hauteur maximum'
    maxLength: 'Longueur maximum'
    maxLength_help: 'Spécifier la longueur maximum de caractères'
    maxLength_placeholder: 'Longueur maximum'
    maxSize: 'Taille maximum'
    maxSize_help: 'Spécifier la taille maximum en megabytes'
    maxSize_placeholder: 'Taille maximum'
    maxWidth: 'Largeur maximum'
    maxWidth_help: 'Spécifier la largeur maximum en pixels'
    maxWidth_placeholder: 'Largeur maximum'
    max_help: "Spécifier le nombre maximum d'éléments à sélectionner"
    max_placeholder: 'Éléments maximum'
    min: 'Éléments minimum'
    minLength: 'Longueur minimum'
    minLength_help: 'Spécifier la longueur minimum de caractères'
    minLength_placeholder: 'Longueur minimum'
    min_help: "Spécifier le nombre minimum d'éléments à sélectionner"
    min_placeholder: 'Éléments minimum'
    precision: Précision
    precision_help: 'Spécifier le nombre de chiffres après la virgule'
    precision_placeholder: Précision
    types: Types
    types_help: 'Spécifier les formats autorisés'
    types_placeholder: Types
  ATTRIBUTE_PROPERTIES:
    _: "Propriétés d'attribut"
    _singular: "Propriété d'attribut"
    isSearchable: Recherchable
    isLocalizable: Localisable
    isScopable: Scopable
  ATTRIBUTE_TYPES:
    _: "Types d'attribut"
    _singular: "Type d'attribut"
    asset_collection: Asset
    color: Couleur
    content: Contenu
    content_collection: 'Liste de contenus'
    date: Date
    decimal: Décimal
    file: Fichier
    html: HTML
    image: Image
    image_collection: "Collection d'image"
    json: JSON
    link: Lien
    mail: Email
    media: Média
    media_collection: 'Liste de médias'
    metric: Métrique
    multiselect: 'Liste déroulante multiple'
    number: Nombre
    price: Prix
    price_collection: Liste de Prix
    product: Produit
    product_collection: 'Liste de produits'
    reference: Référence
    select: 'Liste déroulante'
    switch: 'Oui / Non'
    table: Tableau
    text: Texte
    text_collection: 'Texte multiple'
    text_help: 'Caractères min. : {{ minLength }} - Caractères max. : {{ maxLength }}'
    textarea: 'Texte long'
  CHANNEL_ACCESSOR_TYPES:
    ExAttributeAccessor: Attribut
    ExFormulaAccessor: Formule
    ExFormulaAccessor_import_help: "Chaque ligne/balise de votre import est repérée par la variable nommée `document`. Pour accéder à la propriété `id` vous devez indiquer `document.id`. Dans le cas des CSV, les propriétés sont nommées grâce à l'entête de chaque colonne. Si votre entête contient des espaces ou des caractères spéciaux, vous pouvez utiliser la notation alternative `document[\"cle avec des espaces\"]`.\n"
    empty: "Non défini"
    ExPropertyAccessor: Propriété
    ExRawAccessor: 'Valeur brute'
  CHANNEL_ADAPTER_TYPES:
    api: 'API (HTTP)'
    ftp: FTP
    local: 'CDN Sinfin'
    ssh2_ftp: SSH2 FTP
  CHANNEL_FORMAT_TYPES:
    csv: CSV
    json: JSON
    xml: XML
  CHANNEL_SOURCE_TYPES:
    Content: Contenus
    Media: Médias
    Product: Produits
  CHANNEL_TYPE:
    magento: Magento
    magento_orders: Commandes Magento
    mirakl: Mirakl
    'null': Non typé
  DOCUMENT_TYPES:
    Category: Catégorie
    Content: Contenu
    Media: Media
    Product: Produit
    _: Types
    _singular: Type
    'null': Tous
  EAV_TYPES:
    _: 'Types EAV'
    _singular: 'Type EAV'
    attribute: Attribut
    attribute_group: "Groupe d'attribut"
    category: Categorie
    content: Contenu
    media: Media
    product: Produit
  EXPORT_ACCESSOR_TYPES:
    attribute: Attribut
    completness: Completness
    formula: Formule
    property: Propriété
    properties: Propriétés
    raw: 'Valeur brute'
  EXPORT_ADAPTER_TYPES:
    ftp: FTP
    local: 'Local (téléchargement)'
  EXPORT_FORMATS:
    csv_comma: 'CSV (,)'
    csv_pipe: 'CSV (|)'
    csv_semicolon: 'CSV (;)'
    csv_tab: 'CSV (tab)'
    xlsx: 'Excel XLSX'
  EXPORT_FORMAT_TYPES:
    csv: CSV
    json: JSON
  HUB_ORIGIN_FILTERS:
    owner: Origine
    origin: Marchand
  HUB_PRODUCT_FILTERS:
    price: 'Prix'
    quantity: 'Quantité'
  HUB_SOURCE_TYPES:
    warehouse: 'Entrepôt'
  JSON_OPTIONS:
    _: 'Options JSON'
    _singular: 'Option JSON'
    default: Defaut
    materials: Materials
    references_groups: 'Groupes de références'
    virtual_tour: 'Visite virtuelle'
    reverse_image_search: "Recherche d'image"
  MEDIA_EXPORTS_PRESETS:
    FACEBOOK: 'Publication Facebook'
    INSTAGRAM_POST: 'Publication instagram'
    INSTAGRAM_REEL: 'Reel Story'
    INSTAGRAM_STORY: 'Instagram Story'
    LINKEDIN_BANNER: 'LinkedIn article banner'
    LINKEDIN_IMAGE: 'LinkedIn featured image'
    LINKEDIN_POST_PORTRAIT: 'Post LinkedIn (portrait)'
    LINKEDIN_POST_SQUARE: 'Post LinkedIn (carré)'
    WHATSAPP: 'Status Whatsapp'
  OPERATORS:
    _: 'Opérateurs'
    _singular: 'Opérateur'
    AND: 'Et'
    OR: 'Ou'
    CONTAINS: Contient
    ENDS_WITH: Se termine par
    EQ: Est égal à
    EXCLUDES: Ne contient pas
    GT: Est supérieur à
    GTE: Est supérieur ou égal à
    IN: Contient
    LIKE: Contient
    LT: Est inférieur à
    LTE: Est inférieur ou égual à
    NEQ: Est différent de
    STARTS_WITH: Commence par
    EXISTS: Existe
    INTERSECT_LEAST_ONE: Contient au moins 1 élément de
  ORDERS_STATES:
    ACCEPTED: Acceptée
    AVOIR: Avoir
    CANCELED: Annulée
    DECLINED: Refusée
    PARTIALLY_SHIPPED: 'Partiellement expédiée'
    PENDING_FOR_VALIDATION: 'En attente de validation'
    pending: 'En attente de validation'
    SHIPPED: Expédiée
    _: 'États de commande'
    _notFound: 'État de commande non trouvé'
    _singular: 'État de commande'
  ORDER_TRANSITIONS:
    accept: 'Commande acceptée !'
    cancel: 'Commande annulée !'
    decline: 'Commande refusée !'
  ROLES:
    ROLE_SUPER_ADMIN: 'Super admin'
    ROLE_TECH: Tech
    ROLE_USER: User
    _: Rôles
    _singular: Rôle
  RULE_LEVELS:
    _: 'Niveaux de règle'
    _singular: 'Niveau de règle'
  RULE_TYPES:
    _: 'Types de règle'
    _singular: 'Type de règle'
errors:
  400: 'Requête incorrecte'
  401: 'Non autorisé'
  402: 'Paiement requis'
  403: Interdit
  425: 'Trop tôt'
  429: 'Trop de requêtes'
  500: 'Erreur interne du serveur'
  accept_condition: "Vous devez accepter les conditions d'utilisation."
  bad_login: 'Veuillez vérifier vos identifiants de connexion'
  create: 'Erreur lors de la création'
  default: 'Une erreur est survenue, veuillez réessayer plus tard'
  delete: 'Erreur lors de la suppression'
  loading: 'Erreur lors du chargement des données.'
  maxLength: 'Ce champ doit contenir au maximum {{max}} caractères'
  maxNumbers: 'Ce champ doit contenir au maximum {{max}} caractères'
  max_select: 'Vous ne pouvez pas sélectionner plus de {{max}} éléments'
  media_wrong: 'Impossible de mettre un média ici'
  minArrayLength: 'Ce champ doit contenir au moins {{min}} élément(s)'
  minLength: 'Ce champ doit contenir au moins {{min}} caractères'
  minLowercase: 'Ce champ doit contenir au moins {{min}} minuscule(s)'
  minNumbers: 'Ce champ doit contenir au moins {{min}} chiffre(s)'
  minSymbols: 'Ce champ doit contenir au moins {{min}} caractère(s) spécial'
  minUppercase: 'Ce champ doit contenir au moins {{min}} majuscule(s)'
  must_be_email: "Cet email n'est pas correct"
  must_be_number: 'Ce champ doit être un nombre'
  no_changes: "Aucun changement n'a été effectué"
  passwords_must_match: 'Les mots de passe doivent être identiques'
  product_not_found: 'Produit introuvable, retourner à'
  required_field: 'Ce champ est requis'
  save: 'Erreur lors de la sauvegarde des données. Veuillez vérifier vos champs ou réessayez plus tard.'
  selection: 'Erreur lors de la sélection'
  update: 'Erreur de la modification'
export:
  search: 'Rechercher un attribut'
  search_property: 'Rechercher une propriété'
filters:
  from: Depuis
  preferences:
    choose_name: 'Saisissez un nom'
    choose_name_error: 'Ce nom est déjà utilisé'
    empty_list: 'Aucune préférence de filtre'
    empty_search: 'Aucun résultat pour cette recherche'
    filters_preferences: 'Préférences des filtres'
    title: Préférences
  to: Jusque
helper:
  code: 'Le code doit être unique et ne doit contenir que des lettres, des chiffres et des underscores (_)'
  hub:
    avoir: "Dans le cas des avoirs, tous les montants doivent être saisis en valeurs positives, à l'exception du \"Montant total\", qui devrait être négatif."
  hub_item:
    avoir: "Dans le cas des avoirs, tous les montants doivent être saisis en valeurs positives."
homepage:
  appbar:
    profileManagement: 'Gestion du profil'
    yourPlan: 'Votre formule'
  pop_sync_msg: "La synchronisation permet de récupérer les informations de vos connecteurs (langues, groupes d'attributs, attributs, options, etc.). En raison de la charge de travail de nos serveurs, ce processus peut prendre un certain temps. Veuillez noter que vous ne pouvez demander une synchronisation qu'une fois toutes les 15 minutes."
  stats:
    catalog_scope_count: Connecteur
    product_errors: 'Erreurs produit'
    scope_count: Scopes
    stats_at: 'Les statistiques sont mises en cache pendant 60 minutes, dernière mise à jour le {{date}}'
    total_products: Produits
  title: Dashboard
hubDashboard:
  _: 'Tableau de bord'
  averageOrder: 'Panier moyen'
  caByOrigin: 'Chiffre d''affaire par origine'
  dashboard: 'Tableau de bord'
  orderNumber: 'Total des commandes'
  orderReports: 'Rapports de commandes'
  orders: Commandes
  overview: "Vue d'ensemble"
  revenue: "Ventes totales (€ TTC)"
  currentWeek: 'Semaine en cours'
  lastWeek: 'Semaine dernière'
  byDay: 'Par jour'
  byWeek: 'Par semaine'
  byMonth: 'Par mois'
  clientsStatTitle: Nouveaux et anciens clients
  topProducts: 'Top produits'
  loadingData: 'Chargement des données...'
  oldClients: Anciens clients
  newClients: Nouveaux clients
  noData: 'Aucune donnée disponible'
  salesReports: 'Rapports de ventes'
  stockNumber: 'Total des stocks'
hubSalesReports:
  CAByOrigin: 'CA par origine'
  byOrigin: 'Par origine'
  bySubOrigin: 'Par sous origine'
hubOrdersReports:
  delay: Délais moyen d'expédition
  delayDelivery: Délais moyen de livraison
hubStocksReports:
  state: 'État du stock'
  count_units: 'Nombre d''unités'
  stock_value: 'Valeur du stock'
  stock_sold: 'Stock vendu'
hubSources:
  address: 'Adresse'
  zipCode: 'Code postal'
  phone: 'Téléphone'
  count_ref: 'Nombre de références'
  count_units: 'Nombre d''unités'
  stock_value: 'Valeur du stock'
inputs:
  accountsList:
    label: 'Liste des comptes'
  all_priorities: 'Toutes priorités'
  application:
    label: Application
  attribute: Attribut
  attributes: Attributs
  attributes_group: "Groupe d'attributs"
  catalog: Catalogue
  catalog_scope: Connecteur
  catalogs: Catalogues
  categories: Catégories
  categoriesList:
    label: 'Liste des catégories'
  channels: Channels
  chose_attributes_to_export: 'Choisissez les attributs à exporter'
  chose_properties_to_export: 'Choisissez les propriétés à exporter'
  code: Code
  color: Couleur
  count: Nombre
  created_at: 'Créé le'
  description: Description
  destination: Destination
  detected: Détecté
  disabled: Désactivé
  discountCode:
    label: 'Code de réduction'
  email: 'Adresse e-mail'
  enabled: Activé
  error_email: 'Veuillez renseigner une adresse email valide'
  errors: Erreurs
  eshopList:
    label: 'Liste des e-shop'
  files: Fichiers
  finalStatus: 'Statut final'
  firstname: Prénom
  helpers:
    code: 'Le code doit être unique et ne doit contenir que des lettres, des chiffres et des underscores (_)'
    selectChip: 'Utilisez la touche entrée pour valider les valeurs si vous souhaitez les rajouter manuellement.'
  image: Image
  isBlocking: 'Est bloquant'
  isSuperAdmin: Super-admin
  labels:
    confirm-password: 'Confirmer le mot de passe'
    password: 'Mot de passe'
    submit: Enregistrer
  lastname: Nom
  level: Niveau
  locale: Langue
  locales: Locales
  method:
    label: Méthode
  name: Nom
  new_password: 'Nouveau mot de passe'
  offerValue:
    label: "Valeur de l'offre"
  old_password: 'Mot de passe actuel'
  password: 'Mot de passe'
  passwordConfirmation: 'Confirmation du mot de passe'
  password_helper: 'Le mot de passe doit contenir au moins 8 caractères, 1 chiffre, 1 lettre minuscule, 1 majuscule et 1 caractère spécial'
  period: Période
  priority: Priorité
  products: Produits
  rule: Règle
  rules: Règles
  rules_group: 'Groupe de règles'
  rules_groups: 'Groupes de règles'
  scope: Scope
  searchBy: 'Rechercher par {{fields}}'
  select_value: 'Sélectionner une valeur'
  separator: Format
  source: Source
  status: Statut
  steps: Étapes
  success_email: 'Si votre adresse e-mail existe vous allez recevoir un email pour réinitialiser votre mot de passe'
  text_collection:
    placeholder: 'Écrivez votre texte et tapez entrée pour ajouter'
  toast:
    errors:
      fill: 'Veuillez renseigner les champs'
      same-password: 'Les mots de passe ne sont pas identiques'
  triggerCondition: 'Condition de déclenchement'
  updated_at: 'Modifié le'
  user_groups: "Groupes d'utilisateurs"
  users: Utilisateurs
  value: Valeur
  with_images: Avec les images
  workflow: Workflow
items:
  Address:
    _: Adresses
    _create: Créer
    _singular: Adresse
  Attribute:
    _: Attributs
    _create: Créer
    _created: 'Attribut créé avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer l''attribut "{{label}}" ?'
    _deleted: 'L''attribut "{{label}}" a bien été supprimé.'
    _notFound: 'Attribut non trouvé'
    _position_saved: 'Positions mis à jour avec succès !'
    _saved: 'Attribut enregistré avec succès !'
    _singular: Attribut
    code: Code
    counters:
      options: Options
      uses: Usage
    group: Groupe
    isLocalizable: Localisable
    isRequired: Requis
    isScopable: Scopable
    isSearchable: Recherchable
    names: Nom
    status: Activé
    type: Type
  AttributeGroup:
    _: "Groupes d'attributs"
    _create: Créer
    _created: "Groupe d'attributs créé avec succès !"
    _delete: 'Êtes-vous sûr de vouloir supprimer le groupe d''attributs "{{label}}" ?'
    _deleted: 'Le groupe d''attributs "{{label}}" a bien été supprimé.'
    _notFound: "Aucun groupe d'attributs trouvé"
    _saved: "Groupe d'attributs enregistré avec succès !"
    _singular: "Groupe d'attributs"
    code: Code
    counters:
      attributes: Attributs
      options: Options
      uses: Usage
    names: Nom
    status: Activée
  AttributeOption:
    _: Options
    _create: Créer
    _created: 'Attribut option créé avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer l''attribut option "{{label}}" ?'
    _deleted: 'L''attribut option "{{label}}" a bien été supprimé.'
    _notFound: 'Attribut option non trouvé'
    _saved: 'Attribut option enregistré avec succès !'
    _singular: Option
    attribute: Attribut
    code: Code
    names: Nom
  Catalog:
    _: Catalogues
    _create: Créer
    _created: 'Catalogue créée avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer le catalogue "{{label}}" ?'
    _deleted: 'Le catalogue "{{label}}" a bien été supprimée.'
    _notFound: 'Catalogue non trouvée'
    _saved: 'Catalogue enregistrée avec succès !'
    _singular: Catalogue
    categories: Catégories
    code: Code
    descriptions: Description
    document_type: Type
    filters: Filtres
    main: Information
    names: Nom
    products: Produits
    status: Activé
    upload: 'Média'
  CatalogScope:
    _: Channels
    _create: Créer
    _created: 'Channel créé avec succès !'
    _deleted: 'Le channel "{{label}}" a bien été supprimé.'
    _notFound: 'Aucun résultat'
    _saved: 'Le channel a bien été modifié !'
    _singular: Channel
    catalog: Catalogue
    code: Code
    magento_config_error: 'Il semblerait que les paramètres de connexion à magento ne soient pas correctement configurés pour ce channel.'
    hub_order: Commandes
    hub_order_waiting_for_validation: 'En attente de validation'
    order_waiting: 'Commandes magento en attente de validation'
    infos: Détails
    locales: Langues
    locale: Langue
    last_sync_state: Etat de la dernière synchro.
    last_synced_products: Derniers produits synchronisés
    mapping: Mapping
    attribute_mapping: Mapping des attributs
    names: Nom
    settings: Paramètres
    scope: Scope
    status: Activé
    synchro: Synchros
    workflow: Workflow
  Category:
    _: Catégories
    _create: Créer
    _create_button: 'Créer une catégorie'
    _create_category: 'Créer une catégorie'
    _created: 'Catégorie créée avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer la catégorie "{{label}}" ?'
    _deleted: 'La catégorie "{{label}}" a bien été supprimée.'
    _notFound: 'Catégorie non trouvée'
    _rename: 'Renommer la catégorie'
    _saved: 'Catégorie enregistrée avec succès !'
    _singular: Catégorie
    _type: Catégorie
    color: Couleur
    name: Nom
    parent: Parent
    status: Activée
  CategoryData:
    _: 'Données catégorie'
  Channel:
    _: Channels
    _singular: Channel
    type: Type
    mapping: Mapping
  ChannelAdapter:
    _singular: Adapter
    apiKey: Clé API
    data: Paramètres
    data_help: 'Un couple clé/valeur par ligne séparé par deux points (:).'
    headers: 'Entêtes HTTP'
    headers_help: 'Un couple clé/valeur par ligne séparé par deux points (:).'
    method: Méthode
    url: URL
    consumerKey: Clé client
    consumerSecret: Secret client
    accessToken: Token d'accès
    accessTokenSecret: Secret d'accès
    magento_settings: Paramètres Magento
    mirakl_settings: Paramètres Mirakl
    signatureMethod: Méthode de signature
    storeCode: Code boutique
  ChannelSchedule:
    _singular: Planification
    hourly: 'Toutes les heures.'
    description: 'Choisissez les heures à laquelle vous souhaitez déclencher cet export automatiquement.'
  ChannelMapping:
    _singular: Mapping
  ChannelFormat:
    _singular: Format
  ChannelSource:
    _singular: Source
  ChannelFilter:
    _singular: Filtre
    addRow: 'Ajouter une condition'
    addGroup: 'Ajouter un groupe de conditions'
  ChannelWorkflowExport:
    _: 'Exports de workflow'
    selectedCategories: 'Catégories sélectionnées'
    recommended: 'Recommandé'
    required: 'Obligatoire'
  Communication:
    _: Communications
  Completude:
    _: Complétudes
    _create: Créer
    _created: 'Completude créée avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer la complétude {{label}} ?'
    _deleted: 'La complétude {{label}} a bien été supprimée.'
    _saved: 'Complétude enregistrée avec succès !'
    _singular: Complétude
    _updated: 'Complétude enregistrée avec succès !'
    attributes: Attributs
    code: Code
    missing: Manquant
    missing_attributes: 'Les attributs suivants ne sont pas associés à ce produit :'
    names: Name
    not_exist_attributes: "Les attributs suivants n'existent pas :"
  Config:
      _: Configurations
      _add: 'Créer une configuration'
      _create: Créer
      _created: 'Configuration créée avec succès !'
      _notFound: 'Aucun configuration trouvée'
      _saved: 'Configuration enregistrée avec succès !'
      _singular: Configuration
      key: Clé
      _types:
        pdf:
          _: HUB/ Configuration de l'entête des reçus de commande
          additionalText: Texte additionnel
          documentTitle: Titre du document
          email: Email
          logo: Logo
          name: Nom
          siren: SIREN
          siret: SIRET
          vat: Numéro de TVA intracommunautaire
          address:
            _: Adresse
            city: Ville
            company: Entreprise
            country: Pays
            firstname: Prénom
            lastname: Nom
            mobile: Mobile
            phone: Téléphone
            postcode: Code postal
            region: Région
            street: Rue
            street2: "Complément d'adresse"
        pdf_shipping:
          _: HUB/ Configuration de l'entête des bons de livraisons
          additionalText: Texte additionnel
          additionalHeaderText: Texte d'en tête additionnel
          documentTitle: Titre du document
          email: Email
          logo: Logo
          name: Nom
          siren: SIREN
          siret: SIRET
          vat: Numéro de TVA intracommunautaire
          weightAttribute: Attribut de poids (décimale en KG)
          address:
            _: Adresse
            city: Ville
            company: Entreprise
            country: Pays
            firstname: Prénom
            lastname: Nom
            mobile: Mobile
            phone: Téléphone
            postcode: Code postal
            region: Région
            street: Rue
            street2: "Complément d'adresse"
        environment:
          _: TECH/ Configuration de l'environnement
          defaultLang: Langue par défaut
          langs: Langues
          modules: Modules
  Content:
    _: Contenus
    _add: 'Créer un contenu'
    _create: Créer
    _created: 'Contenu créé avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer le contenu "{{label}}"?'
    _deleted: 'Contenu supprimé avec succès !'
    _notFound: 'Aucun contenu trouvé'
    _saved: 'Contenu enregistré avec succès !'
    _singular: Contenu
    _type: Contenu
    name: Nom
    path: Path
    picker: 'Sélecteur de contenu'
    pickers: 'Sélecteur de contenus'
    select: 'Sélectionner un contenu'
    selects: 'Sélectionner des contenus'
    status: Activé
    upload: 'Média'
  ContentData:
    _: 'Données Contenu'
  ContentFolder:
    _: Dossiers
    _create: 'Créer un dossier'
    _created: 'Dossier créé avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer le dossier "{{label}}"?'
    _deleted: 'Le dossier {{label}} a bien été supprimé.'
    _notFound: 'Aucun dossier trouvé'
    _rename: 'Renommer le dossier'
    _saved: 'Dossier enregistré avec succès !'
    _singular: Dossier
    name: Nom
  Currency:
    _: Devises
    _create: Créer
    _created: 'Devise créée avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer la devise "{{label}}" ?'
    _deleted: 'La devise "{{label}}" a bien été supprimée.'
    _notFound: 'Devise non trouvée'
    _saved: 'Devise enregistrée avec succès !'
    _singular: Devise
    _updated: 'Devise enregistrée avec succès !'
    code: Code
    names: Nom
    symbol: Symbole
  Dashboard:
    Catalog: Catalogues
    Locale: Langues
    last30Days: '30 derniers jours'
    AttributeGroup: "Groupes d'attributs"
    Attribute: Attributs
    AttributeOption: Options
    Scope: Scopes
    CatalogScope: Channels
    ProductError: Erreurs
    Rule: Règles
    RuleGroup: 'Groupes de règles'
    Workflow: Workflows
    User: Utilisateurs
    UserGroup: "Groupes d'utilisateurs"
  Dictionary:
    _: Dictionnaires
    names: Nom
    code: Code
    _create: Créer
    _created: 'Dictionnaire créé avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer le dictionnaire "{{label}}" ?'
    _deleted: 'Le dictionnaire "{{label}}" a bien été supprimé.'
    _notFound: 'Dictionnaire non trouvé'
    _saved: 'Dictionnaire enregistré avec succès !'
    _singular: Dictionnaire
    _mapping: 'Mapping'
  DictionaryMapping:
    _: 'Mappings de dictionnaire'
    _create: 'Créer un mapping'
    _created: 'Mapping créé avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer le mapping "{{label}}" ?'
    _deleted: 'Le mapping "{{label}}" a bien été supprimé.'
    _notFound: 'Mapping non trouvé'
    _saved: 'Mapping enregistré avec succès !'
    _singular: Mapping
    dictionary: Dictionnaire
    from: De
    to: À
  Export:
    Mapping:
      column: Colonne
      in: Sinfin
      name: Colonne
      name_help: 'Nom de la colonne CSV ou de la propriété JSON dans votre export.'
      out: Export
      root: 'Colonne racine'
      root_help: "Si votre flux JSON ou XML contient une structure plus complexe, vous pouvez indiquer ici le chemin vers le noeud contenant la liste de documents à importer (exemple : `cle1.cle2.cle3`). Sinfin doit être en mesure de récupérer une liste de documents pour les parcourir.\n"
      type_choose: 'Choisissez le type de contenu de votre cellule'
    MappingColumn:
      code: Entête
      code_help: 'Soit le nom de la colonne CSV soit le nom de la clé JSON.'
      requiresColumns: 'Certaines colonnes sont obligatoires pour terminer la configuration.'
      willGoTo: 'Sera exporté dans …'
    _: Exports
    _confirm_singular: 'Voulez-vous vraiment déclencher cet export ?'
    _create: Créer
    _created: 'Export créé avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer l''export "{{label}}" ?'
    _deleted: 'L''export "{{label}}" a bien été supprimé.'
    _error: 'Export échoué !'
    _launch: Déclencher
    _none: 'Aucune colonne'
    _notFound: 'Export non trouvé'
    _saved: 'Export enregistré avec succès !'
    _singular: Export
    _success: 'Export terminé !'
    accessor: 'Type de valeur'
    adapter: Destination
    all: 'Exporter tout'
    attribute: Attribut
    connected: Connecté
    cron: Cron
    currentView: 'Exporter la vue actuelle'
    filename: 'Nom du fichier'
    format: Format
    formula: Formule
    helperAdapter: 'Choisissez où le fichier sera envoyé.'
    helperCron: 'Choisissez les heures auxquelles vous souhaitez déclencher cet export automatiquement.'
    helperMapping: 'Selectionnez la gestion de données pour la colonne.'
    host: Hôte
    lastRun: 'Dernier déclenchement'
    mapping: Mapping
    names: Nom
    nextRun: 'Prochain déclenchement'
    notConnected: 'Non connecté'
    password: 'Mot de passe'
    port: Port
    property: Propriété
    raw: Constante
    scheduler: Planification
    source: Type
    username: Utilisateur
    uuid: UUID
    warningMaxColumns: 'Le nombre maximum de colonnes est de 256.'
  HubInvoice:
    _: Comptabilité
    _singular: Comptabilité
  HubOrder:
    AVOIR: Avoir
    CANCELED: Annulée
    DECLINED: Refusée
    PARTIALLY_SHIPPED: 'Partiellement expédiée'
    PENDING_FOR_VALIDATION: 'En attente de validation'
    SHIPPED: Expédiée
    VALIDATED: Validée
    _: Commandes
    _create: Créer
    _created: 'Commande créée avec succès !'
    _deleted: 'Commande supprimée avec succès !'
    _refresh: 'Rafraîchir la commande'
    _refreshed: 'Commande mise à jour avec succès !'
    _singular: Commande
    _singular_avoir: Avoir
    _updated: 'Commande modifiée avec succès !'
    accept: 'Êtes-vous sûr de vouloir accepter cette commande ?'
    acceptedAt: 'Commande acceptée le'
    cancel: 'Êtes-vous sûr de vouloir annuler cette commande ?'
    decline: 'Êtes-vous sûr de vouloir refuser cette commande ?'
    declinedAt: 'Commande refusée le'
    address: Addresse
    amount: Montant
    billing: Facturation
    billing_informations: 'Informations de facturation'
    ca: 'Ventes totales'
    company: Entreprise
    commentary: Commentaire
    createdAt: 'Date de commande'
    createAt: 'Commande créée le'
    customer: Client
    city: Ville
    discount_amount: 'Montant de la remise'
    email: Email
    excluding_tax: HT
    forms:
      backProducts: 'Retourner à la liste des produits'
      contact: Contact
      identification: Identification
      price: 'Prix (HT)'
      selectedProducts: 'Produits séléctionnés'
      showProducts: 'Afficher les produits séléctionnés'
      toggle: "L'adresse de facturation est la même que l'adresse de livraison"
      total: Total
      totalPrice: 'Total des produits'
      tva: 'TVA (%)'
    history: Historique
    inputs:
      additionalNote: 'Note complémentaire'
      city: Ville
      company: Entreprise
      country: Pays
      currencyCode: Devise
      discountAmount: 'Montant de la remise'
      email: Email
      firstname: Prénom
      importedAt: 'Importée le'
      lastname: Nom
      marketplace: Marketplace
      mobile: Mobile
      owner: Propriétaire
      phone: Téléphone
      postcode: 'Code postal'
      products: Produits
      reference: Référence
      region: Région
      shippingAmount: 'Frais de port'
      state: État
      street: Adresse
      street2: "Complément d'adresse"
      subTotal: Sous-total
      taxAmount: 'Montant de la taxe'
      totalAmount: 'Montant total'
    name: Nom
    order_average: 'Panier moyen'
    order_count: 'Nombre de commandes'
    order_number: 'N° de commande'
    total_orders: 'Total des commandes'
    ordered_products: 'Produits commandés'
    origin: Origine
    owner: Propriétaire
    ownerIdentifier: Réference
    phone: Téléphone
    postcode: 'Code postal'
    price: Prix total (TTC)
    product_name: 'Nom du produit'
    provider: Prestataire
    quantity: Quantité
    seeInvoice: 'Voir le PDF'
    quantitySold: Quantité vendue
    sentBy: 'Expédié par'
    sentTime: 'Expédié le'
    ship: 'Êtes-vous sûr de vouloir expédier cette commande ?'
    shipments: Expéditions
    shipment: Expédition
    shipping: Livraison
    shipping_amount: 'Frais de port'
    shipping_date: "Date d'expédition"
    shipping_informations: 'Informations de livraison'
    sku: SKU
    status: Status
    subTotal: Sous-total
    tabs:
      informations: Informations
      moreInformations: 'Informations complémentaires'
      products: Produits
      tax: Taxes
    tax: TTC
    taxPart: Dont taxe
    toggleInformations: "Adresse de facturation identique à l'adresse de livraison"
    total: Total TTC
    total_excl_tax: 'Total HT'
    total_amount: 'Montant total'
    total_price: Total
    track: 'Suivre le colis'
    tracking: Suivi
    trackingNumber: 'N° de suivi'
    unit_price: 'Prix unitaire (TTC)'
    unit_price_info: 'avec ou sans la remise appliquée'
    updatedAt: 'Mis à jour le'
  HubOrderItem:
    _: 'Commandes (produits)'
    _singular: 'Commande (produits)'
    _create: Créer
  HubShipment:
    _: Expéditions
    _create: Créer
    carrier: Transporteur
    trackingLink: Lien de tracking
    trackingNumber: Numéro de tracking
  HubStock:
    _: Stocks
    _create: Créer
    _created: 'Stock créé avec succès !'
    _deleted: 'Stock supprimé avec succès !'
    _saved: 'Stock enregistré avec succès !'
    _singular: Stock
    _updated: 'Stock modifié avec succès !'
    createdAt: 'Date de création'
    gte: 'Quantité supérieure ou égale à'
    id: ID
    lte: 'Quantité inférieure ou égale à'
    product: SKU
    productName: Produit
    quantity: Quantité
    updatedAt: 'Date de modification'
    source: Source
  HubSource:
    _: Sources
    _create: Créer
    _created: 'Source créée avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer la source "{{label}}" ?'
    _deleted: 'La source "{{label}}" a bien été supprimé.'
    _saved: 'Source modifiée avec succès !'
    _singular: Source
    _updated: 'Source modifiée avec succès !'
    code: Code
    names: Nom
    status: Activé
    type: Type
    upload: 'Média'
  HubOrigin:
    _: Origines
  Import:
    Mapping:
      column: Colonne
      in: Import
      name: Colonne
      name_help: 'Nom de la colonne CSV ou de la propriété JSON dans votre export.'
      out: Sinfin
      root: 'Colonne racine'
      root_help: "Vous devez fournir une liste d'éléments à l'import pour qu'il fonctionne. Il arrive cependant que les fichiers sources contiennent des structure plus complexes avec des objets imbriqués. Dans cette situation, il vous est possible de nous indiquer comment retrouver le tableau dans votre structure en précisant son chemin d'accès (exemple : `clé1.clé2.clé3`).\n"
      type_choose: 'Choisissez le type de contenu de votre cellule'
    MappingColumn:
      code: Entête
      code_help: 'Soit le nom de la colonne CSV soit le nom de la clé JSON.'
      requiresColumns: 'Certaines colonnes sont obligatoires pour terminer la configuration.'
      willGoTo: 'Sera importé dans …'
    _: Imports
    _confirm_singular: 'Voulez-vous vraiment déclencher cet import ?'
    _create: Créer
    _created: 'Import créé avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer l''import "{{label}}" ?'
    _deleted: 'L''import "{{label}}" a bien été supprimé.'
    _error: 'Import échoué !'
    _launch: Déclencher
    _none: 'Aucune colonne'
    _notFound: 'Import non trouvé'
    _saved: 'Import enregistré avec succès !'
    _singular: Import
    _success: 'Import terminé !'
    accessor: 'Type de valeur'
    adapter: Source
    attribute: Attribut
    connected: Connecté
    cron: Cron
    filename: 'Nom du fichier'
    format: Format
    formula: Formule
    helperAdapter: 'Choisissez où le fichier sera téléchargé.'
    helperCron: 'Choisissez les heures auxquelles vous souhaitez déclencher cet import automatiquement.'
    helperMapping: 'Selectionnez la gestion de données pour la colonne.'
    host: Hôte
    lastRun: 'Dernier déclenchement'
    mapping: Mapping
    names: Nom
    nextRun: 'Prochain déclenchement'
    notConnected: 'Non connecté'
    password: 'Mot de passe'
    port: Port
    property: Propriété
    raw: Constante
    scheduler: Planification
    source: Type
    username: Utilisateur
    uuid: UUID
  Layout:
    _: Affichage
    _saved: 'Affichage enregistré avec succès !'
    attribute: Attribut
    configure: Configurer
    content_detail: 'Détail contenu'
    content_listing: 'Listing contenu'
    general_attributes: 'Attributs généraux'
    image: Image
    media_detail: 'Fiche média'
    media_listing: 'Listing média'
    none_visible_attributes: 'Attributs non visibles'
    product_detail: 'Fiche produit'
    product_listing: 'Listing produit'
    product_listing_settings: 'Configuration listing produit'
    search: 'Rechercher dans la plateforme Sinfin™ …'
    top_attributes: 'Top attributs'
    visible_attributes: 'Attributs visibles'
  Locale:
    _: Langues
    _create: Créer
    _created: 'Langue créée avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer la langue "{{label}}" ?'
    _deleted: 'La langue "{{label}}" a bien été supprimée.'
    _notFound: 'Langue non trouvée'
    _saved: 'Langue enregistrée avec succès !'
    _singular: Langue
    code: Code
    names: Nom
    status: Activée
  Mapping:
    column: Colonne
    name: Colonne
    name_help: 'Nom de la colonne CSV ou de la propriété JSON dans votre export.'
    type_choose: 'Choisissez le type de contenu de votre cellule'
  MappingColumn:
    _: Colonnes
    _create: Créer
    availableOptions: 'Options disponibles'
    name: Nom
  MappingColumnDefinition:
    _: Définition des colonnes
    _create: Créer
  MeasureFamily:
    _: 'Familles de mesure'
    _singular: 'Famille de mesure'
    code: Code
    default: Défaut
    names: Nom
    units: Unités
  Media:
    _: Médias
    _add: 'Ajouter un média'
    _create: Créer
    _created: 'Média créé avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer le média "{{label}}"?'
    _deleted: 'Le média {{label}} a bien été supprimé.'
    _notFound: 'Aucun média trouvé'
    _saved: 'Média enregistré avec succès !'
    _singular: Média
    _type: DAM
    _unzip: "Décompression en cours..."
    exports: 'Exports prédéfinis'
    format: Format
    formats: Formats
    name: Nom
    path: Chemin
    picker: 'Sélecteur de média'
    pickers: 'Sélecteur de médias'
    size: Taille
    source: Source
    status: Activé
    type: Type
    upload: 'Média'
    uploaded: 'Fichier téléversé'
  MediaData:
    _: 'Données média'
  MediaFolder:
    _: Dossiers
    _create: 'Créer un dossier'
    _created: 'Dossier créé avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer le dossier "{{label}}"?'
    _deleted: 'Le dossier {{label}} a bien été supprimé.'
    _notFound: 'Aucun dossier trouvé'
    _rename: 'Renommer le dossier'
    _saved: 'Dossier enregistré avec succès !'
    _singular: Dossier
    name: Nom
  PasswordUpdate:
    _created: 'Mot de passe modifié avec succès !'
  Product:
    _: Produits
    _create: Créer
    _created: 'Produit créé avec succès !'
    _deleted: 'Produit supprimé avec succès !'
    _refresh: 'Rafraîchir le produit'
    _refreshed: 'Product mis à jour avec succès !'
    _saved: 'Produit enregistré avec succès !'
    _singular: Produit
    _type: PIM
    picker: 'Sélecteur de produit'
    pickers: 'Sélecteur de produits'
    price: 'Prix'
    select: 'Sélectionner un produit'
    select_attributes: 'Choisissez les attributs à afficher'
    selects: 'Sélectionner des produits'
    sku: SKU
    status: Activé
    upload: 'Média'
    uuid: Identifiant unique du produit
    createdAt: 'Date de création'
    updatedAt: 'Date de modification'
  ProductData:
    _: 'Données Produit'
  DocumentErrorSummary:
    _: Erreurs
    _singular: Erreur
    catalogScope: Flux
    identifier: Id. Doc.
    level: Niveau
    locale: Langue
    rule: Règle
    sku: SKU
    type: Type
    errorTargets: "Attributs ciblés"
  ProductSync:
    _confirm: 'Êtes-vous sûr de vouloir synchroniser les produits {{skus}} ?'
    _confirm_singular: 'Êtes-vous sûr de vouloir synchroniser le produit {{skus}} ?'
    _error: 'Une erreur est survenue lors de la synchronisation du produit.'
    _success: 'Demande de synchronisation prise en compte !'
  Rule:
    _: Règles
    _create: Créer
    _created: 'Règle créée avec succès !'
    _deleted: 'La règle "{{label}}" a bien été supprimé.'
    _description: 'Description de la règle'
    _error: "Quand la règle n'est pas respectée"
    _rule: 'Condition et Règle'
    _saved: 'Règle enregistrée avec succès !'
    _singular: Règle
    attribute: Attribut
    attributes: Attributs
    code: Code
    create: 'Créer une règle'
    descriptions: Description
    errorTargets: "Où ranger l'erreur ?"
    isBlocking: Bloquant
    isBlocking_title: 'Doit-on empêcher le workflow de continuer ?'
    level: Niveau
    level_title: "Quel niveau d'erreur est provoqué ?"
    names: Nom
    rule: Règle
    triggerCondition: Condition
    type: Type
  RuleGroup:
    _: 'Groupes de règles'
    _create: Créer
    _created: 'Groupe de règles créé avec succès !'
    _deleted: 'Le groupe de règles "{{label}}" a bien été supprimée.'
    _notFound: 'Aucun résultat'
    _saved: 'Groupe de règles modifié avec succès !'
    _singular: 'Groupe de règles'
    code: Code
    counters:
      rules: Règles
    create: 'Créer un groupe de règles'
    names: Nom
    steps: Étapes
    triggerCondition: Condition
  Scope:
    _: Scopes
    _create: Créer
    _created: 'Scope créé avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer le scope "{{label}}" ?'
    _deleted: 'Le scope "{{label}}" a bien été supprimé.'
    _notFound: 'Aucun résultat'
    _saved: 'Scope enregistré avec succès !'
    _singular: Scope
    code: Code
    names: Nom
  Template:
    _: Modèles
    _create: Créer
    _created: 'Modèle créé avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer le modèle "{{label}}" ?'
    _deleted: 'Le modèle "{{label}}" a bien été supprimé.'
    _notFound: 'Modèle non trouvé'
    _saved: 'Modèle enregistré avec succès !'
    _seeAttributeGroups: "Voir les groupes d'attributs"
    _singular: Modèle
    attributeGroups: "Groupes d'attributs"
    code: Code
    eavTypes: 'Types de document'
    eavTypes_help: "Limiter l'usage de ce template à certains types de document."
    names: Nom
    search_attribute_group: 'Rechercher un attribute group'
    status: Status
  Unit:
    _: 'Unités de mesure'
    _create: Créer
    _created: 'Unité de mesure créée avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer l''unité de mesure "{{label}}" ?'
    _deleted: 'L''unité de mesure "{{label}}" a bien été supprimée.'
    _saved: 'Unité de mesure enregistrée avec succès !'
    _singular: 'Unité de mesure'
    code: Code
    convertFromStandard: "Convertir depuis l'unité standard"
    convertToStandard: 'Convertir en unité standard'
    names: Nom
    symbol: Symbole
  UploadImport:
    _: "Historique d'import"
    _notFound: "Historique de l'import non trouvé"
    errors: "Erreurs"
    details: "Détails de l'import"
    row: 'Ligne'
    rowStatus:
      _imported: 'Nombre de lignes importées'
      _succeeded: 'Nombre de lignes importés avec succès'
      _failed: 'Nombre de lignes contenant des erreurs'
    userEmail: "Email de l'utilisateur"
  UploadExport:
    _: Historique d'export
    _error: 'Export échoué!'
    _success: 'Export terminé!'
    _start: 'Export commencé'
    userEmail: Email de l'utilisateur
  User:
    _: Utilisateurs
    _create: Créer
    _created: "L'utilisateur a bien été créé !"
    _deleted: 'L''utilisateur "{{label}}" a bien été supprimée.'
    _membershipsAreUseless: "Cet utilisateur est super-administrateur, il n'est donc pas nécessaire de gérer ses groupes."
    _saved: "L'utilisateur a bien été modifié !"
    _singular: Utilisateur
    avatar: Avatar
    lastLogin: 'Dernière connexion'
    names: Nom
    sso: SSO
    status: Activé
    userGroups: "Groupes d'utilisateur"
  UserGroup:
    _: "Groupes d'utilisateurs"
    _attribute_groups_saved: "Groupes d'attributs enregistré avec succès !"
    _create: Créer
    _created: "Groupe d'utilisateurs créé avec succès !"
    _delete: 'Êtes-vous sûr de vouloir supprimer le groupe "{{label}}" ?'
    _deleted: 'Le groupe "{{label}}" a bien été supprimée.'
    _notFound: "Groupe d'utilisateur non trouvé"
    _route: Groupe
    _saved: "Groupe d'utilisateur enregistrée avec succès !"
    _singular: "Groupe d'utilisateurs"
    _users_saved: 'Utilisateurs enregistrés avec succès !'
    none: "Sans groupe d'utilisateur"
  UserGroupMembership:
    _: 'Demandes en attente'
    _create: Créer
    _created: 'Demande créée avec succès !'
    _singular: 'Demande en attente'
  UserGroupMembershipRequest:
    _: 'Demandes en attente'
    _add: 'Rejoindre un groupe'
    _create: Créer
    _created: 'Demande créée avec succès !'
    _singular: 'Demande en attente'
    accept: "J'accepte les conditions d'utilisation"
    avatar: Avatar
    createdAt: 'Crée le'
    email: 'Adresse mail'
    group: Groupe
    lastname: Nom
    message: Message
    message_help: "Indiquez aux administrateurs du groupe toutes les informations nécessaires à la validation de votre demande, telles que votre identité, l'équipe à laquelle vous appartenez, votre rôle dans l'entreprise, etc."
    userGroup: "Groupe d'utilisateurs"
  UserGroupMembershipRequestSummary:
    _: 'Demandes en attente'
    _singular: 'Demande en attente'
  UserLogin:
    _: 'Statistiques utilisateurs'
    count: 'Nombre de connexions'
    email: 'Adresse e-mail'
    from: De
    role: Rôle
    to: à
  Workflow:
    _: Workflows
    _create: Créer
    _created: 'Workflow créée avec succès !'
    _delete: 'Êtes-vous sûr de vouloir supprimer le workflow "{{label}}" ?'
    _deleted: 'Le workflow "{{label}}" a bien été supprimée.'
    _notFound: 'Workflow non trouvée'
    _saved: 'Workflow enregistrée avec succès !'
    _singular: Workflow
    descriptions: Description
    initial_status: 'Status initial:'
    names: Nom
    reached_status: 'Status atteint:'
    rules: Règles
    statuses: Nom
  WorkflowStep:
    _: Étapes
    _create: Créer
    _deleted: "L'étape a bien été supprimée."
    _singular: Étape
    add: 'Créer une étape'
    add_rule: 'Créer une règle'
    add_ruleGroup: 'Ajouter un groupe de règle'
    manageRuleGroups: 'Gérer les groupes de règles'
    names: Nom
    ruleGroupsInWorkflow: 'Groupes de règles dans le workflow'
    ruleGroupsNotInWorkflow: 'Groupes de règles'
    statuses: Nom
    subtitle: ''
  WorkflowTest:
    _: Tests
    _singular: Test
    status:
      _error: Erreur
      _failed: Échec
      _invalid: Invalide
      _missing: Manquant
      _skipped: Ignoré
      _succeeded: Succès
login:
  email: 'Continuer avec votre e-mail'
  error: 'Une erreur est survenue lors de la récupération des données'
  forget_password: 'Mot de passe oublié ? Cliquez'
  forget_password_button: 'Recevoir mon lien'
  forget_password_text: 'Veuillez entrer votre e-mail pour rechercher votre compte'
  forget_password_title: 'Mot de passe oublié'
  here: ici
  login: 'Se connecter'
  note: "N'utilisez pas ce formulaire si vous possédez un compte SSO."
  password-changed: 'Votre mot de passe à été changé avec succès'
  reset: 'Réinitialiser le mot de passe'
  reset-tagline: 'Saisissez le nouveau mot de passe pour votre compte SINFIN DXP'
  sso: 'Continuer avec'
  sso_login: 'Continuer avec Google'
  title: 'Connectez-vous à votre espace de travail SINFIN'
  token_expired: 'Votre token a expiré'
  welcome: 'Bienvenue sur SINFIN DXP'
  welcome_back: 'Bienvenue dans votre espace de travail'
lvmh:
  dash:
    scoped_not_scoped: 'Cet attribut est scopable, mais les statistiques associées ne le sont pas, ce qui peut entraîner des valeurs incohérentes ou trompeuses.'
    status:
      attribute_error: 'Attributs obligatoires - erreurs'
      blocker_product: 'Produits - erreurs bloquantes'
      empty_attribute: 'Attributs obligatoires - vides'
      product_without_blocker: 'Produits valides - avec erreurs non bloquantes'
      valid_attribute: 'Attributs obligatoires OK'
      valid_product: 'Produits valides'
    title:
      attributes_by_status: "Nombre d'attributs par statut"
      attributes_by_typologie: 'Attributs obligatoires manquants par typologie : '
      products_by_completion: 'Nombre de produits par complétion'
onboarding:
  settings: Paramètres
  page: Page
  card: Carte
  configure_page: Configurer page
  configure_card: Configurer carte
profile:
  manage:
    password: 'Mon mot de passe'
    title: 'Mon profil'
rule_editor:
  _: 'Editeur de règle'
  _see_options: 'Voir les options'
  _write_rule_editor: "Écrire dans l'éditeur"
rules:
  deleteConfirmation: 'Supprimer une règle'
  deleteConfirmationMessage: 'Êtes-vous sûr de vouloir supprimer cette règle ?'
  form:
    open_rule_editor: "Ouvrir l'éditeur"
    rule_placeholder: 'Écrivez votre règle ici'
    rule_preview: 'Tester la règle'
    rule_preview_action: Test
    rule_preview_placeholder: SKU
    see_rules: 'Voir les règles'
    stop_here_if_error: "{{name}} est une étape bloquante, arréter ici si une erreur s'est produite"
    tabs:
      groupInformations: 'Information du groupe'
      ruleGroup: 'Groupes de règles'
  rule: Règle
  status:
    blocker: Bloquante
    high: Haute
    low: Basse
    medium: Moyenne
  types:
    error: Erreur
    missing: Manquant
settings:
  insufficient_rights: "Vous n'avez pas les droits requis pour modifier ces informations."
  userGroups:
    AttributesInGroup: "Groupe d'attributs associés au groupe d'utilisateurs"
    AttributesNotInGroup: "Groupe d'attributs"
    UsersInGroup: 'Utilisateurs dans le groupe'
    UsersNotInGroup: 'Utilisateurs hors du groupe'
    add: 'Ajouter un groupe'
    addGroupFormSubitle: 'Veuillez compléter les champs ci-dessous pour créer votre groupe.'
    addGroupFormTitle: 'Ajouter un groupe'
    admin: Administrateur
    catalogs: Catalogues
    color: Couleur
    delete:
      confirmText: 'Si vous supprimez ce groupe, il ne sera plus accessible. Si vous ne souhaitez pas le supprimer, annulez la suppression en cliquant sur Annuler.'
      confirmTitle: 'Êtes-vous sûr de vouloir supprimer ce groupe ?'
      success: "Groupe d'utilisateurs supprimé avec succès"
    editGroupFormTitle: 'Modifier le groupe'
    form:
      groupInformations:
        attributeGroups: "Groupe d'attribut"
        catalogs: Catalogs
        color: Couleur
        name: 'Nom du groupe'
        users: Utilisateurs
      tabs:
        attributesGroups: "Groupe d'attributs"
        catalogs: Catalogues
        groupInformations: 'Informations du groupe'
        users: Utilisateurs
    manageGroup: 'Gérer le groupe'
    manageUser: 'Gérer les utilisateurs'
    manager: Manager
    member: Membre
    name: Nom
    noResult: 'Aucun groupe'
    role: Rôle
    scopes: "Groupes d'attributs"
    seeGroups: Groupes
    seeUser: Utilisateur
    seeUsers: Utilisateurs
    subTitle: 'Gestion de vos groupes (création / modification / suppression)'
    title: "Gérer les groupes d'utilisateurs"
    userGroup: "Groupe d'utilisateurs"
    users: Utilisateurs
  users:
    form:
      inputs:
        userGroup: "Groupe d'utilisateur"
        userGroups: "Groupes d'utilisateur"
        user_group: "Groupe d'utilisateur"
    user: Utilisateur
status:
  active: Actif
  all: 'Tous les statuts'
  inactive: Inactif
welcome:
  message: "Votre compte ne semble pas encore configuré, ce qui empêche l'accès aux données de la plateforme. Pour y remédier, vous pouvez demander à rejoindre un groupe. Votre demande sera ensuite examinée par les administrateurs du groupe pour validation."
  pending_request_message: "Vous avez demandé à rejoindre <strong>{{group}}</strong> le <strong>{{date}}</strong>. Votre demande a bien été communiquée aux administrateurs du groupe. Vous recevrez une confirmation par email dès qu'elle sera acceptée."
  pending_requests_title: 'Demande en attente ({{count}})'
  title: 'Bienvenue sur Sinfin !'
