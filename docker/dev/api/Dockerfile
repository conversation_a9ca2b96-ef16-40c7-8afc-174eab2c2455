FROM ubuntu:22.04

RUN apt-get update -yq \
    && apt-get upgrade -yq \
    && DEBIAN_FRONTEND=noninteractive apt-get install --no-install-recommends -yq \
        software-properties-common gpg-agent make \
        nano supervisor git ssh curl zip unzip \
        mysql-client ghostscript imagemagick ffmpeg \
    && apt-get autoremove \
    && apt-get autoclean \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

RUN add-apt-repository -yn ppa:ondrej/php \
    && apt-get update -yq \
    && apt-get upgrade -yq \
    && curl -1sLf 'https://dl.cloudsmith.io/public/symfony/stable/setup.deb.sh' | bash \
    && DEBIAN_FRONTEND=noninteractive apt-get install --no-install-recommends -yq \
        php8.4 php8.4-cli \
        php8.4-dom php8.4-xml php8.4-xdebug \
        php8.4-bz2 php8.4-mbstring php8.4-intl \
        php8.4-curl php8.4-redis php8.4-mysql php8.4-tidy \
        php8.4-apcu php8.4-zip php8.4-pdo php8.4-ssh2 \
        php8.4-soap php8.4-bcmath php8.4-gd php8.4-excimer php8.4-imap \
        symfony-cli \
    && apt-get autoremove \
    && apt-get autoclean \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

COPY --from=composer:latest /usr/bin/composer /usr/local/bin/composer

COPY docker /

WORKDIR /var/www

RUN phpenmod -v ALL -s ALL api \
    && chmod +x /usr/bin/image_*

ENV NVM_DIR /usr/local/nvm
ENV NODE_VERSION v20.13.1

RUN mkdir -p $NVM_DIR \
    && curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash \
    && . $NVM_DIR/nvm.sh \
    && nvm install $NODE_VERSION \
    && nvm alias default $NODE_VERSION \
    && nvm use default \
    && chmod +x /usr/bin/image_*

ENV NODE_PATH $NVM_DIR/$NODE_VERSION/lib/node_modules
ENV PATH $NVM_INSTALL_PATH/bin:$NVM_DIR/versions/node/$NODE_VERSION/bin:$PATH

WORKDIR /var/www

EXPOSE 8000
EXPOSE 3000

ENTRYPOINT [ "image_start" ]

HEALTHCHECK --timeout=60s CMD curl --silent --fail "http://127.0.0.1:8000/api/ping"
